# services

create service: https://sms.remmittance.com/api/services
body: {
"name": "Massage",
"description":"This is the service for our Massage"
}
response: {
"extend": {
"message": "Service created successfully",
"service": {
"created_at": "2025-10-07 15:10:01",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Massage",
"is_active": true,
"name": "Massage",
"service_id": "2ff06b2f-6ccb-412c-8931-17e1e4bae0e7",
"updated_at": "2025-10-07 15:10:01"
}
},
"msg": "Success!"
}

get service by id: https://sms.remmittance.com/api/services/2ff06b2f-6ccb-412c-8931-17e1e4bae0e7
response: {
"extend": {
"service": {
"created_at": "2025-10-07 15:10:01",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Massage",
"is_active": true,
"name": "Massage",
"service_id": "2ff06b2f-6ccb-412c-8931-17e1e4bae0e7",
"updated_at": "2025-10-07 15:10:01"
}
},
"msg": "Success!"
}

get services: https://sms.remmittance.com/api/services
response:{
"extend": {
"count": 1,
"services": [
{
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
}
]
},
"msg": "Success!"
}
patch/update the service: https://sms.remmittance.com/api/services/2ff06b2f-6ccb-412c-8931-17e1e4bae0e7
body: {
"name": "Massage",
"description":"This is the service for our Massage"
}
response: {
"extend": {
"message": "Service updated successfully",
"service": {
"created_at": "2025-10-07 15:10:01",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "I am testing the updates",
"is_active": true,
"name": "Massage",
"service_id": "2ff06b2f-6ccb-412c-8931-17e1e4bae0e7",
"updated_at": "2025-10-07 17:13:03"
}
},
"msg": "Success!"
}
delete the service: https://sms.remmittance.com/api/services/2ff06b2f-6ccb-412c-8931-17e1e4bae0e7
response: {
"extend": {
"message": "Service deactivated successfully"
},
"msg": "Success!"
}

# service prices

create service price: https://sms.remmittance.com/api/service-prices
body: {
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"price_amount":15000
}
response: {

"extend": {
"message": "Service price created successfully",
"service_price": {
"created_at": "2025-10-07 17:24:39",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 15000,
"price_id": "a973bd35-3f82-40e3-a8a4-46292d4bd339",
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 17:24:39"
}
},
"msg": "Success!"
}

get service price by id: https://sms.remmittance.com/api/service-prices/a973bd35-3f82-40e3-a8a4-46292d4bd339
response: {

"extend": {
"service_price": {
"created_at": "2025-10-07 17:24:39",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 15000,
"price_id": "a973bd35-3f82-40e3-a8a4-46292d4bd339",
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 17:24:39"
}
},
"msg": "Success!"
}

get service prices: https://sms.remmittance.com/api/service-prices
response: {

"extend": {
"count": 1,
"service_prices": [
{
"created_at": "2025-10-07 17:24:39",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 15000,
"price_id": "a973bd35-3f82-40e3-a8a4-46292d4bd339",
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 17:24:39"
}
]
},
"msg": "Success!"
}

update service price: https://sms.remmittance.com/api/service-prices/a973bd35-3f82-40e3-a8a4-46292d4bd339
body: {
"effective_from": "2025-10-30",
"effective_to": "2025-11-30"
}
response: {
"extend": {
"message": "Service price updated successfully",
"service_price": {
"created_at": "2025-10-07 17:24:39",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-30",
"effective_to": "2025-11-30",
"price_amount": 15000,
"price_id": "a973bd35-3f82-40e3-a8a4-46292d4bd339",
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 17:55:21"
}
},
"msg": "Success!"
}

get current price of the service given service id: https://sms.remmittance.com/api/services/4abb67eb-4977-4815-ba99-3cb7504cde40/current-price

response: {
"extend": {
"current_price": {
"created_at": "2025-10-07 18:02:56",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 25000,
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:56"
}
},
"msg": "Success!"
}

# storage location

create storage location: https://sms.remmittance.com/api/storage-locations
body: {
"location_number": "1"
}
response: {

"extend": {
"message": "Storage location created successfully",
"storage_location": {
"created_at": "2025-10-07 18:21:37",
"is_available": true,
"location_id": "c40c51bd-5eb5-49b8-a973-174b6b067786",
"location_number": "1",
"notes": null,
"updated_at": "2025-10-07 18:21:37"
}
},
"msg": "Success!"
}

get location by id: https://sms.remmittance.com/api/storage-locations/c40c51bd-5eb5-49b8-a973-174b6b067786
response: {

"extend": {
"storage_location": {
"created_at": "2025-10-07 18:21:37",
"is_available": true,
"location_id": "c40c51bd-5eb5-49b8-a973-174b6b067786",
"location_number": "1",
"notes": null,
"updated_at": "2025-10-07 18:21:37"
}
},
"msg": "Success!"
}

get storage locations:
available lockers: https://sms.remmittance.com/api/storage-locations?available_only=true
default behavior: https://sms.remmittance.com/api/storage-locations
taken lockers: https://sms.remmittance.com/api/storage-locations?available_only=false

response: {

"extend": {
"count": 1,
"storage_locations": [
{
"created_at": "2025-10-07 18:21:37",
"is_available": true,
"location_id": "c40c51bd-5eb5-49b8-a973-174b6b067786",
"location_number": "1",
"notes": null,
"updated_at": "2025-10-07 18:21:37"
}
]
},
"msg": "Success!"
}
update storage location: https://sms.remmittance.com/api/storage-locations/c40c51bd-5eb5-49b8-a973-174b6b067786
body: {
"notes": "Updating this"
}
response: {

"extend": {
"message": "Storage location updated successfully",
"storage_location": {
"created_at": "2025-10-07 18:21:37",
"is_available": true,
"location_id": "c40c51bd-5eb5-49b8-a973-174b6b067786",
"location_number": "1",
"notes": "Updating this",
"updated_at": "2025-10-07 19:11:24"
}
},
"msg": "Success!"
}

delete storage location: https://sms.remmittance.com/api/storage-locations/c40c51bd-5eb5-49b8-a973-174b6b067786
response: {

"extend": {
"message": "Storage location deleted successfully"
},
"msg": "Success!"
}

# customer storage

store customer belongings[POST] : https://sms.remmittance.com//api/customer-storages
body: {
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"location_id": "300bdc98-2145-4a08-8e17-e012820d16fb",
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996",
"items_description": "The customer has a laptop, a water bottle and a pen inside the bag. The bag is grey"
}
success response: {

"extend": {
"customer_storage": {
"created_at": "2025-10-07 19:58:10",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"items_description": "The customer has a laptop, a water bottle and a pen inside the bag. The bag is grey",
"location": {
"created_at": "2025-10-07 19:57:29",
"is_available": false,
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"location_number": "2",
"notes": null,
"updated_at": "2025-10-07 19:58:10"
},
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"notes": null,
"retrieved_at": null,
"status": "stored",
"storage_id": "eedd149e-8e2e-4243-b5cc-0523d2067721",
"stored_at": "2025-10-07 19:58:10",
"updated_at": "2025-10-07 19:58:10",
"visit": {
"created_at": "2025-10-07 07:17:08",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"device_serial_num": "AYTI10087992",
"duration_minutes": null,
"is_loyalty_visit": true,
"loyalty_points_earned": 0,
"notes": null,
"redemption_id": null,
"reward_redeemed": false,
"source": "biometric",
"source_record_id": "49",
"updated_at": "2025-10-07 07:17:08",
"visit_date": "2025-10-07",
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996",
"visit_time": "2025-10-07 07:17:05",
"visit_type": "regular"
},
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996"
},
"message": "Customer storage created successfully"
},
"msg": "Success!"
}

storage taken response: {
"message": "Storage location is not available"
}

get customer item by storage id: https://sms.remmittance.com/api/customer-storages/eedd149e-8e2e-4243-b5cc-0523d2067721
response: {

"extend": {
"customer_storage": {
"created_at": "2025-10-07 19:58:10",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"items_description": "The customer has a laptop, a water bottle and a pen inside the bag. The bag is grey",
"location": {
"created_at": "2025-10-07 19:57:29",
"is_available": false,
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"location_number": "2",
"notes": null,
"updated_at": "2025-10-07 19:58:10"
},
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"notes": null,
"retrieved_at": null,
"status": "stored",
"storage_id": "eedd149e-8e2e-4243-b5cc-0523d2067721",
"stored_at": "2025-10-07 19:58:10",
"updated_at": "2025-10-07 19:58:10",
"visit": {
"created_at": "2025-10-07 07:17:08",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"device_serial_num": "AYTI10087992",
"duration_minutes": null,
"is_loyalty_visit": true,
"loyalty_points_earned": 0,
"notes": null,
"redemption_id": null,
"reward_redeemed": false,
"source": "biometric",
"source_record_id": "49",
"updated_at": "2025-10-07 07:17:08",
"visit_date": "2025-10-07",
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996",
"visit_time": "2025-10-07 07:17:05",
"visit_type": "regular"
},
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996"
}
},
"msg": "Success!"
}

Get all customer storages with params: https://sms.remmittance.com//api/customer-storages?customer_id=98bbdab9-d73e-404d-b78b-af038f8e5c18&location_id=aa828f25-3fc9-493d-98db-b5c651b2aa5d&status=stored&visit_id=55541bc8-7b03-436f-9f91-b1273c16a996
no params: https://sms.remmittance.com//api/customer-storages
response: {

"extend": {
"count": 1,
"customer_storages": [
{
"created_at": "2025-10-07 19:58:10",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"items_description": "The customer has a laptop, a water bottle and a pen inside the bag. The bag is grey",
"location": {
"created_at": "2025-10-07 19:57:29",
"is_available": false,
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"location_number": "2",
"notes": null,
"updated_at": "2025-10-07 19:58:10"
},
"location_id": "aa828f25-3fc9-493d-98db-b5c651b2aa5d",
"notes": null,
"retrieved_at": null,
"status": "stored",
"storage_id": "eedd149e-8e2e-4243-b5cc-0523d2067721",
"stored_at": "2025-10-07 19:58:10",
"updated_at": "2025-10-07 19:58:10",
"visit": {
"created_at": "2025-10-07 07:17:08",
"customer": {
"created_at": "2025-10-07 06:31:42",
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"customer_segment": "regular",
"date_of_birth": null,
"email": "<EMAIL>",
"first_name": "Brian",
"full_name": "Brian Shema",
"last_name": "Shema",
"marketing_consent": true,
"membership_number": "12",
"notes": "This is our regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:31:42"
},
"customer_id": "98bbdab9-d73e-404d-b78b-af038f8e5c18",
"device_serial_num": "AYTI10087992",
"duration_minutes": null,
"is_loyalty_visit": true,
"loyalty_points_earned": 0,
"notes": null,
"redemption_id": null,
"reward_redeemed": false,
"source": "biometric",
"source_record_id": "49",
"updated_at": "2025-10-07 07:17:08",
"visit_date": "2025-10-07",
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996",
"visit_time": "2025-10-07 07:17:05",
"visit_type": "regular"
},
"visit_id": "55541bc8-7b03-436f-9f91-b1273c16a996"
}
]
},
"msg": "Success!"
}

Mark csutomer items are retrieved: https://sms.remmittance.com/api/customer-storages/eedd149e-8e2e-4243-b5cc-0523d2067721/retrieve

body: {
"retrieved_at":"2025-10-07 14:30:00"
}
response: {

"extend": {
"message": "Items retrieved successfully"
},
"msg": "Success!"
}
mark items as abandoned post: https://sms.remmittance.com/api/customer-storages/a477ac78-3e7b-4027-b24a-6620846fd0f8/abandon
body: {
"retrieved_at":"2025-10-07 14:30:00"
}
response: {

"extend": {
"message": "Items marked as abandoned"
},
"msg": "Success!"
}

# service consumption:

create service consumption: https://sms.remmittance.com//api/service-consumptions/bulk
body: {
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b",
"consumed_at": "2025-10-07 14:30:00",
"services": [
{
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"quantity": 2,
"notes": "Extended sauna session"
},
{
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"quantity": 1,
"notes": "Deep tissue massage"
}
]
}
response: {

"extend": {
"bulk_consumption": {
"consumed_at": "2025-10-07T14:30:00",
"consumptions": [
{
"consumed_at": "2025-10-07 14:30:00",
"consumption_id": "25fa61d8-7fff-47c3-9195-70fb8f7be136",
"created_at": "2025-10-07 21:28:08",
"customer": {
"created_at": "2025-10-07 06:57:15",
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"customer_segment": "regular",
"date_of_birth": null,
"email": null,
"first_name": "Shami",
"full_name": "Shami Rwema",
"last_name": "Rwema",
"marketing_consent": false,
"membership_number": "13",
"notes": "This is a regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:57:15"
},
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"notes": "Extended sauna session",
"price": {
"created_at": "2025-10-07 21:27:51",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 30000,
"price_id": "cea81797-3100-4704-bdcc-a255c378681c",
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 21:27:51"
},
"price_id": "cea81797-3100-4704-bdcc-a255c378681c",
"quantity": 2,
"service": {
"created_at": "2025-10-07 15:04:10",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "This is the service for our Sauna",
"is_active": true,
"name": "sauna",
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"updated_at": "2025-10-07 15:04:10"
},
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"total_amount": 60000,
"updated_at": "2025-10-07 21:28:08",
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b"
},
{
"consumed_at": "2025-10-07 14:30:00",
"consumption_id": "d7e3af0e-dc40-41e4-91a9-081d513187f8",
"created_at": "2025-10-07 21:28:08",
"customer": {
"created_at": "2025-10-07 06:57:15",
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"customer_segment": "regular",
"date_of_birth": null,
"email": null,
"first_name": "Shami",
"full_name": "Shami Rwema",
"last_name": "Rwema",
"marketing_consent": false,
"membership_number": "13",
"notes": "This is a regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:57:15"
},
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"notes": "Deep tissue massage",
"price": {
"created_at": "2025-10-07 18:02:56",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 25000,
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:56"
},
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"quantity": 1,
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"total_amount": 25000,
"updated_at": "2025-10-07 21:28:08",
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b"
}
],
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"success": true,
"summary": {
"services": [
{
"consumption_id": "25fa61d8-7fff-47c3-9195-70fb8f7be136",
"quantity": 2,
"service_id": "2592daa1-c7a3-4eba-9f57-ba292856b29b",
"service_name": "sauna",
"total_price": 60000,
"unit_price": 30000
},
{
"consumption_id": "d7e3af0e-dc40-41e4-91a9-081d513187f8",
"quantity": 1,
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"service_name": "scrubbing",
"total_price": 25000,
"unit_price": 25000
}
],
"total_amount": 85000,
"total_services": 2
},
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b"
},
"message": "Successfully created 2 service consumption records"
},
"msg": "Success!"
}
get service consumption by consumption_id: https://sms.remmittance.com//api/service-consumptions/d7e3af0e-dc40-41e4-91a9-081d513187f8
response: {

"extend": {
"service_consumption": {
"consumed_at": "2025-10-07 14:30:00",
"consumption_id": "d7e3af0e-dc40-41e4-91a9-081d513187f8",
"created_at": "2025-10-07 21:28:08",
"customer": {
"created_at": "2025-10-07 06:57:15",
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"customer_segment": "regular",
"date_of_birth": null,
"email": null,
"first_name": "Shami",
"full_name": "Shami Rwema",
"last_name": "Rwema",
"marketing_consent": false,
"membership_number": "13",
"notes": "This is a regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:57:15"
},
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"notes": "Deep tissue massage",
"price": {
"created_at": "2025-10-07 18:02:56",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 25000,
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:56"
},
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"quantity": 1,
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"total_amount": 25000,
"updated_at": "2025-10-07 21:28:08",
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b"
}
},
"msg": "Success!"
}

get all service consumptions: https://sms.remmittance.com//api/service-consumptions
with optional params: https://sms.remmittance.com//api/service-consumptions?customer_id=dc690877-c777-420f-a248-50c7b8577c99&service_id=4abb67eb-4977-4815-ba99-3cb7504cde40& visit_id=286d250e-a066-4b76-9291-01b905c0300b&start_date=2025-09-01&end_date=2025-10-30

response: {

"extend": {
"count": 1,
"service_consumptions": [
{
"consumed_at": "2025-10-07 14:30:00",
"consumption_id": "d7e3af0e-dc40-41e4-91a9-081d513187f8",
"created_at": "2025-10-07 21:28:08",
"customer": {
"created_at": "2025-10-07 06:57:15",
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"customer_segment": "regular",
"date_of_birth": null,
"email": null,
"first_name": "Shami",
"full_name": "Shami Rwema",
"last_name": "Rwema",
"marketing_consent": false,
"membership_number": "13",
"notes": "This is a regular customer",
"phone_number": null,
"preferred_contact_method": "email",
"registration_date": "2025-10-07",
"status": "active",
"updated_at": "2025-10-07 06:57:15"
},
"customer_id": "dc690877-c777-420f-a248-50c7b8577c99",
"notes": "Deep tissue massage",
"price": {
"created_at": "2025-10-07 18:02:56",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"currency": "RWF",
"effective_from": "2025-10-07",
"effective_to": null,
"price_amount": 25000,
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:56"
},
"price_id": "54d33f3f-b308-4efc-9a94-4ee103970106",
"quantity": 1,
"service": {
"created_at": "2025-10-07 18:02:15",
"created_by": "e08456d5-a418-4772-a285-583795b16314",
"description": "Scrubbing service",
"is_active": true,
"name": "scrubbing",
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"updated_at": "2025-10-07 18:02:15"
},
"service_id": "4abb67eb-4977-4815-ba99-3cb7504cde40",
"total_amount": 25000,
"updated_at": "2025-10-07 21:28:08",
"visit_id": "286d250e-a066-4b76-9291-01b905c0300b"
}
]
},
"msg": "Success!"
}

get consumption total: https://sms.remmittance.com//api/service-consumptions/total?customer_id=dc690877-c777-420f-a248-50c7b8577c99&service_id=4abb67eb-4977-4815-ba99-3cb7504cde40& visit_id=286d250e-a066-4b76-9291-01b905c0300b&start_date=2025-09-01&end_date=2025-10-30

response: {

"extend": {
"currency": "USD",
"total_amount": 85000
},
"msg": "Success!"
}
