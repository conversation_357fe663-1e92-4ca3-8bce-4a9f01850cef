/**
 * Storage Location Management Types
 * Types for storage location functionality (lockers, storage areas, etc.)
 */

// Base StorageLocation interface
export interface StorageLocation {
  location_id: string;
  location_number: string;
  notes?: string | null;
  is_available: boolean;
  created_at: string;
  updated_at: string;
}

// Storage location creation request
export interface CreateStorageLocationRequest {
  location_number: string;
  notes?: string;
}

// Storage location update request
export interface UpdateStorageLocationRequest {
  notes?: string;
}

// API Response types
export interface StorageLocationResponse {
  extend: {
    message: string;
    storage_location: StorageLocation;
  };
  msg: string;
}

export interface StorageLocationsListResponse {
  extend: {
    count: number;
    storage_locations: StorageLocation[];
  };
  msg: string;
}

export interface SingleStorageLocationResponse {
  extend: {
    storage_location: StorageLocation;
  };
  msg: string;
}

export interface DeleteStorageLocationResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Storage location filters for listing
export interface StorageLocationFilters {
  is_available?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface StorageLocationFormData {
  location_number: string;
  notes: string;
}

// Utility functions
export const formatLocationNumber = (location: StorageLocation): string => {
  return location.location_number;
};

export const getAvailabilityLabel = (isAvailable: boolean): string => {
  return isAvailable ? "Available" : "Unavailable";
};

export const getAvailabilityColor = (isAvailable: boolean): string => {
  return isAvailable
    ? "bg-green-100 text-green-800"
    : "bg-red-100 text-red-800";
};

export const isLocationActive = (location: StorageLocation): boolean => {
  return location.is_available;
};

export const validateLocationNumber = (number: string): string | null => {
  if (!number || number.trim().length === 0) {
    return "Location number is required";
  }

  if (number.trim().length < 1) {
    return "Location number must be at least 1 character long";
  }

  if (number.trim().length > 50) {
    return "Location number must be less than 50 characters";
  }

  return null;
};

export const validateNotes = (notes: string): string | null => {
  if (notes && notes.length > 500) {
    return "Notes must be less than 500 characters";
  }

  return null;
};

export const validateStorageLocationForm = (
  formData: StorageLocationFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  const numberError = validateLocationNumber(formData.location_number);
  if (numberError) {
    errors.location_number = numberError;
  }

  const notesError = validateNotes(formData.notes);
  if (notesError) {
    errors.notes = notesError;
  }

  return errors;
};

export const getDefaultStorageLocationFormData =
  (): StorageLocationFormData => ({
    location_number: "",
    notes: "",
  });

// Storage location sorting options
export const STORAGE_LOCATION_SORT_OPTIONS = [
  { value: "number_asc", label: "Number (A-Z)" },
  { value: "number_desc", label: "Number (Z-A)" },
  { value: "available_first", label: "Available First" },
  { value: "unavailable_first", label: "Unavailable First" },
  { value: "created_desc", label: "Newest First" },
  { value: "created_asc", label: "Oldest First" },
] as const;

export type StorageLocationSortOption =
  (typeof STORAGE_LOCATION_SORT_OPTIONS)[number]["value"];

// Storage location statistics
export interface StorageLocationStatistics {
  total: number;
  available: number;
  unavailable: number;
}

// Bulk operations
export interface BulkUpdateStorageLocationRequest {
  location_ids: string[];
  updates: Partial<UpdateStorageLocationRequest>;
}

// Location search and filtering
export const searchStorageLocations = (
  locations: StorageLocation[],
  searchTerm: string
): StorageLocation[] => {
  if (!searchTerm.trim()) return locations;

  const term = searchTerm.toLowerCase();
  return locations.filter(
    (location) =>
      location.location_number.toLowerCase().includes(term) ||
      (location.notes && location.notes.toLowerCase().includes(term))
  );
};

export const filterStorageLocationsByAvailability = (
  locations: StorageLocation[],
  isAvailable: boolean
): StorageLocation[] => {
  return locations.filter((location) => location.is_available === isAvailable);
};
