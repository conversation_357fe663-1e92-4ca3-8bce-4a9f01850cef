/**
 * Customer Management Types
 * TypeScript interfaces for customer entities and API responses
 */

// Customer segments enum
export type CustomerSegment = 'regular' | 'vip' | 'premium' | 'gold' | 'silver' | 'bronze';

// Preferred contact methods enum
export type PreferredContactMethod = 'email' | 'phone' | 'sms' | 'whatsapp';

// Customer status enum
export type CustomerStatus = 'active' | 'inactive' | 'suspended' | 'blocked';

// Base Customer interface
export interface Customer {
  customer_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email?: string | null;
  phone_number?: string | null;
  membership_number?: string | null;
  customer_segment: CustomerSegment;
  preferred_contact_method?: PreferredContactMethod | null;
  notes?: string | null;
  date_of_birth?: string | null; // ISO date string
  marketing_consent: boolean;
  status: CustomerStatus;
  registration_date: string; // ISO date string
  created_at: string;
  updated_at: string;
  // Additional fields from get customer by id response
  available_rewards?: number;
  visit_count?: number;
}

// Customer creation request interface
export interface CreateCustomerRequest {
  first_name: string;
  last_name: string;
  email?: string;
  phone?: string;
  membership_number?: string;
  customer_segment?: CustomerSegment;
  preferred_contact_method?: PreferredContactMethod;
  notes?: string;
  date_of_birth?: string; // ISO date string
  marketing_consent?: boolean;
}

// Customer update request interface
export interface UpdateCustomerRequest {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone_number?: string;
  membership_number?: string;
  customer_segment?: CustomerSegment;
  preferred_contact_method?: PreferredContactMethod;
  notes?: string;
  date_of_birth?: string; // ISO date string
  marketing_consent?: boolean;
  status?: CustomerStatus;
}

// Device sync information
export interface DeviceSync {
  devices_synced: number;
  message: string;
  status: 'success' | 'error' | 'partial';
}

// Customer API response for single customer operations (create, update, get by id)
export interface CustomerResponse {
  extend: {
    customer: Customer;
    device_sync?: DeviceSync;
    message?: string;
  };
  msg: string;
  code?: number;
}

// Pagination interface
export interface Pagination {
  has_next: boolean;
  has_prev: boolean;
  page: number;
  pages: number;
  per_page: number;
  total_count: number;
}

// Customer list API response
export interface CustomersListResponse {
  code: number;
  extend: {
    customers: Customer[];
    pagination: Pagination;
  };
  msg: string;
}

// Customer statistics interface
export interface CustomerStatistics {
  customer_id: string;
  customer: Customer;
  start_date?: string | null;
  end_date?: string | null;
  total_visits: number;
  regular_visits: number;
  loyalty_visits: number;
  reward_visits: number;
  complimentary_visits: number;
  biometric_visits: number;
  manual_visits: number;
}

// Customer statistics API response
export interface CustomerStatisticsResponse {
  extend: {
    statistics: CustomerStatistics;
  };
  msg: string;
  code?: number;
}

// Customer form data interface for forms
export interface CustomerFormData {
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  membership_number: string;
  customer_segment: CustomerSegment;
  preferred_contact_method: PreferredContactMethod;
  notes: string;
  date_of_birth: string;
  marketing_consent: boolean;
  status?: CustomerStatus;
}

// Customer list filters interface
export interface CustomerFilters {
  segment?: CustomerSegment;
  status?: CustomerStatus;
  search?: string;
  page?: number;
  per_page?: number;
}

// Customer segment options for dropdowns
export const CUSTOMER_SEGMENTS: { value: CustomerSegment; label: string }[] = [
  { value: 'regular', label: 'Regular' },
  { value: 'vip', label: 'VIP' },
  { value: 'premium', label: 'Premium' },
  { value: 'gold', label: 'Gold' },
  { value: 'silver', label: 'Silver' },
  { value: 'bronze', label: 'Bronze' },
];

// Contact method options for dropdowns
export const CONTACT_METHODS: { value: PreferredContactMethod; label: string }[] = [
  { value: 'email', label: 'Email' },
  { value: 'phone', label: 'Phone' },
  { value: 'sms', label: 'SMS' },
  { value: 'whatsapp', label: 'WhatsApp' },
];

// Customer status options for dropdowns
export const CUSTOMER_STATUSES: { value: CustomerStatus; label: string }[] = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'suspended', label: 'Suspended' },
  { value: 'blocked', label: 'Blocked' },
];

// Helper function to get customer segment display name
export const getCustomerSegmentLabel = (segment: CustomerSegment): string => {
  const segmentOption = CUSTOMER_SEGMENTS.find(s => s.value === segment);
  return segmentOption ? segmentOption.label : segment;
};

// Helper function to get contact method display name
export const getContactMethodLabel = (method: PreferredContactMethod): string => {
  const methodOption = CONTACT_METHODS.find(m => m.value === method);
  return methodOption ? methodOption.label : method;
};

// Helper function to get customer status display name
export const getCustomerStatusLabel = (status: CustomerStatus): string => {
  const statusOption = CUSTOMER_STATUSES.find(s => s.value === status);
  return statusOption ? statusOption.label : status;
};

// Helper function to format customer full name
export const formatCustomerName = (customer: Customer): string => {
  return customer.full_name || `${customer.first_name} ${customer.last_name}`;
};

// Helper function to get customer status color for UI
export const getCustomerStatusColor = (status: CustomerStatus): string => {
  switch (status) {
    case 'active':
      return 'text-green-600 bg-green-100';
    case 'inactive':
      return 'text-gray-600 bg-gray-100';
    case 'suspended':
      return 'text-yellow-600 bg-yellow-100';
    case 'blocked':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// Helper function to get customer segment color for UI
export const getCustomerSegmentColor = (segment: CustomerSegment): string => {
  switch (segment) {
    case 'vip':
      return 'text-purple-600 bg-purple-100';
    case 'premium':
      return 'text-indigo-600 bg-indigo-100';
    case 'gold':
      return 'text-yellow-600 bg-yellow-100';
    case 'silver':
      return 'text-gray-600 bg-gray-100';
    case 'bronze':
      return 'text-orange-600 bg-orange-100';
    case 'regular':
    default:
      return 'text-blue-600 bg-blue-100';
  }
};
