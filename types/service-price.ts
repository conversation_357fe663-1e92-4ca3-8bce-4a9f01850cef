/**
 * Service Price Management Types
 * Types for service pricing functionality
 */

import { Service } from './service';

// Base ServicePrice interface
export interface ServicePrice {
  price_id: string;
  service_id: string;
  price_amount: number;
  currency: string;
  effective_from: string; // ISO date string
  effective_to?: string | null; // ISO date string
  created_at: string;
  updated_at: string;
  created_by: string;
  service: Service;
}

// Service price creation request
export interface CreateServicePriceRequest {
  service_id: string;
  price_amount: number;
  effective_from?: string; // ISO date string, defaults to today
  effective_to?: string; // ISO date string, optional
}

// Service price update request
export interface UpdateServicePriceRequest {
  price_amount?: number;
  effective_from?: string;
  effective_to?: string;
}

// API Response types
export interface ServicePriceResponse {
  extend: {
    message: string;
    service_price: ServicePrice;
  };
  msg: string;
}

export interface ServicePricesListResponse {
  extend: {
    count: number;
    service_prices: ServicePrice[];
  };
  msg: string;
}

export interface SingleServicePriceResponse {
  extend: {
    service_price: ServicePrice;
  };
  msg: string;
}

export interface CurrentPriceResponse {
  extend: {
    current_price: ServicePrice;
  };
  msg: string;
}

// Service price filters for listing
export interface ServicePriceFilters {
  service_id?: string;
  effective_date?: string; // ISO date string
  is_current?: boolean; // Filter for current prices only
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface ServicePriceFormData {
  service_id: string;
  price_amount: string; // String for form input
  effective_from: string;
  effective_to: string;
}

// Utility functions
export const formatPrice = (price: number, currency: string = 'RWF'): string => {
  return new Intl.NumberFormat('en-RW', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(price);
};

export const formatPriceAmount = (amount: number): string => {
  return amount.toLocaleString();
};

export const isCurrentPrice = (servicePrice: ServicePrice): boolean => {
  const now = new Date();
  const effectiveFrom = new Date(servicePrice.effective_from);
  const effectiveTo = servicePrice.effective_to ? new Date(servicePrice.effective_to) : null;
  
  return effectiveFrom <= now && (!effectiveTo || effectiveTo >= now);
};

export const isPriceExpired = (servicePrice: ServicePrice): boolean => {
  if (!servicePrice.effective_to) return false;
  return new Date(servicePrice.effective_to) < new Date();
};

export const isPriceFuture = (servicePrice: ServicePrice): boolean => {
  return new Date(servicePrice.effective_from) > new Date();
};

export const getPriceStatusLabel = (servicePrice: ServicePrice): string => {
  if (isPriceExpired(servicePrice)) return 'Expired';
  if (isPriceFuture(servicePrice)) return 'Future';
  if (isCurrentPrice(servicePrice)) return 'Current';
  return 'Inactive';
};

export const getPriceStatusColor = (servicePrice: ServicePrice): string => {
  if (isPriceExpired(servicePrice)) return 'bg-red-100 text-red-800';
  if (isPriceFuture(servicePrice)) return 'bg-blue-100 text-blue-800';
  if (isCurrentPrice(servicePrice)) return 'bg-green-100 text-green-800';
  return 'bg-gray-100 text-gray-800';
};

export const validatePriceAmount = (amount: string): string | null => {
  if (!amount || amount.trim().length === 0) {
    return 'Price amount is required';
  }
  
  const numericAmount = parseFloat(amount);
  if (isNaN(numericAmount)) {
    return 'Price amount must be a valid number';
  }
  
  if (numericAmount <= 0) {
    return 'Price amount must be greater than 0';
  }
  
  if (numericAmount > 999999999) {
    return 'Price amount is too large';
  }
  
  return null;
};

export const validateEffectiveDate = (date: string, fieldName: string): string | null => {
  if (!date || date.trim().length === 0) {
    return `${fieldName} is required`;
  }
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return `${fieldName} must be a valid date`;
  }
  
  return null;
};

export const validateDateRange = (fromDate: string, toDate: string): string | null => {
  if (!fromDate || !toDate) return null;
  
  const from = new Date(fromDate);
  const to = new Date(toDate);
  
  if (from >= to) {
    return 'Effective to date must be after effective from date';
  }
  
  return null;
};

export const validateServicePriceForm = (formData: ServicePriceFormData): Record<string, string> => {
  const errors: Record<string, string> = {};
  
  if (!formData.service_id) {
    errors.service_id = 'Service is required';
  }
  
  const amountError = validatePriceAmount(formData.price_amount);
  if (amountError) {
    errors.price_amount = amountError;
  }
  
  const fromDateError = validateEffectiveDate(formData.effective_from, 'Effective from date');
  if (fromDateError) {
    errors.effective_from = fromDateError;
  }
  
  if (formData.effective_to) {
    const toDateError = validateEffectiveDate(formData.effective_to, 'Effective to date');
    if (toDateError) {
      errors.effective_to = toDateError;
    }
  }
  
  if (formData.effective_from && formData.effective_to) {
    const rangeError = validateDateRange(formData.effective_from, formData.effective_to);
    if (rangeError) {
      errors.effective_to = rangeError;
    }
  }
  
  return errors;
};

// Currency constants
export const SUPPORTED_CURRENCIES = [
  { code: 'RWF', name: 'Rwandan Franc', symbol: 'RWF' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
] as const;

export type SupportedCurrency = typeof SUPPORTED_CURRENCIES[number]['code'];

// Default service price form data
export const getDefaultServicePriceFormData = (): ServicePriceFormData => ({
  service_id: '',
  price_amount: '',
  effective_from: new Date().toISOString().split('T')[0], // Today's date
  effective_to: '',
});

// Service price sorting options
export const SERVICE_PRICE_SORT_OPTIONS = [
  { value: 'service_name_asc', label: 'Service Name (A-Z)' },
  { value: 'service_name_desc', label: 'Service Name (Z-A)' },
  { value: 'price_asc', label: 'Price (Low to High)' },
  { value: 'price_desc', label: 'Price (High to Low)' },
  { value: 'effective_from_desc', label: 'Newest First' },
  { value: 'effective_from_asc', label: 'Oldest First' },
  { value: 'status_current', label: 'Current Prices First' },
] as const;

export type ServicePriceSortOption = typeof SERVICE_PRICE_SORT_OPTIONS[number]['value'];
