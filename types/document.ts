/**
 * Document Management Types
 * TypeScript interfaces for document entities and API responses
 */

export interface Document {
  document_id: string;
  document_name: string;
  document_description?: string;
  document_category: string;
  original_filename?: string;
  file_name: string;
  file_type: string;
  mime_type?: string;
  file_size_bytes: number;
  file_size_mb: number;
  storage_provider?: string;
  storage_url?: string;
  download_url?: string;
  file_url?: string;
  expiry_date?: string;
  days_until_expiry?: number;
  is_expired?: boolean;
  uploaded_at: string;
  updated_at?: string;
  uploaded_by: string;
  employee_id?: string;
  employee_name?: string;
  company_id: string;
  folder_id?: string;
  folder_name?: string;
  folder_path?: string;
  access_level?: string;
  is_confidential?: boolean;
  status?: string;
}

export interface DocumentUploadRequest {
  document_name: string;
  document_description?: string;
  document_category: string;
  file: File;
  expiry_date?: string;
  employee_id?: string;
  folder_id?: string;
}

export interface DocumentUploadResponse {
  success: boolean;
  message: string;
  document?: Document;
  error?: string;
}

export interface DocumentListResponse {
  success: boolean;
  documents: Document[];
  pagination?: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
  };
  message?: string;
  error?: string;
}

export interface DocumentDetailsResponse {
  success: boolean;
  document?: Document;
  message?: string;
  error?: string;
}

export interface ExpiringDocumentsResponse {
  success: boolean;
  documents: Document[];
  total_expiring: number;
  message?: string;
  error?: string;
}

export interface DocumentFilters {
  category?: string;
  employee_id?: string;
  search?: string;
  expiry_status?: "all" | "expiring" | "expired" | "valid";
  date_from?: string;
  date_to?: string;
  page?: number;
  limit?: number;
}

export interface DocumentCategory {
  value: string;
  label: string;
  description?: string;
}

export const DOCUMENT_CATEGORIES: DocumentCategory[] = [
  {
    value: "resume",
    label: "Resume/CV",
    description: "Employee resumes and curriculum vitae",
  },
  {
    value: "contract",
    label: "Contract",
    description: "Employment contracts and agreements",
  },
  {
    value: "id_document",
    label: "ID Document",
    description: "Identity documents and passports",
  },
  {
    value: "certificate",
    label: "Certificate",
    description: "Professional certificates and qualifications",
  },
  {
    value: "policy",
    label: "Policy",
    description: "Company policies and procedures",
  },
  {
    value: "training",
    label: "Training",
    description: "Training materials and records",
  },
  {
    value: "performance",
    label: "Performance",
    description: "Performance reviews and evaluations",
  },
  {
    value: "medical",
    label: "Medical",
    description: "Medical certificates and health records",
  },
  {
    value: "legal",
    label: "Legal",
    description: "Legal documents and compliance records",
  },
  {
    value: "other",
    label: "Other",
    description: "Other miscellaneous documents",
  },
];

export const SUPPORTED_FILE_TYPES = [
  "application/pdf",
  "application/msword",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/vnd.ms-excel",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "image/jpeg",
  "image/png",
  "image/gif",
  "text/plain",
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export interface DocumentStats {
  total_documents: number;
  documents_by_category: Record<string, number>;
  expiring_soon: number;
  expired: number;
  total_size: number;
}

// Folder Management Types
export interface Folder {
  folder_id: string;
  folder_name: string;
  description?: string;
  parent_folder_id?: string;
  full_path: string;
  depth: number;
  color?: string;
  icon?: string;
  is_private: boolean;
  allowed_roles: string[];
  document_count: number;
  has_subfolders: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface FolderTreeItem extends Folder {
  documents?: Document[];
  subfolders: FolderTreeItem[];
}

export interface FolderPath {
  folder_id: string;
  folder_name: string;
}

export interface FolderStatistics {
  folder_id: string;
  folder_name: string;
  full_path: string;
  depth: number;
  direct_document_count: number;
  direct_size_bytes: number;
  direct_size_mb: number;
  subfolder_count: number;
  subfolder_document_count: number;
  subfolder_size_bytes: number;
  subfolder_size_mb: number;
  total_document_count: number;
  total_size_bytes: number;
  total_size_mb: number;
  category_breakdown: Record<string, number>;
}

export interface CreateFolderRequest {
  folder_name: string;
  description?: string;
  parent_folder_id?: string;
  color?: string;
  icon?: string;
  is_private?: boolean;
  allowed_roles?: string[];
}

export interface UpdateFolderRequest {
  folder_name?: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface MoveFolderRequest {
  folder_id: string;
}

export interface MoveMultipleDocumentsRequest {
  document_ids: string[];
}

// API Response Types for Folders
export interface FolderResponse {
  success: boolean;
  message?: string;
  folder?: Folder;
  error?: string;
}

export interface FolderListResponse {
  success: boolean;
  folders: Folder[];
  pagination?: {
    page: number;
    per_page: number;
    total_count: number;
    total_pages: number;
    has_next: boolean;
    has_prev: boolean;
  };
  filters?: {
    parent_folder_id?: string;
    is_private?: boolean;
    search?: string;
    sort_by: string;
    sort_order: string;
  };
  message?: string;
  error?: string;
}

export interface FolderTreeResponse {
  success: boolean;
  tree: FolderTreeItem[];
  message?: string;
  error?: string;
}

export interface FolderPathResponse {
  success: boolean;
  path: FolderPath[];
  message?: string;
  error?: string;
}

export interface FolderDocumentsResponse {
  success: boolean;
  count: number;
  documents: Document[];
  message?: string;
  error?: string;
}

export interface FolderStatisticsResponse {
  success: boolean;
  statistics: FolderStatistics;
  message?: string;
  error?: string;
}

export interface MoveDocumentsResponse {
  success: boolean;
  message: string;
  updated_count: number;
  error?: string;
}

// Enhanced Document Statistics Types
export interface DocumentStatisticsFilters {
  date_from?: string;
  date_to?: string;
}

export interface DocumentStatisticsResponse {
  success: boolean;
  filters: {
    date_from?: string;
    date_to?: string;
  };
  statistics: {
    total_documents: number;
    total_size_mb: number;
    avg_document_size_mb: number;
    expiring_soon: number;
    category_breakdown: Record<string, number>;
  };
  message?: string;
  error?: string;
}

// Folder Filters
export interface FolderFilters {
  parent_folder_id?: string;
  is_private?: boolean;
  search?: string;
  sort_by?: 'name' | 'created_at' | 'updated_at';
  sort_order?: 'asc' | 'desc';
  page?: number;
  per_page?: number;
}

// Drag and Drop Types
export interface DragItem {
  type: 'document' | 'folder';
  id: string;
  name: string;
}

export interface DropResult {
  type: 'folder' | 'root';
  folderId?: string;
}
