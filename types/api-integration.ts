/**
 * API Integration Types and Interfaces
 * Defines TypeScript interfaces for API client management functionality
 */

// Base API Client interface
export interface ApiClient {
  client_id: string;
  name: string;
  created_at: string;
  last_used_at: string | null;
}

// Request interface for creating a new API client
export interface CreateApiClientRequest {
  name: string;
}

// Response interface for creating a new API client
export interface CreateApiClientResponse {
  client_id: string;
  client_secret: string;
  name: string;
}

// Response interface for resetting client secret
export interface ResetSecretResponse {
  client_id: string;
  client_secret: string;
  message: string;
  name: string;
  reset_at: string;
}

// Response interface for getting all API clients
export interface GetApiClientsResponse {
  clients: ApiClient[];
}

// Response interface for deleting an API client
export interface DeleteApiClientResponse {
  client_id: string;
  deleted_at: string;
  message: string;
}

// Interface for API client with secret (used only during creation/reset)
export interface ApiClientWithSecret extends ApiClient {
  client_secret: string;
  showSecret: boolean; // UI state to control secret visibility
}

// Props for confirmation modal
export interface ConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  isDestructive?: boolean;
  isLoading?: boolean;
}

// Props for copy button component
export interface CopyButtonProps {
  text: string;
  label?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

// Props for secret display component
export interface SecretDisplayProps {
  clientId: string;
  clientSecret: string;
  onClose: () => void;
  isReset?: boolean;
}

// Props for API client table
export interface ApiClientTableProps {
  clients: ApiClient[];
  loading: boolean;
  onResetSecret: (client: ApiClient) => void;
  onDeleteClient: (client: ApiClient) => void;
}

// Props for create API client modal
export interface CreateApiClientModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (response: CreateApiClientResponse) => void;
}

// State interface for the main API integration component
export interface ApiIntegrationState {
  clients: ApiClient[];
  loading: boolean;
  error: string | null;
  showCreateModal: boolean;
  showSecretDisplay: boolean;
  currentSecret: CreateApiClientResponse | ResetSecretResponse | null;
  showDeleteConfirm: boolean;
  showResetConfirm: boolean;
  clientToDelete: ApiClient | null;
  clientToReset: ApiClient | null;
  isDeleting: boolean;
  isResetting: boolean;
}
