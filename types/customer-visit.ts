/**
 * Customer Visit Types and Interfaces
 * Defines all TypeScript interfaces for customer visits, filtering, and statistics
 */

import { Customer } from './customer';

// Visit source types
export type VisitSource = 'biometric' | 'manual';

// Visit types
export type VisitType = 'regular' | 'loyalty' | 'reward' | 'complimentary';

// Customer Visit interface
export interface CustomerVisit {
  visit_id: string;
  customer_id: string;
  customer: Customer;
  visit_date: string; // YYYY-MM-DD format
  visit_time: string; // ISO datetime string
  visit_type: VisitType;
  source: VisitSource;
  source_record_id: string;
  device_serial_num: string;
  duration_minutes: number | null;
  is_loyalty_visit: boolean;
  loyalty_points_earned: number;
  reward_redeemed: boolean;
  redemption_id: string | null;
  notes: string | null;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

// Customer Visit Filters
export interface CustomerVisitFilters {
  customer_id?: string;
  date?: string; // YYYY-MM-DD format
  start_date?: string; // YYYY-MM-DD format
  end_date?: string; // YYYY-MM-DD format
  visit_type?: VisitType;
  source?: VisitSource;
  is_loyalty_visit?: boolean;
  reward_redeemed?: boolean;
  page?: number;
  per_page?: number;
}

// Pagination interface for visits
export interface VisitPagination {
  count: number;
  has_next: boolean;
  has_prev: boolean;
  next: string | null;
  page: number;
  per_page: number;
  previous: string | null;
  total_pages: number;
  total_records: number;
}

// Customer Visits List Response (no filters)
export interface CustomerVisitsListResponse {
  extend: {
    count: number;
    visits: CustomerVisit[];
  };
  msg: string;
}

// Customer Visits List Response (with filters)
export interface CustomerVisitsFilteredResponse {
  filters_applied: {
    customer_id: string | null;
    date: string | null;
    start_date: string | null;
    end_date: string | null;
  };
  pagination: VisitPagination;
  status: string;
  visits: CustomerVisit[];
}

// Single Customer Visit Response
export interface CustomerVisitResponse {
  extend: {
    visit: CustomerVisit;
  };
  msg: string;
}

// Daily Summary interface
export interface DailySummary {
  date: string; // YYYY-MM-DD format
  total_visits: number;
  unique_customers: number;
  loyalty_visits: number;
  reward_redemptions: number;
}

// Daily Summary Response
export interface DailySummaryResponse {
  code: number;
  extend: {
    summary: DailySummary;
  };
  msg: string;
}

// Customer Visit History Response
export interface CustomerVisitHistoryResponse {
  filters_applied: {
    customer_id: string;
    date: string | null;
    start_date: string | null;
    end_date: string | null;
  };
  pagination: VisitPagination;
  statistics: {
    customer_id: string;
    start_date: string | null;
    end_date: string | null;
    total_visits: number;
    regular_visits: number;
    loyalty_visits: number;
    reward_visits: number;
    complimentary_visits: number;
    biometric_visits: number;
    manual_visits: number;
  };
  status: string;
  visits: CustomerVisit[];
}

// Visit Statistics interface
export interface VisitStatistics {
  customer_id: string;
  start_date: string | null;
  end_date: string | null;
  total_visits: number;
  regular_visits: number;
  loyalty_visits: number;
  reward_visits: number;
  complimentary_visits: number;
  biometric_visits: number;
  manual_visits: number;
}

// Constants for visit types and sources
export const VISIT_TYPES = [
  { value: 'regular', label: 'Regular' },
  { value: 'loyalty', label: 'Loyalty' },
  { value: 'reward', label: 'Reward' },
  { value: 'complimentary', label: 'Complimentary' },
] as const;

export const VISIT_SOURCES = [
  { value: 'biometric', label: 'Biometric' },
  { value: 'manual', label: 'Manual' },
] as const;

// Helper functions
export const getVisitTypeLabel = (visitType: VisitType): string => {
  const type = VISIT_TYPES.find(t => t.value === visitType);
  return type ? type.label : visitType;
};

export const getVisitSourceLabel = (source: VisitSource): string => {
  const sourceItem = VISIT_SOURCES.find(s => s.value === source);
  return sourceItem ? sourceItem.label : source;
};

export const getVisitTypeColor = (visitType: VisitType): string => {
  switch (visitType) {
    case 'regular':
      return 'bg-blue-100 text-blue-800';
    case 'loyalty':
      return 'bg-purple-100 text-purple-800';
    case 'reward':
      return 'bg-green-100 text-green-800';
    case 'complimentary':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getVisitSourceColor = (source: VisitSource): string => {
  switch (source) {
    case 'biometric':
      return 'bg-emerald-100 text-emerald-800';
    case 'manual':
      return 'bg-orange-100 text-orange-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const formatVisitTime = (visitTime: string): string => {
  try {
    const date = new Date(visitTime);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    return visitTime;
  }
};

export const formatVisitDate = (visitDate: string): string => {
  try {
    const date = new Date(visitDate);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch (error) {
    return visitDate;
  }
};

export const formatVisitDateTime = (visitTime: string): string => {
  try {
    const date = new Date(visitTime);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true,
    });
  } catch (error) {
    return visitTime;
  }
};

export const getDurationDisplay = (durationMinutes: number | null): string => {
  if (durationMinutes === null || durationMinutes === 0) {
    return 'N/A';
  }
  
  if (durationMinutes < 60) {
    return `${durationMinutes}m`;
  }
  
  const hours = Math.floor(durationMinutes / 60);
  const minutes = durationMinutes % 60;
  
  if (minutes === 0) {
    return `${hours}h`;
  }
  
  return `${hours}h ${minutes}m`;
};

export const isRecentVisit = (visitTime: string, hoursThreshold: number = 24): boolean => {
  try {
    const visitDate = new Date(visitTime);
    const now = new Date();
    const diffInHours = (now.getTime() - visitDate.getTime()) / (1000 * 60 * 60);
    return diffInHours <= hoursThreshold;
  } catch (error) {
    return false;
  }
};
