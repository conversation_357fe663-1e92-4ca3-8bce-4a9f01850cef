/**
 * Service Management Types
 * Types for service management functionality
 */

// Base Service interface
export interface Service {
  service_id: string;
  name: string;
  description?: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: string;
}

// Service creation request
export interface CreateServiceRequest {
  name: string;
  description?: string;
}

// Service update request
export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
}

// API Response types
export interface ServiceResponse {
  extend: {
    message: string;
    service: Service;
  };
  msg: string;
}

export interface ServicesListResponse {
  extend: {
    count: number;
    services: Service[];
  };
  msg: string;
}

export interface SingleServiceResponse {
  extend: {
    service: Service;
  };
  msg: string;
}

export interface DeleteServiceResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Service filters for listing
export interface ServiceFilters {
  is_active?: boolean;
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface ServiceFormData {
  name: string;
  description: string;
  is_active: boolean;
}

// Utility functions
export const formatServiceName = (service: Service): string => {
  return service.name || "Unnamed Service";
};

export const getServiceStatusLabel = (isActive: boolean): string => {
  return isActive ? "Active" : "Inactive";
};

export const getServiceStatusColor = (isActive: boolean): string => {
  return isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800";
};

export const isServiceActive = (service: Service): boolean => {
  return service.is_active;
};

export const validateServiceName = (name: string): string | null => {
  if (!name || name.trim().length === 0) {
    return "Service name is required";
  }
  if (name.trim().length < 2) {
    return "Service name must be at least 2 characters long";
  }
  if (name.trim().length > 100) {
    return "Service name must be less than 100 characters";
  }
  return null;
};

export const validateServiceDescription = (
  description: string
): string | null => {
  if (description && description.trim().length > 500) {
    return "Description must be less than 500 characters";
  }
  return null;
};

export const validateServiceForm = (
  formData: ServiceFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  const nameError = validateServiceName(formData.name);
  if (nameError) {
    errors.name = nameError;
  }

  const descriptionError = validateServiceDescription(formData.description);
  if (descriptionError) {
    errors.description = descriptionError;
  }

  return errors;
};

// Service status constants
export const SERVICE_STATUSES = {
  ACTIVE: true,
  INACTIVE: false,
} as const;

export type ServiceStatus =
  (typeof SERVICE_STATUSES)[keyof typeof SERVICE_STATUSES];

// Default service form data
export const getDefaultServiceFormData = (): ServiceFormData => ({
  name: "",
  description: "",
  is_active: true,
});

// Service sorting options
export const SERVICE_SORT_OPTIONS = [
  { value: "name_asc", label: "Name (A-Z)" },
  { value: "name_desc", label: "Name (Z-A)" },
  { value: "created_desc", label: "Newest First" },
  { value: "created_asc", label: "Oldest First" },
  { value: "status_active", label: "Active First" },
  { value: "status_inactive", label: "Inactive First" },
] as const;

export type ServiceSortOption = (typeof SERVICE_SORT_OPTIONS)[number]["value"];
