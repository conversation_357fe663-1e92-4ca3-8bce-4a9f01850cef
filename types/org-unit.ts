/**
 * Organizational Unit Types
 * Types for managing organizational units and sub-units within departments
 */

// Base organizational unit interface
export interface OrgUnit {
  org_unit_id: string;
  name: string;
  description: string | null;
  department_id: string;
  department_name: string;
  parent_id: string | null;
  level: number;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
  children?: OrgUnit[];
}

// Request types for creating organizational units
export interface CreateOrgUnitRequest {
  name: string;
  description?: string;
  department_id: string;
  parent_id?: string;
  level: number;
  manager_id?: string;
}

// Request types for updating organizational units
export interface UpdateOrgUnitRequest {
  name?: string;
  description?: string;
  manager_id?: string;
}

// Response types
export interface OrgUnitResponse {
  success: boolean;
  org_unit: OrgUnit;
  message?: string;
}

export interface OrgUnitsListResponse {
  success: boolean;
  org_units: OrgUnit[];
  total_count: number;
  message?: string;
}

export interface SingleOrgUnitResponse extends OrgUnit {
  children: OrgUnit[];
}

export interface DeleteOrgUnitResponse {
  success: boolean;
  message: string;
}

// Department structure response (hierarchical view)
export interface DepartmentStructure {
  department_id: string;
  department_name: string;
  units: OrgUnitTreeItem[];
}

export interface OrgUnitTreeItem extends Omit<OrgUnit, 'children'> {
  children: OrgUnitTreeItem[];
}

// Filter and query types
export interface OrgUnitFilters {
  department_id?: string;
  parent_id?: string;
  level?: number;
  search?: string;
  page?: number;
  per_page?: number;
}

// Form data types
export interface OrgUnitFormData {
  name: string;
  description: string;
  department_id: string;
  parent_id: string;
  level: number;
  manager_id: string;
}

// Tree node for hierarchical display
export interface OrgUnitTreeNode {
  unit: OrgUnit;
  children: OrgUnitTreeNode[];
  isExpanded: boolean;
  depth: number;
}

// Department option for dropdowns
export interface DepartmentOption {
  department_id: string;
  name: string;
}

// Parent unit option for dropdowns (when creating sub-units)
export interface ParentUnitOption {
  org_unit_id: string;
  name: string;
  level: number;
  full_path: string; // e.g., "Department > Unit"
}

// Manager option for dropdowns
export interface ManagerOption {
  employee_id: string;
  full_name: string;
  position?: string;
}

// Statistics and analytics
export interface OrgUnitStatistics {
  total_units: number;
  units_by_level: Record<number, number>;
  units_by_department: Record<string, number>;
  units_with_managers: number;
  units_without_managers: number;
}

// Error types
export interface OrgUnitError {
  field?: string;
  message: string;
  code?: string;
}

export interface OrgUnitValidationErrors {
  name?: string[];
  description?: string[];
  department_id?: string[];
  parent_id?: string[];
  level?: string[];
  manager_id?: string[];
  general?: string[];
}

// Action types for state management
export type OrgUnitAction = 
  | 'create'
  | 'edit'
  | 'delete'
  | 'view'
  | 'assign_manager'
  | 'move'
  | 'duplicate';

// Modal state types
export interface OrgUnitModalState {
  isOpen: boolean;
  mode: 'create' | 'edit' | 'view';
  unit?: OrgUnit | null;
  parentUnit?: ParentUnitOption | null;
  department?: DepartmentOption | null;
}

// Breadcrumb item for navigation
export interface OrgUnitBreadcrumb {
  id: string;
  name: string;
  type: 'department' | 'unit';
  level?: number;
}
