/**
 * Service Consumption Management Types
 * Types for tracking and managing customer service consumption with bulk operations
 */

import { Customer } from "./customer";
import { Service } from "./service";

// Base ServiceConsumption interface
export interface ServiceConsumption {
  consumption_id: string;
  customer_id: string;
  service_id: string;
  visit_id?: string; // Added for visit tracking
  consumed_at: string; // Changed from consumption_date to match API
  quantity: number;
  total_amount: number; // Removed unit_price as it's not in the API response
  payment_status?: PaymentStatus; // Made optional as not in API response
  payment_date?: string | null; // ISO date string
  notes?: string | null;
  created_at: string;
  updated_at: string;
  customer: Customer;
  service: Service;
  price: {
    price_id: string;
    service_id: string;
    price_amount: number;
    currency: string;
    effective_from: string;
    effective_to: string | null;
    created_at: string;
    updated_at: string;
    created_by: string;
    service: Service;
  };
}

// Payment status types
export type PaymentStatus = "pending" | "paid" | "cancelled" | "refunded";

// Service item for bulk creation
export interface ServiceConsumptionItem {
  service_id: string;
  quantity: number;
  notes?: string;
}

// Service consumption creation request (bulk)
export interface CreateServiceConsumptionRequest {
  customer_id: string;
  visit_id: string;
  consumed_at: string; // ISO date string
  services: ServiceConsumptionItem[];
}

// Service consumption update request
export interface UpdateServiceConsumptionRequest {
  service_id?: string;
  consumed_at?: string;
  quantity?: number;
  payment_status?: PaymentStatus;
  notes?: string;
}

// Payment update request
export interface UpdatePaymentStatusRequest {
  payment_status: PaymentStatus;
  payment_date?: string; // ISO date string, defaults to today for 'paid' status
  notes?: string;
}

// API Response types
export interface ServiceConsumptionResponse {
  extend: {
    message: string;
    service_consumption: ServiceConsumption;
  };
  msg: string;
}

export interface ServiceConsumptionsListResponse {
  extend: {
    count: number;
    service_consumptions: ServiceConsumption[];
  };
  msg: string;
}

export interface SingleServiceConsumptionResponse {
  extend: {
    service_consumption: ServiceConsumption;
  };
  msg: string;
}

export interface DeleteServiceConsumptionResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Service consumption filters for listing
export interface ServiceConsumptionFilters {
  customer_id?: string;
  service_id?: string;
  visit_id?: string; // Added
  payment_status?: PaymentStatus;
  consumption_date_from?: string; // ISO date string
  consumption_date_to?: string; // ISO date string
  payment_date_from?: string; // ISO date string
  payment_date_to?: string; // ISO date string
  start_date?: string; // Added for total endpoint
  end_date?: string; // Added for total endpoint
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface ServiceConsumptionFormData {
  customer_id: string;
  service_id: string;
  visit_id: string;
  consumed_at: string;
  quantity: string;
  payment_status: PaymentStatus;
  notes: string;
}

// Payment status utility functions
export const getPaymentStatusLabel = (status: PaymentStatus): string => {
  switch (status) {
    case "pending":
      return "Pending";
    case "paid":
      return "Paid";
    case "cancelled":
      return "Cancelled";
    case "refunded":
      return "Refunded";
    default:
      return "Unknown";
  }
};

export const getPaymentStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case "pending":
      return "bg-yellow-100 text-yellow-800";
    case "paid":
      return "bg-green-100 text-green-800";
    case "cancelled":
      return "bg-gray-100 text-gray-800";
    case "refunded":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const isPaymentPending = (consumption: ServiceConsumption): boolean => {
  return consumption.payment_status === "pending";
};

export const isPaymentCompleted = (
  consumption: ServiceConsumption
): boolean => {
  return consumption.payment_status === "paid";
};

export const calculateTotalAmount = (
  quantity: number,
  unitPrice: number
): number => {
  return quantity * unitPrice;
};

export const formatCurrency = (
  amount: number,
  currency: string = "RWF"
): string => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
};

// Validation functions
export const validateQuantity = (quantity: string): string | null => {
  const num = parseFloat(quantity);

  if (!quantity || quantity.trim().length === 0) {
    return "Quantity is required";
  }

  if (isNaN(num)) {
    return "Quantity must be a valid number";
  }

  if (num <= 0) {
    return "Quantity must be greater than 0";
  }

  if (num > 1000) {
    return "Quantity cannot exceed 1000";
  }

  return null;
};

export const validateUnitPrice = (price: string): string | null => {
  const num = parseFloat(price);

  if (!price || price.trim().length === 0) {
    return "Unit price is required";
  }

  if (isNaN(num)) {
    return "Unit price must be a valid number";
  }

  if (num < 0) {
    return "Unit price cannot be negative";
  }

  if (num > 100000) {
    return "Unit price cannot exceed 100,000";
  }

  return null;
};

export const validateConsumptionDate = (date: string): string | null => {
  if (!date || date.trim().length === 0) {
    return "Consumption date is required";
  }

  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) {
    return "Consumption date must be a valid date";
  }

  const today = new Date();
  today.setHours(23, 59, 59, 999); // End of today
  if (dateObj > today) {
    return "Consumption date cannot be in the future";
  }

  return null;
};

export const validateNotes = (notes: string): string | null => {
  if (notes && notes.length > 1000) {
    return "Notes must be less than 1000 characters";
  }

  return null;
};

export const validateServiceConsumptionForm = (
  formData: ServiceConsumptionFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.customer_id) {
    errors.customer_id = "Customer is required";
  }

  if (!formData.service_id) {
    errors.service_id = "Service is required";
  }

  if (!formData.visit_id) {
    errors.visit_id = "Visit is required";
  }

  const quantityError = validateQuantity(formData.quantity);
  if (quantityError) {
    errors.quantity = quantityError;
  }

  const dateError = validateConsumptionDate(formData.consumed_at);
  if (dateError) {
    errors.consumed_at = dateError;
  }

  const notesError = validateNotes(formData.notes);
  if (notesError) {
    errors.notes = notesError;
  }

  return errors;
};

// Default service consumption form data
export const getDefaultServiceConsumptionFormData =
  (): ServiceConsumptionFormData => ({
    customer_id: "",
    service_id: "",
    visit_id: "",
    consumed_at: new Date().toISOString().split("T")[0], // Today's date
    quantity: "1",
    payment_status: "pending",
    notes: "",
  });

// Service consumption sorting options
export const SERVICE_CONSUMPTION_SORT_OPTIONS = [
  { value: "consumed_at_desc", label: "Consumption Date (Newest First)" },
  { value: "consumed_at_asc", label: "Consumption Date (Oldest First)" },
  { value: "customer_name_asc", label: "Customer Name (A-Z)" },
  { value: "customer_name_desc", label: "Customer Name (Z-A)" },
  { value: "service_name_asc", label: "Service Name (A-Z)" },
  { value: "total_amount_desc", label: "Total Amount (Highest First)" },
  { value: "total_amount_asc", label: "Total Amount (Lowest First)" },
  { value: "payment_status_pending", label: "Pending Payments First" },
  { value: "payment_date_desc", label: "Payment Date (Newest First)" },
] as const;

export type ServiceConsumptionSortOption =
  (typeof SERVICE_CONSUMPTION_SORT_OPTIONS)[number]["value"];

// Service consumption statistics
export interface ServiceConsumptionStatistics {
  total: number;
  pending_payment: number;
  paid: number;
  cancelled: number;
  refunded: number;
  total_revenue: number;
  pending_revenue: number;
  by_service: Record<
    string,
    {
      count: number;
      revenue: number;
    }
  >;
  by_customer: Record<
    string,
    {
      count: number;
      revenue: number;
    }
  >;
  by_month: Record<
    string,
    {
      count: number;
      revenue: number;
    }
  >;
}

// Bulk operations
export interface BulkUpdatePaymentStatusRequest {
  consumption_ids: string[];
  payment_status: PaymentStatus;
  payment_date?: string;
  notes?: string;
}

export interface BulkDeleteServiceConsumptionRequest {
  consumption_ids: string[];
}

// Payment status options for dropdowns
export const PAYMENT_STATUS_OPTIONS: { value: PaymentStatus; label: string }[] =
  [
    { value: "pending", label: "Pending" },
    { value: "paid", label: "Paid" },
    { value: "cancelled", label: "Cancelled" },
    { value: "refunded", label: "Refunded" },
  ];

// Search and filtering
export const searchServiceConsumptions = (
  consumptions: ServiceConsumption[],
  searchTerm: string
): ServiceConsumption[] => {
  if (!searchTerm.trim()) return consumptions;

  const term = searchTerm.toLowerCase();
  return consumptions.filter(
    (consumption) =>
      consumption.customer.first_name.toLowerCase().includes(term) ||
      consumption.customer.last_name.toLowerCase().includes(term) ||
      (consumption.customer.email &&
        consumption.customer.email.toLowerCase().includes(term)) ||
      consumption.service.name.toLowerCase().includes(term) ||
      (consumption.notes && consumption.notes.toLowerCase().includes(term))
  );
};

export const filterServiceConsumptionsByPaymentStatus = (
  consumptions: ServiceConsumption[],
  status: PaymentStatus
): ServiceConsumption[] => {
  return consumptions.filter(
    (consumption) => consumption.payment_status === status
  );
};

export const filterServiceConsumptionsByCustomer = (
  consumptions: ServiceConsumption[],
  customerId: string
): ServiceConsumption[] => {
  return consumptions.filter(
    (consumption) => consumption.customer_id === customerId
  );
};

export const filterServiceConsumptionsByService = (
  consumptions: ServiceConsumption[],
  serviceId: string
): ServiceConsumption[] => {
  return consumptions.filter(
    (consumption) => consumption.service_id === serviceId
  );
};

export const filterServiceConsumptionsByDateRange = (
  consumptions: ServiceConsumption[],
  fromDate: string,
  toDate: string
): ServiceConsumption[] => {
  const from = new Date(fromDate);
  const to = new Date(toDate);

  return consumptions.filter((consumption) => {
    const consumptionDate = new Date(consumption.consumed_at);
    return consumptionDate >= from && consumptionDate <= to;
  });
};
