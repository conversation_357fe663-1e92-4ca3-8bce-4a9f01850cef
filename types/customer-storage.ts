/**
 * Customer Storage Management Types
 * Types for managing customer belongings storage, retrieval, and abandonment
 */

import { Customer } from "./customer";
import { StorageLocation } from "./storage-location";

// Visit interface (nested in customer storage)
export interface Visit {
  visit_id: string;
  customer_id: string;
  visit_date: string;
  visit_time: string;
  visit_type: string;
  source: string;
  source_record_id: string;
  device_serial_num: string | null;
  duration_minutes: number | null;
  is_loyalty_visit: boolean;
  loyalty_points_earned: number;
  reward_redeemed: boolean;
  redemption_id: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  customer?: Customer;
}

// Storage status type
export type StorageStatus = "stored" | "retrieved" | "abandoned";

// Base CustomerStorage interface
export interface CustomerStorage {
  storage_id: string;
  customer_id: string;
  location_id: string;
  visit_id: string;
  items_description: string;
  status: StorageStatus;
  stored_at: string;
  retrieved_at: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
  customer: Customer;
  location: StorageLocation;
  visit: Visit;
}

// Customer storage creation request
export interface CreateCustomerStorageRequest {
  customer_id: string;
  location_id: string;
  visit_id: string;
  items_description: string;
  notes?: string;
}

// Customer storage update request
export interface UpdateCustomerStorageRequest {
  location_id?: string;
  items_description?: string;
  notes?: string;
}

// Customer storage retrieval request
export interface RetrieveCustomerStorageRequest {
  retrieved_at?: string; // ISO datetime string
  notes?: string;
}

// Customer storage abandonment request
export interface AbandonCustomerStorageRequest {
  retrieved_at?: string; // ISO datetime string (reused for abandonment)
  notes?: string;
}

// API Response types
export interface CustomerStorageResponse {
  extend: {
    message: string;
    customer_storage: CustomerStorage;
  };
  msg: string;
}

export interface CustomerStoragesListResponse {
  extend: {
    count: number;
    customer_storages: CustomerStorage[];
  };
  msg: string;
}

export interface SingleCustomerStorageResponse {
  extend: {
    customer_storage: CustomerStorage;
  };
  msg: string;
}

export interface DeleteCustomerStorageResponse {
  extend: {
    message: string;
  };
  msg: string;
}

export interface RetrieveStorageResponse {
  extend: {
    message: string;
  };
  msg: string;
}

export interface AbandonStorageResponse {
  extend: {
    message: string;
  };
  msg: string;
}

// Customer storage filters for listing
export interface CustomerStorageFilters {
  customer_id?: string;
  location_id?: string;
  visit_id?: string;
  status?: StorageStatus;
  search?: string;
  page?: number;
  limit?: number;
}

// Form data interface
export interface CustomerStorageFormData {
  customer_id: string;
  location_id: string;
  visit_id: string;
  items_description: string;
  notes: string;
}

// Utility functions
export const getStorageStatus = (
  customerStorage: CustomerStorage
): StorageStatus => {
  return customerStorage.status;
};

export const getStorageStatusLabel = (status: StorageStatus): string => {
  switch (status) {
    case "stored":
      return "Stored";
    case "retrieved":
      return "Retrieved";
    case "abandoned":
      return "Abandoned";
    default:
      return "Unknown";
  }
};

export const getStorageStatusColor = (status: StorageStatus): string => {
  switch (status) {
    case "stored":
      return "bg-blue-100 text-blue-800";
    case "retrieved":
      return "bg-green-100 text-green-800";
    case "abandoned":
      return "bg-red-100 text-red-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

export const isStorageActive = (customerStorage: CustomerStorage): boolean => {
  return customerStorage.status === "stored";
};

export const getStorageDuration = (
  customerStorage: CustomerStorage
): number => {
  const startDate = new Date(customerStorage.stored_at);
  const endDate = customerStorage.retrieved_at
    ? new Date(customerStorage.retrieved_at)
    : new Date();

  return Math.ceil(
    (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)
  );
};

export const formatStorageDuration = (days: number): string => {
  if (days === 0) return "Today";
  if (days === 1) return "1 day";
  if (days < 30) return `${days} days`;
  if (days < 365) {
    const months = Math.floor(days / 30);
    return months === 1 ? "1 month" : `${months} months`;
  }
  const years = Math.floor(days / 365);
  return years === 1 ? "1 year" : `${years} years`;
};

export const isStorageOverdue = (
  customerStorage: CustomerStorage,
  maxDays: number = 30
): boolean => {
  if (!isStorageActive(customerStorage)) return false;
  return getStorageDuration(customerStorage) > maxDays;
};

export const validateItemDescription = (description: string): string | null => {
  if (!description || description.trim().length === 0) {
    return "Item description is required";
  }

  if (description.trim().length < 3) {
    return "Item description must be at least 3 characters long";
  }

  if (description.trim().length > 500) {
    return "Item description must be less than 500 characters";
  }

  return null;
};

export const validateNotes = (notes: string): string | null => {
  if (notes && notes.length > 1000) {
    return "Notes must be less than 1000 characters";
  }

  return null;
};

export const validateCustomerStorageForm = (
  formData: CustomerStorageFormData
): Record<string, string> => {
  const errors: Record<string, string> = {};

  if (!formData.customer_id) {
    errors.customer_id = "Customer is required";
  }

  if (!formData.location_id) {
    errors.location_id = "Storage location is required";
  }

  if (!formData.visit_id) {
    errors.visit_id = "Visit is required";
  }

  const descriptionError = validateItemDescription(formData.items_description);
  if (descriptionError) {
    errors.items_description = descriptionError;
  }

  const notesError = validateNotes(formData.notes);
  if (notesError) {
    errors.notes = notesError;
  }

  return errors;
};

// Default customer storage form data
export const getDefaultCustomerStorageFormData =
  (): CustomerStorageFormData => ({
    customer_id: "",
    location_id: "",
    visit_id: "",
    items_description: "",
    notes: "",
  });

// Customer storage sorting options
export const CUSTOMER_STORAGE_SORT_OPTIONS = [
  { value: "customer_name_asc", label: "Customer Name (A-Z)" },
  { value: "customer_name_desc", label: "Customer Name (Z-A)" },
  { value: "stored_date_desc", label: "Storage Date (Newest First)" },
  { value: "stored_date_asc", label: "Storage Date (Oldest First)" },
  { value: "retrieved_date_desc", label: "Retrieved Date (Newest First)" },
  { value: "location_name_asc", label: "Location Number (A-Z)" },
  { value: "status_active", label: "Active Storage First" },
  { value: "duration_desc", label: "Longest Duration First" },
] as const;

export type CustomerStorageSortOption =
  (typeof CUSTOMER_STORAGE_SORT_OPTIONS)[number]["value"];

// Customer storage statistics
export interface CustomerStorageStatistics {
  total: number;
  active: number;
  retrieved: number;
  abandoned: number;
  overdue: number;
  by_location: Record<string, number>;
  by_customer: Record<string, number>;
}

// Search and filtering
export const searchCustomerStorages = (
  storages: CustomerStorage[],
  searchTerm: string
): CustomerStorage[] => {
  if (!searchTerm.trim()) return storages;

  const term = searchTerm.toLowerCase();
  return storages.filter(
    (storage) =>
      storage.customer.first_name.toLowerCase().includes(term) ||
      storage.customer.last_name.toLowerCase().includes(term) ||
      (storage.customer.email &&
        storage.customer.email.toLowerCase().includes(term)) ||
      storage.items_description.toLowerCase().includes(term) ||
      storage.location.location_number.toLowerCase().includes(term) ||
      (storage.notes && storage.notes.toLowerCase().includes(term))
  );
};

export const filterCustomerStoragesByStatus = (
  storages: CustomerStorage[],
  status: StorageStatus
): CustomerStorage[] => {
  return storages.filter((storage) => storage.status === status);
};

export const filterCustomerStoragesByCustomer = (
  storages: CustomerStorage[],
  customerId: string
): CustomerStorage[] => {
  return storages.filter((storage) => storage.customer_id === customerId);
};

export const filterCustomerStoragesByLocation = (
  storages: CustomerStorage[],
  locationId: string
): CustomerStorage[] => {
  return storages.filter((storage) => storage.location_id === locationId);
};

export const filterCustomerStoragesByDateRange = (
  storages: CustomerStorage[],
  fromDate: string,
  toDate: string
): CustomerStorage[] => {
  const from = new Date(fromDate);
  const to = new Date(toDate);

  return storages.filter((storage) => {
    const storedDate = new Date(storage.stored_at);
    return storedDate >= from && storedDate <= to;
  });
};

// Helper to calculate statistics from storage array
export const calculateStorageStatistics = (
  storages: CustomerStorage[]
): CustomerStorageStatistics => {
  const stats: CustomerStorageStatistics = {
    total: storages.length,
    active: 0,
    retrieved: 0,
    abandoned: 0,
    overdue: 0,
    by_location: {},
    by_customer: {},
  };

  storages.forEach((storage) => {
    // Count by status
    if (storage.status === "stored") {
      stats.active++;
      if (isStorageOverdue(storage)) {
        stats.overdue++;
      }
    } else if (storage.status === "retrieved") {
      stats.retrieved++;
    } else if (storage.status === "abandoned") {
      stats.abandoned++;
    }

    // Count by location
    const locationKey = storage.location.location_number;
    stats.by_location[locationKey] = (stats.by_location[locationKey] || 0) + 1;

    // Count by customer
    const customerKey = `${storage.customer.first_name} ${storage.customer.last_name}`;
    stats.by_customer[customerKey] = (stats.by_customer[customerKey] || 0) + 1;
  });

  return stats;
};
