/**
 * Device Sync Management Types
 * TypeScript interfaces for device sync API responses and operations
 */

// Command status types
export type CommandStatus = 'completed' | 'pending' | 'failed' | 'error';
export type CommandType = 'setusername' | 'deleteuser' | 'adduser' | 'sync' | 'init';
export type BiometricType = 'unknown' | 'fingerprint' | 'face' | 'palm';
export type PersonType = 'customer' | 'employee';

// Individual sync command interface
export interface SyncCommand {
  backupnum: number | null;
  biometric_type: BiometricType;
  can_retry: boolean;
  command_id: number;
  command_type: CommandType;
  created: string; // ISO date string
  err_count: number;
  last_attempt: string; // ISO date string
  modified: string; // ISO date string
  status: CommandStatus;
}

// Device sync details for a specific device
export interface DeviceSyncDetails {
  commands: SyncCommand[];
  device_connected: boolean;
  device_sn: string;
}

// Sync summary statistics
export interface SyncSummary {
  failed_devices: number;
  pending_devices: number;
  synced_devices: number;
  total_devices: number;
}

// Base sync status response (for both customer and employee)
export interface SyncStatusResponse {
  device_sync_details: DeviceSyncDetails[];
  enroll_id: number;
  person_id: string;
  person_name: string;
  person_type: PersonType;
  sync_summary: SyncSummary;
}

// Customer sync status response
export interface CustomerSyncStatusResponse extends SyncStatusResponse {
  person_type: 'customer';
}

// Employee sync status response
export interface EmployeeSyncStatusResponse extends SyncStatusResponse {
  person_type: 'employee';
}

// Failed sync details
export interface FailedSync {
  person_id: string;
  person_name: string;
  person_type: PersonType;
  device_sn: string;
  command_type: CommandType;
  error_message: string;
  last_attempt: string;
  err_count: number;
  can_retry: boolean;
}

// Sync failures response
export interface SyncFailuresResponse {
  company_id: string;
  failed_by_type: {
    customers: number;
    employees: number;
  };
  failures: FailedSync[];
  total_failed_syncs: number;
}

// Retry sync request
export interface RetrySyncRequest {
  person_type: PersonType;
  person_id: string;
}

// Reset details for retry operation
export interface ResetDetail {
  device_sn: string;
  command_id: number;
  command_type: CommandType;
  previous_status: CommandStatus;
  new_status: CommandStatus;
}

// Retry sync response
export interface RetrySyncResponse {
  commands_reset: number;
  devices_affected: string[];
  message: string;
  person_id: string;
  person_name: string;
  person_type: PersonType;
  reset_details: ResetDetail[];
  status: 'success' | 'error' | 'warning';
  warnings: string[];
}

// Super Admin device management types

// Add company device request
export interface AddCompanyDeviceRequest {
  company_id: string;
  device_sn: string;
}

// Device info in add company device response
export interface AddedDevice {
  company_id: string;
  company_name: string;
  created_at: string;
  database_name: string;
  device_sn: string;
  id: number;
}

// Add company device response
export interface AddCompanyDeviceResponse {
  device: AddedDevice;
  message: string;
}

// Initialize device response
export interface InitializeDeviceResponse {
  message: string;
}

// Device sync management state for UI components
export interface DeviceSyncState {
  isLoading: boolean;
  error: string | null;
  syncStatus: SyncStatusResponse | null;
  syncFailures: SyncFailuresResponse | null;
  selectedPersonId: string | null;
  selectedPersonType: PersonType | null;
  isRetrying: boolean;
  retryResult: RetrySyncResponse | null;
}

// Props for device sync components
export interface DeviceSyncManagementProps {
  companyId?: string;
}

export interface SyncStatusCardProps {
  syncStatus: SyncStatusResponse;
  onRetry: (personId: string, personType: PersonType) => void;
  isRetrying: boolean;
}

export interface SyncFailuresTableProps {
  failures: FailedSync[];
  onRetry: (personId: string, personType: PersonType) => void;
  isRetrying: boolean;
}

export interface DeviceCommandsTableProps {
  commands: SyncCommand[];
  deviceSn: string;
}

// Super Admin device management state
export interface SuperAdminDeviceState {
  isLoading: boolean;
  error: string | null;
  isAddingDevice: boolean;
  isInitializingDevice: boolean;
  addDeviceResult: AddCompanyDeviceResponse | null;
  initializeResult: InitializeDeviceResponse | null;
}

export interface AddDeviceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (result: AddCompanyDeviceResponse) => void;
  companyId: string;
  companyName: string;
}

export interface InitializeDeviceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (result: InitializeDeviceResponse) => void;
  deviceSn: string;
}
