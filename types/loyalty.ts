/**
 * Customer Loyalty Types and Interfaces
 * Based on actual API specifications for loyalty management system
 */

// ============================================================================
// CORE LOYALTY INTERFACES
// ============================================================================

/**
 * Loyalty rule configuration
 */
export interface LoyaltyRule {
  rule_id: string;
  name: string;
  description: string;
  rule_type: string;
  reward_type: string;
  reward_value: number;
  reward_description: string | null;
  reward_expiry_days: number | null;
  trigger_value: number;
  trigger_period_days: number | null;
  applicable_customer_segments: string | null;
  applicable_visit_types: string | null;
  max_redemptions_per_customer: number | null;
  priority: number;
  is_active: boolean;
  valid_from: string;
  valid_until: string;
  created_at: string;
  updated_at: string;
}

/**
 * Customer loyalty balance item
 */
export interface LoyaltyBalance {
  balance_id: string;
  customer_id: string;
  rule_id: string;
  rule: LoyaltyRule;
  current_count: number;
  target_count: number;
  progress_percentage: number;
  rewards_earned: number;
  rewards_redeemed: number;
  rewards_expired: number;
  rewards_available: number;
  last_activity_date: string;
  period_start_date: string | null;
  period_end_date: string | null;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Customer reward redemption record
 */
export interface RewardRedemption {
  redemption_id: string;
  customer_id: string;
  rule_id: string;
  reward_type: string;
  reward_value: number;
  redeemed_at: string;
  visit_id?: string;
  notes?: string;
  status: "completed" | "pending" | "cancelled";
  created_at: string;
  updated_at: string;
}

/**
 * Customer loyalty summary
 */
export interface CustomerLoyalty {
  customer_id: string;
  customer_name: string;
  customer_segment: string;
  total_rewards_earned: number;
  total_rewards_redeemed: number;
  total_rewards_available: number;
  active_balances: LoyaltyBalance[];
  recent_redemptions: RewardRedemption[];
}

/**
 * Customer rewards information
 */
export interface CustomerRewards {
  eligible: boolean;
  total_available_rewards: number;
  balances: LoyaltyBalance[];
}

// ============================================================================
// API RESPONSE INTERFACES
// ============================================================================

/**
 * Customer loyalty summary API response
 */
export interface CustomerLoyaltyResponse {
  extend: {
    loyalty: CustomerLoyalty;
  };
  msg: string;
}

/**
 * Customer rewards API response
 */
export interface CustomerRewardsResponse {
  extend: {
    rewards: CustomerRewards;
  };
  msg: string;
}

/**
 * Loyalty balances list API response
 */
export interface LoyaltyBalancesResponse {
  extend: {
    count: number;
    balances: LoyaltyBalance[];
  };
  msg: string;
}

/**
 * Customer redemptions list API response
 */
export interface CustomerRedemptionsResponse {
  extend: {
    count: number;
    redemptions: RewardRedemption[];
  };
  msg: string;
}

/**
 * Reward redemption request
 */
export interface RedeemRewardRequest {
  rule_id: string;
}

/**
 * Reward redemption API response
 */
export interface RedeemRewardResponse {
  extend: {
    redemption: RewardRedemption;
  };
  msg: string;
}

// ============================================================================
// LOYALTY ANALYTICS INTERFACES
// ============================================================================

/**
 * Loyalty analytics period
 */
export interface LoyaltyAnalyticsPeriod {
  start_date: string;
  end_date: string;
}

/**
 * Redemptions by reward type breakdown
 */
export interface RedemptionsByRewardType {
  [rewardType: string]: number;
}

/**
 * Loyalty analytics data
 */
export interface LoyaltyAnalytics {
  period: LoyaltyAnalyticsPeriod;
  customers_with_rewards: number;
  total_rewards_earned: number;
  total_rewards_redeemed: number;
  total_rewards_available: number;
  redemptions: {
    total: number;
    by_reward_type: RedemptionsByRewardType;
  };
}

/**
 * Loyalty analytics API response
 */
export interface LoyaltyAnalyticsResponse {
  extend: {
    analytics: LoyaltyAnalytics;
  };
  msg: string;
}

// ============================================================================
// FILTER AND QUERY INTERFACES
// ============================================================================

/**
 * Loyalty analytics filters
 */
export interface LoyaltyAnalyticsFilters {
  start_date?: string;
  end_date?: string;
}

/**
 * Customer redemptions filters
 */
export interface CustomerRedemptionsFilters {
  customer_id?: string;
  start_date?: string;
  end_date?: string;
  reward_type?: string;
  status?: "completed" | "pending" | "cancelled";
  page?: number;
  per_page?: number;
}

/**
 * Loyalty balances filters
 */
export interface LoyaltyBalancesFilters {
  customer_id?: string;
  reward_type?: string;
  is_active?: boolean;
  page?: number;
  per_page?: number;
}

// ============================================================================
// UTILITY INTERFACES AND CONSTANTS
// ============================================================================

/**
 * Redemption status options
 */
export const REDEMPTION_STATUS = {
  COMPLETED: "completed",
  PENDING: "pending",
  CANCELLED: "cancelled",
} as const;

/**
 * Reward type display names
 */
export const REWARD_TYPE_LABELS = {
  free_visit: "Free Visit",
  discount_percentage: "Percentage Discount",
  discount_amount: "Amount Discount",
  loyalty_points: "Loyalty Points",
  free_service: "Free Service",
} as const;

/**
 * Rule type display names
 */
export const RULE_TYPE_LABELS = {
  visit_count: "Visit Count",
  spending_amount: "Spending Amount",
  referral: "Referral",
  birthday: "Birthday",
  anniversary: "Anniversary",
} as const;

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/**
 * Format reward value for display
 */
export const formatRewardValue = (
  rewardType: string,
  value: number
): string => {
  switch (rewardType) {
    case "discount_percentage":
      return `${value}%`;
    case "discount_amount":
      return `$${value.toFixed(2)}`;
    case "loyalty_points":
      return `${value} points`;
    case "free_visit":
      return `${value} visit${value !== 1 ? "s" : ""}`;
    case "free_service":
      return `${value} service${value !== 1 ? "s" : ""}`;
    default:
      return value.toString();
  }
};

/**
 * Get status color for active/inactive
 */
export const getActiveStatusColor = (isActive: boolean): string => {
  return isActive ? "text-green-600 bg-green-100" : "text-gray-600 bg-gray-100";
};

/**
 * Get status color for redemption
 */
export const getRedemptionStatusColor = (status: string): string => {
  switch (status) {
    case "completed":
      return "text-green-600 bg-green-100";
    case "pending":
      return "text-yellow-600 bg-yellow-100";
    case "cancelled":
      return "text-red-600 bg-red-100";
    default:
      return "text-gray-600 bg-gray-100";
  }
};

/**
 * Check if rule is expiring soon (within 7 days)
 */
export const isRuleExpiringSoon = (validUntil: string): boolean => {
  const expiry = new Date(validUntil);
  const now = new Date();
  const sevenDaysFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

  return expiry <= sevenDaysFromNow && expiry > now;
};

/**
 * Calculate total available rewards by type
 */
export const calculateRewardsByType = (
  balances: LoyaltyBalance[]
): RedemptionsByRewardType => {
  return balances
    .filter((balance) => balance.is_active && balance.rewards_available > 0)
    .reduce((acc, balance) => {
      const rewardType = balance.rule.reward_type;
      acc[rewardType] = (acc[rewardType] || 0) + balance.rewards_available;
      return acc;
    }, {} as RedemptionsByRewardType);
};

/**
 * Get progress color based on percentage
 */
export const getProgressColor = (percentage: number): string => {
  if (percentage >= 100) return "bg-green-500";
  if (percentage >= 75) return "bg-blue-500";
  if (percentage >= 50) return "bg-yellow-500";
  return "bg-gray-300";
};
