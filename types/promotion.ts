/**
 * Promotion Types and Interfaces
 * Defines all TypeScript interfaces for promotions, rules, and rewards
 */

import { CustomerSegment } from './customer';
import { VisitType } from './customer-visit';

// Rule types for promotions
export type RuleType = 'visit_count' | 'spending_amount' | 'frequency' | 'birthday' | 'first_visit';

// Reward types for promotions
export type RewardType = 'free_visit' | 'discount_percentage' | 'discount_amount' | 'loyalty_points' | 'free_service';

// Promotion interface
export interface Promotion {
  rule_id: string;
  name: string;
  description: string | null;
  rule_type: RuleType;
  trigger_value: number;
  trigger_period_days: number | null;
  reward_type: RewardType;
  reward_value: number;
  reward_description: string | null;
  reward_expiry_days: number | null;
  applicable_customer_segments: string | null; // Can be comma-separated values
  applicable_visit_types: string | null; // Can be comma-separated values
  max_redemptions_per_customer: number | null;
  priority: number;
  is_active: boolean;
  valid_from: string | null; // ISO date string
  valid_until: string | null; // ISO date string
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
}

// Create Promotion Request
export interface CreatePromotionRequest {
  name: string;
  description?: string;
  rule_type: RuleType;
  trigger_value: number;
  trigger_period_days?: number;
  reward_type: RewardType;
  reward_value: number;
  reward_description?: string;
  reward_expiry_days?: number;
  applicable_customer_segments?: string;
  applicable_visit_types?: string;
  max_redemptions_per_customer?: number;
  priority?: number;
  valid_from?: string;
  valid_until?: string;
}

// Update Promotion Request
export interface UpdatePromotionRequest {
  name?: string;
  description?: string;
  trigger_value?: number;
  trigger_period_days?: number;
  reward_value?: number;
  reward_description?: string;
  reward_expiry_days?: number;
  applicable_customer_segments?: string;
  applicable_visit_types?: string;
  max_redemptions_per_customer?: number;
  priority?: number;
  is_active?: boolean;
  valid_from?: string;
  valid_until?: string;
}

// Promotion Response
export interface PromotionResponse {
  extend: {
    message: string;
    promotion: Promotion;
  };
  msg: string;
}

// Promotions List Response
export interface PromotionsListResponse {
  extend: {
    count: number;
    promotions: Promotion[];
  };
  msg: string;
}

// Single Promotion Response
export interface SinglePromotionResponse {
  extend: {
    promotion: Promotion;
  };
  msg: string;
}

// Deactivate Promotion Response
export interface DeactivatePromotionResponse {
  extend: {
    message: string;
    promotion: Promotion;
  };
  msg: string;
}

// Promotion filters
export interface PromotionFilters {
  is_active?: boolean;
  rule_type?: RuleType;
  reward_type?: RewardType;
  applicable_customer_segments?: CustomerSegment;
  search?: string;
}

// Constants for rule types and reward types
export const RULE_TYPES = [
  { value: 'visit_count', label: 'Visit Count', description: 'Trigger after a certain number of visits' },
  { value: 'spending_amount', label: 'Spending Amount', description: 'Trigger after spending a certain amount' },
  { value: 'frequency', label: 'Visit Frequency', description: 'Trigger based on visit frequency' },
  { value: 'birthday', label: 'Birthday', description: 'Trigger on customer birthday' },
  { value: 'first_visit', label: 'First Visit', description: 'Trigger on first visit' },
] as const;

export const REWARD_TYPES = [
  { value: 'free_visit', label: 'Free Visit', description: 'Award free visits' },
  { value: 'discount_percentage', label: 'Percentage Discount', description: 'Percentage discount on services' },
  { value: 'discount_amount', label: 'Fixed Discount', description: 'Fixed amount discount' },
  { value: 'loyalty_points', label: 'Loyalty Points', description: 'Award loyalty points' },
  { value: 'free_service', label: 'Free Service', description: 'Award free specific service' },
] as const;

// Helper functions
export const getRuleTypeLabel = (ruleType: RuleType): string => {
  const rule = RULE_TYPES.find(r => r.value === ruleType);
  return rule ? rule.label : ruleType;
};

export const getRuleTypeDescription = (ruleType: RuleType): string => {
  const rule = RULE_TYPES.find(r => r.value === ruleType);
  return rule ? rule.description : '';
};

export const getRewardTypeLabel = (rewardType: RewardType): string => {
  const reward = REWARD_TYPES.find(r => r.value === rewardType);
  return reward ? reward.label : rewardType;
};

export const getRewardTypeDescription = (rewardType: RewardType): string => {
  const reward = REWARD_TYPES.find(r => r.value === rewardType);
  return reward ? reward.description : '';
};

export const getPromotionStatusColor = (isActive: boolean): string => {
  return isActive 
    ? 'bg-green-100 text-green-800' 
    : 'bg-red-100 text-red-800';
};

export const getPromotionStatusLabel = (isActive: boolean): string => {
  return isActive ? 'Active' : 'Inactive';
};

export const getRuleTypeColor = (ruleType: RuleType): string => {
  switch (ruleType) {
    case 'visit_count':
      return 'bg-blue-100 text-blue-800';
    case 'spending_amount':
      return 'bg-green-100 text-green-800';
    case 'frequency':
      return 'bg-purple-100 text-purple-800';
    case 'birthday':
      return 'bg-pink-100 text-pink-800';
    case 'first_visit':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const getRewardTypeColor = (rewardType: RewardType): string => {
  switch (rewardType) {
    case 'free_visit':
      return 'bg-emerald-100 text-emerald-800';
    case 'discount_percentage':
      return 'bg-orange-100 text-orange-800';
    case 'discount_amount':
      return 'bg-red-100 text-red-800';
    case 'loyalty_points':
      return 'bg-indigo-100 text-indigo-800';
    case 'free_service':
      return 'bg-teal-100 text-teal-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const formatPromotionName = (promotion: Promotion): string => {
  return promotion.name || `${getRuleTypeLabel(promotion.rule_type)} Promotion`;
};

export const formatTriggerValue = (ruleType: RuleType, triggerValue: number): string => {
  switch (ruleType) {
    case 'visit_count':
      return `${triggerValue} visits`;
    case 'spending_amount':
      return `$${triggerValue}`;
    case 'frequency':
      return `${triggerValue} visits per period`;
    case 'birthday':
      return 'On birthday';
    case 'first_visit':
      return 'First visit';
    default:
      return triggerValue.toString();
  }
};

export const formatRewardValue = (rewardType: RewardType, rewardValue: number): string => {
  switch (rewardType) {
    case 'free_visit':
      return `${rewardValue} free visit${rewardValue > 1 ? 's' : ''}`;
    case 'discount_percentage':
      return `${rewardValue}% discount`;
    case 'discount_amount':
      return `$${rewardValue} discount`;
    case 'loyalty_points':
      return `${rewardValue} points`;
    case 'free_service':
      return 'Free service';
    default:
      return rewardValue.toString();
  }
};

export const isPromotionExpired = (promotion: Promotion): boolean => {
  if (!promotion.valid_until) return false;
  
  try {
    const expiryDate = new Date(promotion.valid_until);
    const now = new Date();
    return expiryDate < now;
  } catch (error) {
    return false;
  }
};

export const isPromotionActive = (promotion: Promotion): boolean => {
  if (!promotion.is_active) return false;
  if (isPromotionExpired(promotion)) return false;
  
  if (promotion.valid_from) {
    try {
      const startDate = new Date(promotion.valid_from);
      const now = new Date();
      if (startDate > now) return false;
    } catch (error) {
      // If date parsing fails, assume it's active
    }
  }
  
  return true;
};

export const getApplicableSegments = (promotion: Promotion): string[] => {
  if (!promotion.applicable_customer_segments) return [];
  return promotion.applicable_customer_segments.split(',').map(s => s.trim());
};

export const getApplicableVisitTypes = (promotion: Promotion): string[] => {
  if (!promotion.applicable_visit_types) return [];
  return promotion.applicable_visit_types.split(',').map(s => s.trim());
};

export const formatPromotionPeriod = (promotion: Promotion): string => {
  const validFrom = promotion.valid_from ? new Date(promotion.valid_from).toLocaleDateString() : 'No start date';
  const validUntil = promotion.valid_until ? new Date(promotion.valid_until).toLocaleDateString() : 'No end date';
  
  if (!promotion.valid_from && !promotion.valid_until) {
    return 'Always active';
  }
  
  return `${validFrom} - ${validUntil}`;
};
