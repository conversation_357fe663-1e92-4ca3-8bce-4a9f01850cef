import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CustomersContent from '@/components/hr/CustomersContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Customer Management | KaziSync',
  description: 'Manage customers, customer visits, promotions and loyalty programs',
};

export default function CustomersPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <CustomersContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
