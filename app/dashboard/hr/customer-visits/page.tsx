import React from "react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import CustomerVisitsContent from "@/components/hr/CustomerVisitsContent";

const CustomerVisitsPage: React.FC = () => {
  return (
    <ProtectedRoute allowedRoles={["hr", "admin", "super-admin"]}>
      <DashboardLayout>
        <CustomerVisitsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CustomerVisitsPage;
