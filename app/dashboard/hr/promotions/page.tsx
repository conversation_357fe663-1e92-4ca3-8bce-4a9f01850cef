import React from "react";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import PromotionsContent from "@/components/hr/PromotionsContent";

const PromotionsPage: React.FC = () => {
  return (
    <ProtectedRoute allowedRoles={["hr", "admin", "super-admin"]}>
      <DashboardLayout>
        <PromotionsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default PromotionsPage;
