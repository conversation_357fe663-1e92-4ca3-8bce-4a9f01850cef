import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import OrganizationalUnitsContent from '@/components/hr/OrganizationalUnitsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export default function OrganizationalUnitsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <OrganizationalUnitsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
