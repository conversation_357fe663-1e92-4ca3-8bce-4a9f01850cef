import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ApiIntegrationContent from '@/components/hr/ApiIntegrationContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'API Integration | KaziSync',
  description: 'Manage API client credentials and integrations for your applications',
};

export default function ApiIntegrationPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ApiIntegrationContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
