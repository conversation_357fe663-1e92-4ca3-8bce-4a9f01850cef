import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import DeviceSyncManagement from '@/components/hr/DeviceSyncManagement';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Device Sync Management | KaziSync',
  description: 'Manage device synchronization for employees and customers',
};

export default function DeviceSyncPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <DeviceSyncManagement />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
