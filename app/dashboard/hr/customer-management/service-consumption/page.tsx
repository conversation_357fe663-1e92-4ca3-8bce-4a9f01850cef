import { Metadata } from "next";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import DashboardLayout from "@/components/layouts/DashboardLayout";
import ServiceConsumptionContent from "@/components/hr/ServiceConsumptionContent";

export const metadata: Metadata = {
  title: "Service Consumption Management | KaziSync",
  description: "Track and manage customer service consumption with bulk operations support",
};

export default function ServiceConsumptionPage() {
  return (
    <ProtectedRoute allowedRoles={["hr", "admin", "super-admin"]}>
      <DashboardLayout>
        <ServiceConsumptionContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
