import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CustomerStorageContent from '@/components/hr/CustomerStorageContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Customer Storage Management | KaziSync',
  description: 'Manage customer belongings storage, retrieval, and abandonment tracking',
};

export default function CustomerStoragePage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <CustomerStorageContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
