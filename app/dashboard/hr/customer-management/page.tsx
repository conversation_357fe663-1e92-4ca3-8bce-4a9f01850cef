import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import CustomerManagementDashboard from '@/components/hr/CustomerManagementDashboard';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Customer Management | KaziSync',
  description: 'Comprehensive customer relationship and service management platform',
};

export default function CustomerManagementPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <CustomerManagementDashboard />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
