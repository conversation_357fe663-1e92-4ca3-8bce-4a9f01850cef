import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import StorageLocationsContent from '@/components/hr/StorageLocationsContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Storage Locations Management | KaziSync',
  description: 'Manage storage locations and track capacity usage',
};

export default function StorageLocationsPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <StorageLocationsContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
