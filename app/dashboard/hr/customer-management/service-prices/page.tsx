import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ServicePricesContent from '@/components/hr/ServicePricesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Service Prices Management | KaziSync',
  description: 'Manage service pricing with effective dates and currency support',
};

export default function ServicePricesPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ServicePricesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
