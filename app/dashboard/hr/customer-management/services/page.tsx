import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ServicesContent from '@/components/hr/ServicesContent';
import ProtectedRoute from '@/components/auth/ProtectedRoute';

export const metadata = {
  title: 'Services Management | KaziSync',
  description: 'Manage available services and service offerings',
};

export default function ServicesPage() {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <ServicesContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
}
