import React from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import LoyaltyContent from '@/components/hr/LoyaltyContent';

const LoyaltyPage = () => {
  return (
    <ProtectedRoute allowedRoles={['hr', 'admin', 'super-admin']}>
      <DashboardLayout>
        <LoyaltyContent />
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default LoyaltyPage;
