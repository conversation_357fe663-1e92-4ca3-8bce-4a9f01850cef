"use client";

import React, { createContext, useContext, useState, useCallback } from "react";
import { DragItem, DropResult } from "@/types/document";
import { moveDocumentToFolder } from "@/lib/documents";
import { moveDocumentsToFolder } from "@/lib/folders";
import toast from "react-hot-toast";

interface DragDropContextType {
  draggedItem: DragItem | null;
  isDragging: boolean;
  startDrag: (item: DragItem) => void;
  endDrag: () => void;
  handleDrop: (dropResult: DropResult, draggedItem: DragItem) => Promise<boolean>;
}

const DragDropContext = createContext<DragDropContextType | null>(null);

export const useDragDrop = () => {
  const context = useContext(DragDropContext);
  if (!context) {
    throw new Error("useDragDrop must be used within a DragDropProvider");
  }
  return context;
};

interface DragDropProviderProps {
  children: React.ReactNode;
  onItemMoved?: () => void;
}

export const DragDropProvider: React.FC<DragDropProviderProps> = ({
  children,
  onItemMoved,
}) => {
  const [draggedItem, setDraggedItem] = useState<DragItem | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const startDrag = useCallback((item: DragItem) => {
    setDraggedItem(item);
    setIsDragging(true);
  }, []);

  const endDrag = useCallback(() => {
    setDraggedItem(null);
    setIsDragging(false);
  }, []);

  const handleDrop = useCallback(async (dropResult: DropResult, draggedItem: DragItem): Promise<boolean> => {
    try {
      if (draggedItem.type === "document") {
        // Move document to folder
        if (dropResult.type === "folder" && dropResult.folderId) {
          await moveDocumentToFolder(draggedItem.id, dropResult.folderId);
          toast.success(`Document "${draggedItem.name}" moved to folder`);
        } else if (dropResult.type === "root") {
          // Move document to root (no folder)
          await moveDocumentToFolder(draggedItem.id, "");
          toast.success(`Document "${draggedItem.name}" moved to root`);
        }
      } else if (draggedItem.type === "folder") {
        // Move folder functionality would be implemented here
        // For now, we'll show a message that this feature is coming soon
        toast.error("Moving folders is not yet implemented");
        return false;
      }

      onItemMoved?.();
      return true;
    } catch (error) {
      console.error("Error moving item:", error);
      toast.error(error instanceof Error ? error.message : "Failed to move item");
      return false;
    }
  }, [onItemMoved]);

  const value: DragDropContextType = {
    draggedItem,
    isDragging,
    startDrag,
    endDrag,
    handleDrop,
  };

  return (
    <DragDropContext.Provider value={value}>
      {children}
    </DragDropContext.Provider>
  );
};

// Utility component for drop zones
interface DropZoneProps {
  onDrop: (dropResult: DropResult) => void;
  dropResult: DropResult;
  children: React.ReactNode;
  className?: string;
  activeClassName?: string;
  disabled?: boolean;
}

export const DropZone: React.FC<DropZoneProps> = ({
  onDrop,
  dropResult,
  children,
  className = "",
  activeClassName = "bg-blue-50 border-blue-200 border-2 border-dashed",
  disabled = false,
}) => {
  const [isOver, setIsOver] = useState(false);
  const { draggedItem, isDragging } = useDragDrop();

  const handleDragOver = (e: React.DragEvent) => {
    if (disabled || !isDragging) return;
    e.preventDefault();
    setIsOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (disabled || !isDragging) return;
    // Only set isOver to false if we're actually leaving the drop zone
    const rect = e.currentTarget.getBoundingClientRect();
    const x = e.clientX;
    const y = e.clientY;
    
    if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
      setIsOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    if (disabled || !isDragging) return;
    e.preventDefault();
    setIsOver(false);
    onDrop(dropResult);
  };

  const shouldShowDropZone = isDragging && !disabled;
  const dropZoneClasses = shouldShowDropZone && isOver ? activeClassName : "";

  return (
    <div
      className={`${className} ${dropZoneClasses} transition-all duration-200`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {children}
      {shouldShowDropZone && isOver && (
        <div className="absolute inset-0 flex items-center justify-center bg-blue-50/80 rounded-lg pointer-events-none">
          <div className="bg-white px-3 py-2 rounded-md border border-blue-200 text-blue-700 text-sm font-medium">
            Drop here to move
          </div>
        </div>
      )}
    </div>
  );
};

// Draggable item wrapper
interface DraggableProps {
  item: DragItem;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

export const Draggable: React.FC<DraggableProps> = ({
  item,
  children,
  className = "",
  disabled = false,
}) => {
  const { startDrag, endDrag } = useDragDrop();

  const handleDragStart = (e: React.DragEvent) => {
    if (disabled) return;
    e.dataTransfer.effectAllowed = "move";
    e.dataTransfer.setData("application/json", JSON.stringify(item));
    startDrag(item);
  };

  const handleDragEnd = () => {
    if (disabled) return;
    endDrag();
  };

  return (
    <div
      className={className}
      draggable={!disabled}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}
    </div>
  );
};

// Hook for handling drag and drop in components
export const useDragDropHandlers = () => {
  const { handleDrop, draggedItem } = useDragDrop();

  const onDrop = useCallback(async (dropResult: DropResult) => {
    if (!draggedItem) return;
    await handleDrop(dropResult, draggedItem);
  }, [handleDrop, draggedItem]);

  return { onDrop, draggedItem };
};
