'use client';

import React, { useState } from 'react';
import { DocumentUploadRequest } from '@/types/document';
import { uploadDocument } from '@/lib/documents';
import DocumentUploadForm from './DocumentUploadForm';
import { X, CheckCircle } from 'lucide-react';

interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  employees?: Array<{ employee_id: string; full_name: string }>;
  currentFolderId?: string;
}

const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  employees = [],
  currentFolderId
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  if (!isOpen) return null;

  const handleSubmit = async (data: DocumentUploadRequest) => {
    setIsLoading(true);
    setSuccessMessage('');

    try {
      const response = await uploadDocument(data);
      
      if (response.success) {
        setSuccessMessage('Document uploaded successfully!');
        
        // Close modal after a short delay to show success message
        setTimeout(() => {
          onSuccess();
          onClose();
          setSuccessMessage('');
        }, 1500);
      } else {
        throw new Error(response.error || 'Failed to upload document');
      }
    } catch (error: any) {
      throw error; // Let the form handle the error display
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
      setSuccessMessage('');
    }
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
          aria-hidden="true"
          onClick={handleClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                Upload Document
              </h3>
              <button
                onClick={handleClose}
                disabled={isLoading}
                className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Success Message */}
          {successMessage && (
            <div className="bg-green-50 border-l-4 border-green-400 p-4 mx-6 mt-4">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-400 mr-2" />
                <p className="text-sm font-medium text-green-800">{successMessage}</p>
              </div>
            </div>
          )}

          {/* Form */}
          <div className="bg-white px-6 py-4">
            <DocumentUploadForm
              onSubmit={handleSubmit}
              onCancel={handleClose}
              employees={employees}
              isLoading={isLoading}
              currentFolderId={currentFolderId}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DocumentUploadModal;
