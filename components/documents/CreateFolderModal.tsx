"use client";

import React, { useState, useEffect } from "react";
import { X, Folder, <PERSON>, Users, Palette } from "lucide-react";
import { CreateFolderRequest, Folder as FolderType } from "@/types/document";
import { createFolder, getFolders } from "@/lib/folders";
import toast from "react-hot-toast";

interface CreateFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (folder: FolderType) => void;
  parentFolderId?: string;
  parentFolderName?: string;
}

const FOLDER_COLORS = [
  { name: "Blue", value: "#2196F3", class: "bg-blue-500" },
  { name: "Green", value: "#4CAF50", class: "bg-green-500" },
  { name: "Orange", value: "#FF9800", class: "bg-orange-500" },
  { name: "Purple", value: "#9C27B0", class: "bg-purple-500" },
  { name: "<PERSON>", value: "#F44336", class: "bg-red-500" },
  { name: "<PERSON>", value: "#607D8B", class: "bg-gray-500" },
];

const FOLDER_ICONS = [
  { name: "Folder", value: "folder" },
  { name: "Documents", value: "file-text" },
  { name: "Images", value: "image" },
  { name: "Archive", value: "archive" },
  { name: "Settings", value: "settings" },
  { name: "Users", value: "users" },
  { name: "Lock", value: "lock" },
  { name: "Star", value: "star" },
];

const ALLOWED_ROLES = [
  { value: "hr", label: "HR" },
  { value: "admin", label: "Admin" },
  { value: "employee", label: "Employee" },
];

const CreateFolderModal: React.FC<CreateFolderModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  parentFolderId,
  parentFolderName,
}) => {
  const [formData, setFormData] = useState<CreateFolderRequest>({
    folder_name: "",
    description: "",
    parent_folder_id: parentFolderId,
    color: "",
    icon: "folder",
    is_private: false,
    allowed_roles: ["hr", "admin", "employee"],
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen) {
      setFormData({
        folder_name: "",
        description: "",
        parent_folder_id: parentFolderId,
        color: "",
        icon: "folder",
        is_private: false,
        allowed_roles: ["hr", "admin", "employee"],
      });
      setErrors({});
    }
  }, [isOpen, parentFolderId]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.folder_name.trim()) {
      newErrors.folder_name = "Folder name is required";
    } else if (formData.folder_name.length < 2) {
      newErrors.folder_name = "Folder name must be at least 2 characters";
    } else if (formData.folder_name.length > 100) {
      newErrors.folder_name = "Folder name must be less than 100 characters";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters";
    }

    if (formData.is_private && (!formData.allowed_roles || formData.allowed_roles.length === 0)) {
      newErrors.allowed_roles = "At least one role must be selected for private folders";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await createFolder(formData);
      
      if (response.success && response.folder) {
        toast.success("Folder created successfully");
        onSuccess(response.folder);
        onClose();
      } else {
        toast.error(response.error || "Failed to create folder");
      }
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create folder");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof CreateFolderRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const handleRoleToggle = (role: string) => {
    const currentRoles = formData.allowed_roles || [];
    const newRoles = currentRoles.includes(role)
      ? currentRoles.filter(r => r !== role)
      : [...currentRoles, role];
    
    handleInputChange("allowed_roles", newRoles);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Folder className="h-6 w-6 text-primary mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Create New Folder</h2>
              {parentFolderName && (
                <p className="text-sm text-gray-600">in {parentFolderName}</p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Folder Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Folder Name *
            </label>
            <input
              type="text"
              value={formData.folder_name}
              onChange={(e) => handleInputChange("folder_name", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary ${
                errors.folder_name ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter folder name"
              maxLength={100}
            />
            {errors.folder_name && (
              <p className="text-red-600 text-sm mt-1">{errors.folder_name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary ${
                errors.description ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter folder description (optional)"
              rows={3}
              maxLength={500}
            />
            {errors.description && (
              <p className="text-red-600 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Palette className="h-4 w-4 inline mr-1" />
              Folder Color
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleInputChange("color", "")}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  !formData.color ? "border-primary ring-2 ring-primary/20" : "border-gray-300"
                } bg-gray-100`}
                title="Default"
              />
              {FOLDER_COLORS.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => handleInputChange("color", color.value)}
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.color === color.value ? "border-primary ring-2 ring-primary/20" : "border-gray-300"
                  } ${color.class}`}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Privacy Settings */}
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_private}
                onChange={(e) => handleInputChange("is_private", e.target.checked)}
                className="rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Lock className="h-4 w-4 ml-2 mr-1 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Private Folder</span>
            </label>
            <p className="text-xs text-gray-500 mt-1">
              Private folders are only accessible to selected roles
            </p>
          </div>

          {/* Allowed Roles (only show if private) */}
          {formData.is_private && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="h-4 w-4 inline mr-1" />
                Allowed Roles *
              </label>
              <div className="space-y-2">
                {ALLOWED_ROLES.map((role) => (
                  <label key={role.value} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={formData.allowed_roles?.includes(role.value) || false}
                      onChange={() => handleRoleToggle(role.value)}
                      className="rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="ml-2 text-sm text-gray-700">{role.label}</span>
                  </label>
                ))}
              </div>
              {errors.allowed_roles && (
                <p className="text-red-600 text-sm mt-1">{errors.allowed_roles}</p>
              )}
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? "Creating..." : "Create Folder"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateFolderModal;
