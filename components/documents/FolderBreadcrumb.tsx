"use client";

import React, { useState, useEffect } from "react";
import { ChevronRight, Home, Folder } from "lucide-react";
import { FolderPath } from "@/types/document";
import { getFolderPath } from "@/lib/folders";

interface FolderBreadcrumbProps {
  currentFolderId?: string;
  onNavigate: (folderId?: string) => void;
  className?: string;
}

const FolderBreadcrumb: React.FC<FolderBreadcrumbProps> = ({
  currentFolderId,
  onNavigate,
  className = "",
}) => {
  const [path, setPath] = useState<FolderPath[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const loadPath = async () => {
      if (!currentFolderId) {
        setPath([]);
        return;
      }

      try {
        setLoading(true);
        const response = await getFolderPath(currentFolderId);
        if (response.success) {
          setPath(response.path);
        }
      } catch (error) {
        console.error("Error loading folder path:", error);
        setPath([]);
      } finally {
        setLoading(false);
      }
    };

    loadPath();
  }, [currentFolderId]);

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-pulse flex items-center space-x-2">
          <div className="h-4 w-4 bg-gray-200 rounded"></div>
          <div className="h-4 w-20 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <nav className={`flex items-center space-x-1 sm:space-x-2 text-xs sm:text-sm overflow-x-auto ${className}`} aria-label="Breadcrumb">
      {/* Root/Home */}
      <button
        onClick={() => onNavigate()}
        className={`flex items-center px-1 sm:px-2 py-1 rounded-md transition-colors whitespace-nowrap ${
          !currentFolderId
            ? "bg-primary/10 text-primary"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
        }`}
      >
        <Home className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
        <span className="hidden sm:inline">All Documents</span>
        <span className="sm:hidden">Docs</span>
      </button>

      {/* Path segments */}
      {path.map((segment, index) => (
        <React.Fragment key={segment.folder_id}>
          <ChevronRight className="h-4 w-4 text-gray-400" />
          <button
            onClick={() => onNavigate(segment.folder_id)}
            className={`flex items-center px-2 py-1 rounded-md transition-colors ${
              index === path.length - 1
                ? "bg-primary/10 text-primary"
                : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
            }`}
          >
            <Folder className="h-4 w-4 mr-1" />
            <span className="truncate max-w-32">{segment.folder_name}</span>
          </button>
        </React.Fragment>
      ))}
    </nav>
  );
};

export default FolderBreadcrumb;
