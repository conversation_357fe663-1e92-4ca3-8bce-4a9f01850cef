"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Text, HardDrive, Layers } from "lucide-react";
import { FolderStatistics, FolderTreeItem } from "@/types/document";
import { getFolderStatistics } from "@/lib/folders";
import LoadingSpinner from "@/components/ui/LoadingSpinner";
import toast from "react-hot-toast";

interface FolderStatisticsModalProps {
  isOpen: boolean;
  onClose: () => void;
  folder: FolderTreeItem;
}

const FolderStatisticsModal: React.FC<FolderStatisticsModalProps> = ({
  isOpen,
  onClose,
  folder,
}) => {
  const [statistics, setStatistics] = useState<FolderStatistics | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen && folder) {
      loadStatistics();
    }
  }, [isOpen, folder]);

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getFolderStatistics(folder.folder_id);
      
      if (response.success && response.statistics) {
        setStatistics(response.statistics);
      } else {
        setError(response.error || "Failed to load folder statistics");
      }
    } catch (error) {
      console.error("Error loading folder statistics:", error);
      setError(error instanceof Error ? error.message : "Failed to load folder statistics");
      toast.error("Failed to load folder statistics");
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (sizeInMB: number): string => {
    if (sizeInMB < 1) {
      return `${(sizeInMB * 1024).toFixed(1)} KB`;
    } else if (sizeInMB < 1024) {
      return `${sizeInMB.toFixed(1)} MB`;
    } else {
      return `${(sizeInMB / 1024).toFixed(1)} GB`;
    }
  };

  const getCategoryColor = (index: number): string => {
    const colors = [
      "bg-blue-500",
      "bg-green-500", 
      "bg-yellow-500",
      "bg-purple-500",
      "bg-red-500",
      "bg-indigo-500",
      "bg-pink-500",
      "bg-gray-500"
    ];
    return colors[index % colors.length];
  };

  if (!isOpen || !folder) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="h-6 w-6 text-primary mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Folder Statistics</h2>
              <p className="text-sm text-gray-600">{folder.folder_name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading && (
            <div className="flex justify-center py-12">
              <LoadingSpinner size="lg" message="Loading statistics..." />
            </div>
          )}

          {error && (
            <div className="text-center py-12">
              <div className="text-red-600 mb-2">
                <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-medium">Failed to Load Statistics</p>
                <p className="text-sm text-gray-500 mt-1">{error}</p>
              </div>
              <button
                onClick={loadStatistics}
                className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {statistics && !loading && !error && (
            <div className="space-y-6">
              {/* Overview Cards */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <FileText className="h-8 w-8 text-blue-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-blue-900">Total Documents</p>
                      <p className="text-2xl font-bold text-blue-600">{statistics.total_document_count}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <HardDrive className="h-8 w-8 text-green-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-green-900">Total Size</p>
                      <p className="text-2xl font-bold text-green-600">{formatFileSize(statistics.total_size_mb)}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Folder className="h-8 w-8 text-purple-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-purple-900">Subfolders</p>
                      <p className="text-2xl font-bold text-purple-600">{statistics.subfolder_count}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                  <div className="flex items-center">
                    <Layers className="h-8 w-8 text-orange-600 mr-3" />
                    <div>
                      <p className="text-sm font-medium text-orange-900">Depth Level</p>
                      <p className="text-2xl font-bold text-orange-600">{statistics.depth}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Detailed Breakdown */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Direct vs Subfolder Breakdown */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Storage Breakdown</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Direct Documents</span>
                      <div className="text-right">
                        <span className="font-medium">{statistics.direct_document_count}</span>
                        <span className="text-sm text-gray-500 ml-2">({formatFileSize(statistics.direct_size_mb)})</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">In Subfolders</span>
                      <div className="text-right">
                        <span className="font-medium">{statistics.subfolder_document_count}</span>
                        <span className="text-sm text-gray-500 ml-2">({formatFileSize(statistics.subfolder_size_mb)})</span>
                      </div>
                    </div>
                    <div className="border-t pt-2 flex justify-between items-center font-semibold">
                      <span className="text-gray-900">Total</span>
                      <div className="text-right">
                        <span>{statistics.total_document_count}</span>
                        <span className="text-sm text-gray-500 ml-2">({formatFileSize(statistics.total_size_mb)})</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Category Breakdown */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Category Distribution</h3>
                  {Object.keys(statistics.category_breakdown).length > 0 ? (
                    <div className="space-y-2">
                      {Object.entries(statistics.category_breakdown).map(([category, count], index) => (
                        <div key={category} className="flex items-center justify-between">
                          <div className="flex items-center">
                            <div className={`w-3 h-3 rounded-full ${getCategoryColor(index)} mr-2`}></div>
                            <span className="text-sm text-gray-600 capitalize">{category}</span>
                          </div>
                          <span className="font-medium">{count}</span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500 italic">No documents in this folder</p>
                  )}
                </div>
              </div>

              {/* Folder Path */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Folder Path</h3>
                <p className="text-sm text-gray-600 font-mono bg-white px-3 py-2 rounded border">
                  {statistics.full_path}
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default FolderStatisticsModal;
