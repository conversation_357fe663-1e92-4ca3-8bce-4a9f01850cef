"use client";

import React, { useState, useEffect } from "react";
import { 
  <PERSON><PERSON>hart3, 
  FileText, 
  HardDrive, 
  AlertTriangle, 
  Calendar,
  TrendingUp,
  Filter
} from "lucide-react";
import { getDocumentStatistics, getStorageUsage } from "@/lib/documents";
import { formatFileSize } from "@/lib/documents";
import toast from "react-hot-toast";

interface DocumentStatisticsProps {
  className?: string;
}

interface StatisticsData {
  total_documents: number;
  total_size_mb: number;
  avg_document_size_mb: number;
  expiring_soon: number;
  category_breakdown: Record<string, number>;
}

interface StorageData {
  total_bytes: number;
  total_mb: number;
  object_count: number;
}

const DocumentStatistics: React.FC<DocumentStatisticsProps> = ({ className = "" }) => {
  const [statistics, setStatistics] = useState<StatisticsData | null>(null);
  const [storage, setStorage] = useState<StorageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateFilter, setDateFilter] = useState({
    date_from: "",
    date_to: "",
  });

  const loadStatistics = async () => {
    try {
      setLoading(true);
      setError(null);

      const [statsResponse, storageResponse] = await Promise.all([
        getDocumentStatistics(dateFilter.date_from || dateFilter.date_to ? dateFilter : undefined),
        getStorageUsage(),
      ]);

      if (statsResponse.success) {
        setStatistics(statsResponse.statistics);
      } else {
        throw new Error(statsResponse.error || "Failed to load statistics");
      }

      if (storageResponse.success) {
        setStorage(storageResponse.usage);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load statistics");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, [dateFilter]);

  const handleDateFilterChange = (field: string, value: string) => {
    setDateFilter(prev => ({ ...prev, [field]: value }));
  };

  const clearDateFilter = () => {
    setDateFilter({ date_from: "", date_to: "" });
  };

  if (loading) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="h-6 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-48 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
        <div className="text-center text-red-600">
          <AlertTriangle className="h-12 w-12 mx-auto mb-3" />
          <p className="text-sm">{error}</p>
          <button
            onClick={loadStatistics}
            className="mt-2 text-sm text-primary hover:text-primary-dark underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  const categoryColors: Record<string, string> = {
    resume: "bg-blue-500",
    contract: "bg-green-500",
    id_document: "bg-purple-500",
    certificate: "bg-yellow-500",
    policy: "bg-gray-500",
    training: "bg-indigo-500",
    performance: "bg-pink-500",
    medical: "bg-red-500",
    legal: "bg-orange-500",
    other: "bg-gray-400",
  };

  const categoryLabels: Record<string, string> = {
    resume: "Resume/CV",
    contract: "Contract",
    id_document: "ID Document",
    certificate: "Certificate",
    policy: "Policy",
    training: "Training",
    performance: "Performance",
    medical: "Medical",
    legal: "Legal",
    other: "Other",
  };

  const totalCategoryDocuments = statistics ? Object.values(statistics.category_breakdown).reduce((sum, count) => sum + count, 0) : 0;

  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <BarChart3 className="h-6 w-6 text-primary mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">Document Statistics</h3>
          </div>
          
          {/* Date Filter */}
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <input
                type="date"
                value={dateFilter.date_from}
                onChange={(e) => handleDateFilterChange("date_from", e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="From"
              />
              <span className="text-gray-500">to</span>
              <input
                type="date"
                value={dateFilter.date_to}
                onChange={(e) => handleDateFilterChange("date_to", e.target.value)}
                className="text-sm border border-gray-300 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-primary"
                placeholder="To"
              />
              {(dateFilter.date_from || dateFilter.date_to) && (
                <button
                  onClick={clearDateFilter}
                  className="text-sm text-gray-500 hover:text-gray-700 underline"
                >
                  Clear
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center">
              <FileText className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-blue-900">Total Documents</p>
                <p className="text-2xl font-bold text-blue-600">{statistics?.total_documents || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center">
              <HardDrive className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-green-900">Total Size</p>
                <p className="text-2xl font-bold text-green-600">
                  {storage ? formatFileSize(storage.total_bytes) : "0 B"}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-yellow-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-yellow-900">Expiring Soon</p>
                <p className="text-2xl font-bold text-yellow-600">{statistics?.expiring_soon || 0}</p>
              </div>
            </div>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-purple-900">Avg Size</p>
                <p className="text-2xl font-bold text-purple-600">
                  {statistics?.avg_document_size_mb ? `${statistics.avg_document_size_mb.toFixed(2)} MB` : "0 MB"}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Category Breakdown */}
        {statistics && Object.keys(statistics.category_breakdown).length > 0 && (
          <div>
            <h4 className="text-lg font-semibold text-gray-900 mb-4">Documents by Category</h4>
            <div className="space-y-3">
              {Object.entries(statistics.category_breakdown)
                .sort(([,a], [,b]) => b - a)
                .map(([category, count]) => {
                  const percentage = totalCategoryDocuments > 0 ? (count / totalCategoryDocuments) * 100 : 0;
                  return (
                    <div key={category} className="flex items-center">
                      <div className="flex items-center min-w-0 flex-1">
                        <div className={`w-4 h-4 rounded ${categoryColors[category] || categoryColors.other} mr-3 flex-shrink-0`}></div>
                        <span className="text-sm font-medium text-gray-900 truncate">
                          {categoryLabels[category] || category}
                        </span>
                      </div>
                      <div className="flex items-center ml-4">
                        <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                          <div
                            className={`h-2 rounded-full ${categoryColors[category] || categoryColors.other}`}
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                        <span className="text-sm text-gray-600 w-12 text-right">{count}</span>
                        <span className="text-xs text-gray-500 w-12 text-right">
                          ({percentage.toFixed(1)}%)
                        </span>
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>
        )}

        {/* Empty State */}
        {statistics && statistics.total_documents === 0 && (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-gray-500">No documents found for the selected period</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentStatistics;
