"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  Document,
  DocumentFilters as DocumentFiltersType,
  FolderTreeItem,
  Folder,
} from "@/types/document";
import {
  getDocuments,
  getExpiringDocuments,
  downloadDocument,
  deleteDocument,
} from "@/lib/documents";
import { getEmployees } from "@/lib/employee";
import { getFolderDocuments, deleteFolder } from "@/lib/folders";
import toast from "react-hot-toast";
import { CollapsibleDocumentFilters } from "./DocumentFilters";
import DocumentUploadModal from "./DocumentUploadModal";
import DocumentDetailsModal from "./DocumentDetailsModal";
import DeleteConfirmationModal from "./DeleteConfirmationModal";
import FolderTree from "./FolderTree";
import CreateFolderModal from "./CreateFolderModal";
import EditFolderModal from "./EditFolderModal";
import FolderStatisticsModal from "./FolderStatisticsModal";
import FolderBreadcrumb from "./FolderBreadcrumb";
import DocumentStatistics from "./DocumentStatistics";
import BulkDocumentOperations from "./BulkDocumentOperations";
import SelectableDocumentCard from "./SelectableDocumentCard";
import { DragDropProvider, DropZone, useDragDropHandlers } from "./DragDropProvider";
import DraggableDocumentCard from "./DraggableDocumentCard";
import {
  Plus,
  Grid,
  List,
  BarChart3,
  FolderPlus,
  Folder as FolderIcon,
  Settings,
  Search,
  Filter,
  CheckSquare,
  AlertTriangle,
} from "lucide-react";

interface DocumentManagementContentProps {
  onItemMoved?: () => void;
}

const DocumentManagementContent: React.FC<DocumentManagementContentProps> = ({ onItemMoved }) => {
  const { user, companies } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [expiringDocuments, setExpiringDocuments] = useState<Document[]>([]);
  const [employees, setEmployees] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [documentToDelete, setDocumentToDelete] = useState<Document | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [filters, setFilters] = useState<DocumentFiltersType>({});
  const [pagination, setPagination] = useState<any>(null);

  // New folder-related state
  const [currentFolder, setCurrentFolder] = useState<FolderTreeItem | null>(null);
  const [isCreateFolderModalOpen, setIsCreateFolderModalOpen] = useState(false);
  const [isEditFolderModalOpen, setIsEditFolderModalOpen] = useState(false);
  const [isStatisticsModalOpen, setIsStatisticsModalOpen] = useState(false);
  const [parentFolderForCreation, setParentFolderForCreation] = useState<string | undefined>();
  const [folderToEdit, setFolderToEdit] = useState<FolderTreeItem | null>(null);
  const [folderForStatistics, setFolderForStatistics] = useState<FolderTreeItem | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showStatistics, setShowStatistics] = useState(false);
  const [showFolderTree, setShowFolderTree] = useState(true);
  const [folderToDelete, setFolderToDelete] = useState<FolderTreeItem | null>(null);

  // Bulk operations state
  const [selectedDocuments, setSelectedDocuments] = useState<Document[]>([]);
  const [bulkOperationsMode, setBulkOperationsMode] = useState(false);

  // Folder tree refresh trigger
  const [folderTreeRefreshTrigger, setFolderTreeRefreshTrigger] = useState(0);

  const { onDrop } = useDragDropHandlers();

  // Function to refresh folder tree
  const refreshFolderTree = () => {
    setFolderTreeRefreshTrigger(prev => prev + 1);
  };

  // Load documents based on current folder
  const loadDocuments = async () => {
    if (!companies || companies.length === 0) {
      console.log("No companies available, skipping document load");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      let response;
      if (currentFolder) {
        // Load documents from specific folder
        response = await getFolderDocuments(currentFolder.folder_id, false);
        if (response.success) {
          setDocuments(response.documents);
          setPagination(null); // Folder documents don't have pagination
        } else {
          throw new Error(response.error || "Failed to load folder documents");
        }
      } else {
        // COMMENTED OUT: No longer loading all documents by default
        // Documents are now accessed only through folder navigation
        // This improves performance and encourages organized document management
        /*
        response = await getDocuments(filters);
        if (response.success) {
          setDocuments(response.documents);
          setPagination(response.pagination);
        } else {
          throw new Error(response.error || "Failed to load documents");
        }
        */

        // Set empty documents when no folder is selected
        setDocuments([]);
        setPagination(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load documents");
    } finally {
      setLoading(false);
    }
  };

  const loadExpiringDocuments = async () => {
    if (!companies || companies.length === 0) {
      console.log("No companies available, skipping expiring documents load");
      return;
    }

    try {
      const response = await getExpiringDocuments(30);
      if (response.success) {
        setExpiringDocuments(response.documents);
      }
    } catch (err) {
      console.error("Error loading expiring documents:", err);
    }
  };

  const loadEmployees = async () => {
    if (!companies || companies.length === 0) {
      console.log("No companies available, skipping employee load");
      return;
    }

    try {
      const companyId = companies[0].company_id;
      if (!companyId) {
        console.error("Company ID is undefined");
        return;
      }

      const response = await getEmployees(companyId);
      setEmployees(response);
    } catch (err: any) {
      console.error("Failed to load employees:", err);
      toast.error("Failed to load employees");
    }
  };

  useEffect(() => {
    if (companies && companies.length > 0) {
      loadDocuments();
    }
  }, [currentFolder, filters, companies]);

  useEffect(() => {
    if (companies && companies.length > 0) {
      loadExpiringDocuments();
      loadEmployees();
    }
  }, [companies]);

  // Event handlers
  const handleFolderSelect = (folder: FolderTreeItem | null) => {
    setCurrentFolder(folder);
    setFilters({}); // Reset filters when changing folders
  };

  const handleCreateFolder = (parentFolderId?: string) => {
    setParentFolderForCreation(parentFolderId);
    setIsCreateFolderModalOpen(true);
  };

  const handleFolderCreated = (folder: Folder) => {
    toast.success("Folder created successfully");
    // Refresh the folder tree and documents
    refreshFolderTree();
    loadDocuments();
  };

  const handleEditFolder = (folder: FolderTreeItem) => {
    setFolderToEdit(folder);
    setIsEditFolderModalOpen(true);
  };

  const handleFolderUpdated = (folder: Folder) => {
    toast.success("Folder updated successfully");
    // Refresh the folder tree and documents
    refreshFolderTree();
    loadDocuments();
  };

  const handleViewStatistics = (folder: FolderTreeItem) => {
    setFolderForStatistics(folder);
    setIsStatisticsModalOpen(true);
  };

  // Bulk operations handlers
  const handleDocumentSelection = (document: Document, selected: boolean) => {
    if (selected) {
      setSelectedDocuments(prev => [...prev, document]);
    } else {
      setSelectedDocuments(prev => prev.filter(d => d.document_id !== document.document_id));
    }
  };

  const handleBulkOperationComplete = () => {
    setSelectedDocuments([]);
    refreshFolderTree();
    loadDocuments();
  };

  const toggleBulkMode = () => {
    setBulkOperationsMode(!bulkOperationsMode);
    setSelectedDocuments([]);
  };

  const handleDeleteFolder = (folder: FolderTreeItem) => {
    setFolderToDelete(folder);
  };

  const handleConfirmDeleteFolder = async () => {
    if (!folderToDelete) return;

    try {
      setIsDeleting(true);
      await deleteFolder(folderToDelete.folder_id);
      toast.success("Folder deleted successfully");
      setFolderToDelete(null);

      // If we're currently viewing the deleted folder, go back to root
      if (currentFolder?.folder_id === folderToDelete.folder_id) {
        setCurrentFolder(null);
      }

      // Refresh folder tree and documents
      refreshFolderTree();
      loadDocuments();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete folder");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleMoveFolder = (folder: FolderTreeItem) => {
    // TODO: Implement folder moving
    toast.error("Folder moving coming soon");
  };

  const handleDownload = async (document: Document) => {
    try {
      await downloadDocument(document.document_id, document.document_name);
      toast.success("Download started");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to download document");
    }
  };

  const handleView = (document: Document) => {
    setSelectedDocumentId(document.document_id);
  };

  const handleDelete = (document: Document) => {
    setDocumentToDelete(document);
  };

  const handleConfirmDelete = async () => {
    if (!documentToDelete) return;

    try {
      setIsDeleting(true);
      await deleteDocument(documentToDelete.document_id);
      toast.success("Document deleted successfully");
      setDocumentToDelete(null);
      loadDocuments();
      loadExpiringDocuments();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete document");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setDocumentToDelete(null);
    setFolderToDelete(null);
  };

  const handleUploadSuccess = () => {
    loadDocuments();
    loadExpiringDocuments();
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const handleItemMoved = () => {
    refreshFolderTree();
    loadDocuments();
    onItemMoved?.();
  };

  const handleDropOnFolder = (dropResult: any) => {
    onDrop(dropResult);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Document Management</h1>
          <p className="text-gray-600 mt-1 text-sm sm:text-base">
            Manage and organize your company documents
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-2">
          {/* Mobile folder tree toggle */}
          <button
            onClick={() => setShowFolderTree(!showFolderTree)}
            className="sm:hidden p-2 border border-gray-300 rounded-md text-gray-600 hover:bg-gray-100 transition-colors"
          >
            <FolderIcon className="h-4 w-4" />
          </button>

          {/* View Mode Toggle */}
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${viewMode === 'grid' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <Grid className="h-4 w-4" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 ${viewMode === 'list' ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-100'} transition-colors`}
            >
              <List className="h-4 w-4" />
            </button>
          </div>

          {/* Statistics Toggle */}
          <button
            onClick={() => setShowStatistics(!showStatistics)}
            className={`p-2 border border-gray-300 rounded-md transition-colors ${
              showStatistics ? 'bg-primary text-white' : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <BarChart3 className="h-4 w-4" />
          </button>

          {/* Create Folder */}
          <button
            onClick={() => handleCreateFolder(currentFolder?.folder_id)}
            className="inline-flex items-center px-3 py-2 bg-secondary text-white rounded-md hover:bg-secondary-dark transition-colors text-sm"
          >
            <FolderPlus className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">New Folder</span>
          </button>

          {/* Bulk Operations Toggle */}
          <button
            onClick={toggleBulkMode}
            className={`inline-flex items-center px-4 py-2 rounded-md transition-colors text-sm ${
              bulkOperationsMode
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <CheckSquare className="h-4 w-4 mr-2" />
            <span className="hidden sm:inline">
              {bulkOperationsMode ? 'Exit Bulk Mode' : 'Bulk Select'}
            </span>
          </button>

          {/* Upload Document */}
          <button
            onClick={() => setIsUploadModalOpen(true)}
            className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors text-sm"
          >
            <Plus className="h-5 w-5 mr-2" />
            <span className="hidden sm:inline">Upload Document</span>
          </button>
        </div>
      </div>

      {/* Statistics Panel */}
      {showStatistics && (
        <DocumentStatistics className="mb-6" />
      )}

      {/* Main Content */}
      <div className="flex flex-col lg:flex-row gap-8 h-[calc(100vh-280px)] min-h-[600px]">
        {/* Sidebar - Folder Tree */}
        {showFolderTree && (
          <div className="w-full lg:w-80 order-2 lg:order-1 flex flex-col">
            {/* Folder Tree Header */}
            <div className="flex-shrink-0 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                <FolderIcon className="h-5 w-5 mr-2 text-primary" />
                Folders
              </h3>
              <p className="text-sm text-gray-600 mt-1">Organize your documents</p>
            </div>
            {/* Scrollable Folder Tree */}
            <div className="flex-1 overflow-y-auto border border-gray-200 rounded-xl bg-white shadow-sm">
              <FolderTree
                onFolderSelect={handleFolderSelect}
                onCreateFolder={handleCreateFolder}
                onEditFolder={handleEditFolder}
                onDeleteFolder={handleDeleteFolder}
                onMoveFolder={handleMoveFolder}
                onViewStatistics={handleViewStatistics}
                selectedFolderId={currentFolder?.folder_id}
                showDocuments={false}
                allowDragDrop={true}
                companyId={companies && companies.length > 0 ? companies[0].company_id : undefined}
                refreshTrigger={folderTreeRefreshTrigger}
              />
            </div>
          </div>
        )}

        {/* Main Content Area */}
        <div className={`flex-1 order-1 lg:order-2 ${showFolderTree ? '' : 'w-full'} flex flex-col`}>
          {/* Fixed Header Section */}
          <div className="flex-shrink-0 space-y-6 mb-6">
            {/* Breadcrumb */}
            <div className="bg-white rounded-xl border border-gray-200 p-4 shadow-sm">
              <FolderBreadcrumb
                currentFolderId={currentFolder?.folder_id}
                onNavigate={(folderId) => {
                  if (folderId) {
                    // Find the folder in the tree and set it as current
                    // For now, we'll just clear the current folder if navigating to root
                    setCurrentFolder(null);
                  } else {
                    setCurrentFolder(null);
                  }
                }}
              />
            </div>

            {/* Filters */}
            <div className="bg-white rounded-xl border border-gray-200 shadow-sm">
              <CollapsibleDocumentFilters
                filters={filters}
                onFiltersChange={setFilters}
                employees={employees}
                defaultExpanded={false}
              />
            </div>
          </div>

          {/* Scrollable Documents Area */}
          <div className="flex-1 overflow-y-auto">
            <DropZone
              onDrop={handleDropOnFolder}
              dropResult={{ type: "folder", folderId: currentFolder?.folder_id }}
              className="relative h-full bg-white rounded-xl border border-gray-200 shadow-sm p-6"
            >
            {loading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8">
                {[...Array(8)].map((_, i) => (
                  <div key={i} className="bg-gray-50 rounded-xl border border-gray-100 p-6 animate-pulse">
                    <div className="h-40 bg-gray-200 rounded-lg"></div>
                  </div>
                ))}
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center max-w-md">
                  <div className="bg-red-50 border border-red-200 rounded-xl p-8">
                    <div className="text-red-600 mb-4">
                      <AlertTriangle className="h-12 w-12 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold mb-2">Error Loading Documents</h3>
                      <p className="text-sm">{error}</p>
                    </div>
                    <button
                      onClick={loadDocuments}
                      className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                    >
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            ) : documents.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center max-w-md">
                  {currentFolder ? (
                    <div>
                      <FolderIcon className="h-16 w-16 mx-auto mb-6 text-gray-300" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">No Documents Found</h3>
                      <p className="text-gray-500 mb-6">This folder is empty. Upload your first document to get started.</p>
                      <button
                        onClick={() => setIsUploadModalOpen(true)}
                        className="inline-flex items-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
                      >
                        <Plus className="h-5 w-5 mr-2" />
                        Upload Document
                      </button>
                    </div>
                  ) : (
                    <div>
                      <FolderIcon className="h-16 w-16 mx-auto mb-6 text-gray-300" />
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">Select a Folder</h3>
                      <p className="text-gray-500 mb-6">Choose a folder from the sidebar to view documents, or create a new folder to organize your files.</p>
                      <button
                        onClick={() => handleCreateFolder()}
                        className="inline-flex items-center px-6 py-3 bg-secondary text-white rounded-lg hover:bg-secondary-dark transition-colors font-medium"
                      >
                        <FolderPlus className="h-5 w-5 mr-2" />
                        Create Folder
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <>
                {/* Bulk Operations Bar */}
                {bulkOperationsMode && (
                  <BulkDocumentOperations
                    selectedDocuments={selectedDocuments}
                    onSelectionChange={setSelectedDocuments}
                    onBulkOperationComplete={handleBulkOperationComplete}
                    allDocuments={documents}
                    className="mb-6"
                  />
                )}

                <div className={viewMode === 'grid'
                  ? "grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-8"
                  : "space-y-6"
                }>
                {documents.map((document) =>
                  bulkOperationsMode ? (
                    <SelectableDocumentCard
                      key={document.document_id}
                      document={document}
                      onDownload={handleDownload}
                      onView={handleView}
                      onDelete={handleDelete}
                      showEmployeeInfo={true}
                      isSelected={selectedDocuments.some(d => d.document_id === document.document_id)}
                      onSelectionChange={handleDocumentSelection}
                      allowSelection={true}
                    />
                  ) : (
                    <DraggableDocumentCard
                      key={document.document_id}
                      document={document}
                      onDownload={handleDownload}
                      onView={handleView}
                      onDelete={handleDelete}
                      showEmployeeInfo={true}
                      allowDragDrop={true}
                    />
                  )
                )}
                </div>
              </>
            )}

            {/* Pagination */}
            {pagination && pagination.total_pages > 1 && (
              <div className="flex justify-center mt-8 pt-6 border-t border-gray-200">
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-600">
                    Page {pagination.current_page} of {pagination.total_pages}
                  </span>
                  <div className="flex space-x-2">
                    {[...Array(pagination.total_pages)].map((_, i) => (
                      <button
                        key={i}
                        onClick={() => handlePageChange(i + 1)}
                        className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                          pagination.current_page === i + 1
                            ? 'bg-primary text-white shadow-sm'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {i + 1}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
            </DropZone>
          </div>
        </div>
      </div>

      {/* Modals */}
      <DocumentUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        onSuccess={handleUploadSuccess}
        employees={employees}
        currentFolderId={currentFolder?.folder_id}
      />

      <CreateFolderModal
        isOpen={isCreateFolderModalOpen}
        onClose={() => setIsCreateFolderModalOpen(false)}
        onSuccess={handleFolderCreated}
        parentFolderId={parentFolderForCreation}
        parentFolderName={currentFolder?.folder_name}
      />

      <EditFolderModal
        isOpen={isEditFolderModalOpen}
        onClose={() => setIsEditFolderModalOpen(false)}
        onSuccess={handleFolderUpdated}
        folder={folderToEdit!}
      />

      <FolderStatisticsModal
        isOpen={isStatisticsModalOpen}
        onClose={() => setIsStatisticsModalOpen(false)}
        folder={folderForStatistics!}
      />

      {selectedDocumentId && (
        <DocumentDetailsModal
          isOpen={!!selectedDocumentId}
          onClose={() => setSelectedDocumentId(null)}
          documentId={selectedDocumentId}
          onDelete={() => {
            loadDocuments();
            loadExpiringDocuments();
          }}
        />
      )}

      <DeleteConfirmationModal
        isOpen={!!(documentToDelete || folderToDelete)}
        onClose={handleCancelDelete}
        onConfirm={documentToDelete ? handleConfirmDelete : handleConfirmDeleteFolder}
        document={documentToDelete}
        folder={folderToDelete}
        isDeleting={isDeleting}
      />
    </div>
  );
};

const EnhancedDocumentManagement: React.FC = () => {
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const handleItemMoved = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  return (
    <DragDropProvider onItemMoved={handleItemMoved}>
      <DocumentManagementContent onItemMoved={handleItemMoved} />
    </DragDropProvider>
  );
};

export default EnhancedDocumentManagement;
