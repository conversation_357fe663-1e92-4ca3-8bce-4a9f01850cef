"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, Folder, <PERSON><PERSON>, Edit3 } from "lucide-react";
import { UpdateFolderRequest, Folder as FolderType, FolderTreeItem } from "@/types/document";
import { updateFolder } from "@/lib/folders";
import toast from "react-hot-toast";

interface EditFolderModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (folder: FolderType) => void;
  folder: FolderTreeItem;
}

const FOLDER_COLORS = [
  { name: "Blue", value: "#2196F3", class: "bg-blue-500" },
  { name: "Green", value: "#4CAF50", class: "bg-green-500" },
  { name: "Orange", value: "#FF9800", class: "bg-orange-500" },
  { name: "Purple", value: "#9C27B0", class: "bg-purple-500" },
  { name: "<PERSON>", value: "#F44336", class: "bg-red-500" },
  { name: "<PERSON>", value: "#607D8B", class: "bg-gray-500" },
];

const FOLDER_ICONS = [
  { name: "Folder", value: "folder" },
  { name: "Documents", value: "file-text" },
  { name: "Images", value: "image" },
  { name: "Archive", value: "archive" },
  { name: "Settings", value: "settings" },
  { name: "Users", value: "users" },
  { name: "Lock", value: "lock" },
  { name: "Star", value: "star" },
];

const EditFolderModal: React.FC<EditFolderModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  folder,
}) => {
  const [formData, setFormData] = useState<UpdateFolderRequest>({
    folder_name: "",
    description: "",
    color: "",
    icon: "folder",
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (isOpen && folder) {
      setFormData({
        folder_name: folder.folder_name || "",
        description: folder.description || "",
        color: folder.color || "",
        icon: folder.icon || "folder",
      });
      setErrors({});
    }
  }, [isOpen, folder]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.folder_name?.trim()) {
      newErrors.folder_name = "Folder name is required";
    } else if (formData.folder_name.length < 2) {
      newErrors.folder_name = "Folder name must be at least 2 characters";
    } else if (formData.folder_name.length > 100) {
      newErrors.folder_name = "Folder name must be less than 100 characters";
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = "Description must be less than 500 characters";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const response = await updateFolder(folder.folder_id, formData);
      
      if (response.success && response.folder) {
        toast.success("Folder updated successfully");
        onSuccess(response.folder);
        onClose();
      } else {
        toast.error(response.error || "Failed to update folder");
      }
    } catch (error) {
      console.error("Error updating folder:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update folder");
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateFolderRequest, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  if (!isOpen || !folder) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Edit3 className="h-6 w-6 text-primary mr-3" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Edit Folder</h2>
              <p className="text-sm text-gray-600">{folder.folder_name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Folder Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Folder Name *
            </label>
            <input
              type="text"
              value={formData.folder_name}
              onChange={(e) => handleInputChange("folder_name", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary ${
                errors.folder_name ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter folder name"
              maxLength={100}
            />
            {errors.folder_name && (
              <p className="text-red-600 text-sm mt-1">{errors.folder_name}</p>
            )}
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary ${
                errors.description ? "border-red-300" : "border-gray-300"
              }`}
              placeholder="Enter folder description (optional)"
              rows={3}
              maxLength={500}
            />
            {errors.description && (
              <p className="text-red-600 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          {/* Color Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Palette className="h-4 w-4 inline mr-1" />
              Folder Color
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                type="button"
                onClick={() => handleInputChange("color", "")}
                className={`w-8 h-8 rounded-full border-2 transition-all ${
                  !formData.color ? "border-primary ring-2 ring-primary/20" : "border-gray-300"
                } bg-gray-100`}
                title="Default"
              />
              {FOLDER_COLORS.map((color) => (
                <button
                  key={color.value}
                  type="button"
                  onClick={() => handleInputChange("color", color.value)}
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    formData.color === color.value ? "border-primary ring-2 ring-primary/20" : "border-gray-300"
                  } ${color.class}`}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Icon Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Folder className="h-4 w-4 inline mr-1" />
              Folder Icon
            </label>
            <div className="grid grid-cols-4 gap-2">
              {FOLDER_ICONS.map((icon) => (
                <button
                  key={icon.value}
                  type="button"
                  onClick={() => handleInputChange("icon", icon.value)}
                  className={`p-2 rounded-md border transition-all text-center ${
                    formData.icon === icon.value 
                      ? "border-primary bg-primary/10 text-primary" 
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title={icon.name}
                >
                  <div className="text-xs">{icon.name}</div>
                </button>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? "Updating..." : "Update Folder"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditFolderModal;
