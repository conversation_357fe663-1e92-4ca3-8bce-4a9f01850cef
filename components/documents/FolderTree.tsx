"use client";

import React, { useState, useEffect, useCallback } from "react";
import {
  Folder,
  FolderOpen,
  File,
  ChevronRight,
  ChevronDown,
  Plus,
  Edit,
  Trash2,
  Move,
  Lock,
  BarChart3
} from "lucide-react";
import { FolderTreeItem, Document } from "@/types/document";
import { getFolderTree } from "@/lib/folders";
import { DropZone, useDragDropHandlers } from "./DragDropProvider";

interface FolderTreeProps {
  onFolderSelect?: (folder: FolderTreeItem | null) => void;
  onDocumentSelect?: (document: Document) => void;
  onCreateFolder?: (parentFolderId?: string) => void;
  onEditFolder?: (folder: FolderTreeItem) => void;
  onDeleteFolder?: (folder: FolderTreeItem) => void;
  onMoveFolder?: (folder: FolderTreeItem) => void;
  onViewStatistics?: (folder: FolderTreeItem) => void;
  selectedFolderId?: string;
  className?: string;
  showDocuments?: boolean;
  allowDragDrop?: boolean;
  companyId?: string;
  refreshTrigger?: number; // Add refresh trigger prop
}

interface FolderNodeProps {
  folder: FolderTreeItem;
  level: number;
  onFolderSelect?: (folder: FolderTreeItem | null) => void;
  onDocumentSelect?: (document: Document) => void;
  onCreateFolder?: (parentFolderId?: string) => void;
  onEditFolder?: (folder: FolderTreeItem) => void;
  onDeleteFolder?: (folder: FolderTreeItem) => void;
  onMoveFolder?: (folder: FolderTreeItem) => void;
  onViewStatistics?: (folder: FolderTreeItem) => void;
  selectedFolderId?: string;
  showDocuments?: boolean;
  allowDragDrop?: boolean;
}

const FolderNode: React.FC<FolderNodeProps> = ({
  folder,
  level,
  onFolderSelect,
  onDocumentSelect,
  onCreateFolder,
  onEditFolder,
  onDeleteFolder,
  onMoveFolder,
  onViewStatistics,
  selectedFolderId,
  showDocuments = false,
  allowDragDrop = false,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showActions, setShowActions] = useState(false);

  const hasChildren = folder.subfolders.length > 0 || (showDocuments && folder.documents && folder.documents.length > 0);
  const isSelected = selectedFolderId === folder.folder_id;

  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  const handleFolderClick = () => {
    onFolderSelect?.(folder);
  };

  const handleDocumentClick = (document: Document) => {
    onDocumentSelect?.(document);
  };

  // Use drag-drop handlers from context
  const { onDrop } = useDragDropHandlers();

  const handleFolderDrop = (dropResult: any) => {
    onDrop(dropResult);
  };

  return (
    <div className="select-none">
      {/* Folder Row */}
      <DropZone
        onDrop={handleFolderDrop}
        dropResult={{ type: "folder", folderId: folder.folder_id }}
        disabled={!allowDragDrop}
        className="relative"
      >
        <div
          className={`flex items-center py-3 px-3 rounded-lg cursor-pointer transition-all duration-200 group relative ${
            isSelected
              ? "bg-primary/10 text-primary border border-primary/20 shadow-sm"
              : "hover:bg-gray-50 hover:shadow-sm"
          }`}
          style={{ paddingLeft: `${level * 24 + 12}px` }}
          onClick={handleFolderClick}
          onMouseEnter={() => setShowActions(true)}
          onMouseLeave={() => setShowActions(false)}
        >
        {/* Expand/Collapse Button */}
        {hasChildren && (
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleToggle();
            }}
            className="mr-1 p-1 hover:bg-gray-200 rounded transition-colors"
          >
            {isExpanded ? (
              <ChevronDown className="h-4 w-4 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 text-gray-500" />
            )}
          </button>
        )}

        {/* Folder Icon */}
        <div className="mr-2 flex-shrink-0">
          {folder.is_private ? (
            <Lock className="h-4 w-4 text-gray-500" />
          ) : isExpanded ? (
            <FolderOpen className="h-4 w-4 text-blue-500" />
          ) : (
            <Folder className="h-4 w-4 text-blue-500" />
          )}
        </div>

        {/* Folder Name */}
        <span className="flex-1 text-sm font-medium text-gray-900 truncate">
          {folder.folder_name}
        </span>

        {/* Document Count */}
        {folder.document_count > 0 && (
          <span className="text-xs text-gray-500 mr-2">
            {folder.document_count}
          </span>
        )}

        {/* Folder Color Indicator */}
        {folder.color && (
          <div 
            className="w-3 h-3 rounded-full mr-2 flex-shrink-0"
            style={{ backgroundColor: folder.color }}
          />
        )}

        {/* Actions Menu */}
        {showActions && (
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onCreateFolder?.(folder.folder_id);
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="Create subfolder"
            >
              <Plus className="h-3 w-3 text-gray-500" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEditFolder?.(folder);
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="Edit folder"
            >
              <Edit className="h-3 w-3 text-gray-500" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onViewStatistics?.(folder);
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="View folder statistics"
            >
              <BarChart3 className="h-3 w-3 text-gray-500" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onMoveFolder?.(folder);
              }}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              title="Move folder"
            >
              <Move className="h-3 w-3 text-gray-500" />
            </button>
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDeleteFolder?.(folder);
              }}
              className="p-1 hover:bg-red-200 rounded transition-colors"
              title="Delete folder"
            >
              <Trash2 className="h-3 w-3 text-red-500" />
            </button>
          </div>
        )}
        </div>
      </DropZone>

      {/* Children */}
      {isExpanded && hasChildren && (
        <div className="ml-4">
          {/* Subfolders */}
          {folder.subfolders.map((subfolder) => (
            <FolderNode
              key={subfolder.folder_id}
              folder={subfolder}
              level={level + 1}
              onFolderSelect={onFolderSelect}
              onDocumentSelect={onDocumentSelect}
              onCreateFolder={onCreateFolder}
              onEditFolder={onEditFolder}
              onDeleteFolder={onDeleteFolder}
              onMoveFolder={onMoveFolder}
              onViewStatistics={onViewStatistics}
              selectedFolderId={selectedFolderId}
              showDocuments={showDocuments}
              allowDragDrop={allowDragDrop}
            />
          ))}

          {/* Documents */}
          {showDocuments && folder.documents && folder.documents.map((document) => (
            <div
              key={document.document_id}
              className="flex items-center py-1 px-2 ml-4 rounded-md cursor-pointer hover:bg-gray-50 transition-colors"
              style={{ paddingLeft: `${(level + 1) * 20 + 8}px` }}
              onClick={() => handleDocumentClick(document)}
              draggable={allowDragDrop}
              onDragStart={(e) => {
                if (!allowDragDrop) return;
                e.dataTransfer.setData("application/json", JSON.stringify({
                  type: "document",
                  id: document.document_id,
                  name: document.document_name
                }));
              }}
            >
              <File className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
              <span className="text-sm text-gray-700 truncate">
                {document.document_name}
              </span>
              <span className="text-xs text-gray-500 ml-auto">
                {document.file_size_mb.toFixed(2)} MB
              </span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const FolderTree: React.FC<FolderTreeProps> = ({
  onFolderSelect,
  onDocumentSelect,
  onCreateFolder,
  onEditFolder,
  onDeleteFolder,
  onMoveFolder,
  onViewStatistics,
  selectedFolderId,
  className = "",
  showDocuments = false,
  allowDragDrop = false,
  companyId,
  refreshTrigger = 0,
}) => {
  const [folderTree, setFolderTree] = useState<FolderTreeItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadFolderTree = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getFolderTree(showDocuments, companyId);
      if (response.success) {
        setFolderTree(response.tree);
      } else {
        setError(response.error || "Failed to load folder tree");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to load folder tree");
    } finally {
      setLoading(false);
    }
  }, [showDocuments, companyId]);

  useEffect(() => {
    loadFolderTree();
  }, [showDocuments, refreshTrigger, companyId, loadFolderTree]);

  if (loading) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-4 ${className}`}>
        <div className="animate-pulse space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-8 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-white rounded-xl border border-gray-200 p-4 ${className}`}>
        <div className="text-center text-red-600">
          <p className="text-sm">{error}</p>
          <button
            onClick={loadFolderTree}
            className="mt-2 text-sm text-primary hover:text-primary-dark underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Folders</h3>
        <button
          onClick={() => onCreateFolder?.()}
          className="inline-flex items-center px-4 py-2 text-sm bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
        >
          <Plus className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">New Folder</span>
          <span className="sm:hidden">New</span>
        </button>
      </div>

      {/* Tree Content */}
      <div className="p-6 max-h-80 sm:max-h-96 overflow-y-auto">
        {folderTree.length === 0 ? (
          <div className="text-center text-gray-500 py-12">
            <Folder className="h-16 w-16 mx-auto mb-4 text-gray-300" />
            <h4 className="text-lg font-semibold text-gray-900 mb-2">No Folders Yet</h4>
            <p className="text-sm text-gray-600 mb-6">Create your first folder to organize documents</p>
            <button
              onClick={() => onCreateFolder?.()}
              className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              Create Folder
            </button>
          </div>
        ) : (
          <div className="space-y-1">
            {/* Root Level - COMMENTED OUT: Documents now accessed via folders only */}
            {/*
            <div
              className={`flex items-center py-2 px-2 rounded-md cursor-pointer transition-colors ${
                selectedFolderId === null ? "bg-primary/10 text-primary border border-primary/20" : "hover:bg-gray-50"
              }`}
              onClick={() => onFolderSelect?.(null)}
            >
              <Folder className="h-4 w-4 text-blue-500 mr-2" />
              <span className="text-sm font-medium text-gray-900">All Documents</span>
            </div>
            */}

            {/* Folder Tree */}
            {folderTree.map((folder) => (
              <FolderNode
                key={folder.folder_id}
                folder={folder}
                level={0}
                onFolderSelect={onFolderSelect}
                onDocumentSelect={onDocumentSelect}
                onCreateFolder={onCreateFolder}
                onEditFolder={onEditFolder}
                onDeleteFolder={onDeleteFolder}
                onMoveFolder={onMoveFolder}
                onViewStatistics={onViewStatistics}
                selectedFolderId={selectedFolderId}
                showDocuments={showDocuments}
                allowDragDrop={allowDragDrop}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default FolderTree;
