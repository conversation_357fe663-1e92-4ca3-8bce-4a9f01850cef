"use client";

import React from "react";
import { Document } from "@/types/document";
import { formatFileSize, getFileIcon } from "@/lib/documents";
import {
  Download,
  Eye,
  Trash2,
  Calendar,
  User,
  AlertTriangle,
  Folder,
  GripVertical,
} from "lucide-react";
import { Draggable } from "./DragDropProvider";

interface DraggableDocumentCardProps {
  document: Document;
  onDownload: (document: Document) => void;
  onView: (document: Document) => void;
  onDelete?: (document: Document) => void;
  showEmployeeInfo?: boolean;
  className?: string;
  allowDragDrop?: boolean;
}

const DraggableDocumentCard: React.FC<DraggableDocumentCardProps> = ({
  document,
  onDownload,
  onView,
  onDelete,
  showEmployeeInfo = false,
  className = "",
  allowDragDrop = false,
}) => {
  const FileIcon = getFileIcon(document.file_type);

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      resume: "bg-blue-100 text-blue-800",
      contract: "bg-green-100 text-green-800",
      id_document: "bg-purple-100 text-purple-800",
      certificate: "bg-yellow-100 text-yellow-800",
      policy: "bg-gray-100 text-gray-800",
      training: "bg-indigo-100 text-indigo-800",
      performance: "bg-pink-100 text-pink-800",
      medical: "bg-red-100 text-red-800",
      legal: "bg-orange-100 text-orange-800",
      other: "bg-gray-100 text-gray-800",
    };
    return colors[category] || colors["other"];
  };

  const getExpiryStatus = () => {
    if (!document.expiry_date) return null;
    
    if (document.is_expired) {
      return { color: "text-red-600", label: "Expired" };
    } else if (document.days_until_expiry !== undefined && document.days_until_expiry <= 30) {
      return { 
        color: "text-yellow-600", 
        label: `Expires in ${document.days_until_expiry} days` 
      };
    }
    return null;
  };

  const expiryStatus = getExpiryStatus();

  const dragItem = {
    type: "document" as const,
    id: document.document_id,
    name: document.document_name,
  };

  const cardContent = (
    <div className={`bg-white rounded-xl border border-gray-200 overflow-hidden hover:border-primary/30 hover:shadow-lg transition-all duration-300 group ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-100">
        <div className="flex items-start justify-between">
          <div className="flex items-center min-w-0 flex-1">
            {allowDragDrop && (
              <div className="mr-3 opacity-0 group-hover:opacity-100 transition-opacity cursor-grab active:cursor-grabbing">
                <GripVertical className="h-4 w-4 text-gray-400" />
              </div>
            )}
            <FileIcon className="h-10 w-10 text-primary mr-4 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="text-base font-semibold text-gray-900 truncate mb-1">
                {document.document_name}
              </h3>
              <p className="text-sm text-gray-500">
                {formatFileSize(document.file_size_bytes)}
              </p>
            </div>
          </div>

          {/* Category Badge */}
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${getCategoryColor(document.document_category)}`}>
            {document.document_category}
          </span>
        </div>
      </div>

      {/* Content */}
      <div className="p-6 space-y-4">
        {/* Description */}
        {document.document_description && (
          <p className="text-sm text-gray-600 line-clamp-2 leading-relaxed">
            {document.document_description}
          </p>
        )}

        {/* Metadata Grid */}
        <div className="space-y-3">
          {/* Folder Info */}
          {document.folder_name && (
            <div className="flex items-center text-sm text-gray-500">
              <Folder className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{document.folder_path || document.folder_name}</span>
            </div>
          )}

          {/* Employee Info */}
          {showEmployeeInfo && document.employee_name && (
            <div className="flex items-center text-sm text-gray-500">
              <User className="h-4 w-4 mr-2 flex-shrink-0" />
              <span className="truncate">{document.employee_name}</span>
            </div>
          )}

          {/* Upload Date */}
          <div className="flex items-center text-sm text-gray-500">
            <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
            <span>
              Uploaded {new Date(document.uploaded_at).toLocaleDateString()}
            </span>
          </div>
        </div>

        {/* Expiry Warning */}
        {expiryStatus && (
          <div className={`flex items-center text-sm ${expiryStatus.color} bg-yellow-50 border border-yellow-200 rounded-lg p-3`}>
            <AlertTriangle className="h-4 w-4 mr-2 flex-shrink-0" />
            <span className="font-medium">{expiryStatus.label}</span>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex space-x-3">
            <button
              onClick={() => onView(document)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200"
            >
              <Eye className="h-4 w-4 mr-2" />
              View
            </button>
            <button
              onClick={() => onDownload(document)}
              className="inline-flex items-center px-4 py-2 text-sm font-medium text-primary bg-primary/10 border border-primary/20 rounded-lg hover:bg-primary/20 hover:border-primary/30 transition-all duration-200"
            >
              <Download className="h-4 w-4 mr-2" />
              Download
            </button>
          </div>

          {onDelete && (
            <button
              onClick={() => onDelete(document)}
              className="inline-flex items-center px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 hover:text-red-700 rounded-lg transition-all duration-200"
              title="Delete document"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );

  if (allowDragDrop) {
    return (
      <Draggable item={dragItem} className="cursor-grab active:cursor-grabbing">
        {cardContent}
      </Draggable>
    );
  }

  return cardContent;
};

export default DraggableDocumentCard;
