"use client";

import React from "react";
import { Document } from "@/types/document";
import { formatFileSize, getFileIcon } from "@/lib/documents";
import {
  Download,
  Eye,
  Trash2,
  Calendar,
  User,
  AlertTriangle,
  Folder,
  CheckSquare,
  Square,
} from "lucide-react";

interface SelectableDocumentCardProps {
  document: Document;
  onDownload: (document: Document) => void;
  onView: (document: Document) => void;
  onDelete?: (document: Document) => void;
  showEmployeeInfo?: boolean;
  className?: string;
  isSelected?: boolean;
  onSelectionChange?: (document: Document, selected: boolean) => void;
  allowSelection?: boolean;
}

const SelectableDocumentCard: React.FC<SelectableDocumentCardProps> = ({
  document,
  onDownload,
  onView,
  onDelete,
  showEmployeeInfo = false,
  className = "",
  isSelected = false,
  onSelectionChange,
  allowSelection = false,
}) => {
  const FileIcon = getFileIcon(document.file_type);
  const isExpiring = document.expiry_date && new Date(document.expiry_date) <= new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  const isExpired = document.expiry_date && new Date(document.expiry_date) < new Date();

  const handleSelectionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onSelectionChange) {
      onSelectionChange(document, !isSelected);
    }
  };

  const handleCardClick = () => {
    if (allowSelection && onSelectionChange) {
      onSelectionChange(document, !isSelected);
    } else {
      onView(document);
    }
  };

  return (
    <div 
      className={`bg-white rounded-xl border border-gray-200 hover:border-gray-300 transition-all duration-200 hover:shadow-md group cursor-pointer ${
        isSelected ? 'ring-2 ring-blue-500 border-blue-300' : ''
      } ${className}`}
      onClick={handleCardClick}
    >
      {/* Selection Checkbox */}
      {allowSelection && (
        <div className="absolute top-3 left-3 z-10">
          <button
            onClick={handleSelectionClick}
            className="p-1 rounded-md bg-white shadow-sm border border-gray-200 hover:bg-gray-50 transition-colors"
          >
            {isSelected ? (
              <CheckSquare className="h-4 w-4 text-blue-600" />
            ) : (
              <Square className="h-4 w-4 text-gray-400" />
            )}
          </button>
        </div>
      )}

      {/* Document Preview */}
      <div className="relative p-6 pb-4">
        <div className="flex items-center justify-center h-32 bg-gray-50 rounded-lg mb-4 relative">
          <FileIcon className="h-16 w-16 text-gray-400" />
          
          {/* Status Indicators */}
          {isExpired && (
            <div className="absolute top-2 right-2 bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-medium">
              Expired
            </div>
          )}
          {isExpiring && !isExpired && (
            <div className="absolute top-2 right-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium">
              Expiring Soon
            </div>
          )}
        </div>

        {/* Document Info */}
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900 text-sm line-clamp-2 group-hover:text-primary transition-colors">
            {document.document_name}
          </h3>
          
          {document.document_description && (
            <p className="text-xs text-gray-600 line-clamp-2">
              {document.document_description}
            </p>
          )}

          <div className="flex items-center justify-between text-xs text-gray-500">
            <span className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              {new Date(document.uploaded_at).toLocaleDateString()}
            </span>
            <span>{formatFileSize(document.file_size_mb)}</span>
          </div>

          {showEmployeeInfo && document.employee_name && (
            <div className="flex items-center text-xs text-gray-500">
              <User className="h-3 w-3 mr-1" />
              <span>{document.employee_name}</span>
            </div>
          )}

          {document.folder_name && (
            <div className="flex items-center text-xs text-gray-500">
              <Folder className="h-3 w-3 mr-1" />
              <span>{document.folder_name}</span>
            </div>
          )}

          {document.document_category && (
            <div className="inline-block">
              <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {document.document_category}
              </span>
            </div>
          )}

          {document.expiry_date && (
            <div className={`flex items-center text-xs ${
              isExpired ? 'text-red-600' : isExpiring ? 'text-yellow-600' : 'text-gray-500'
            }`}>
              {(isExpired || isExpiring) && <AlertTriangle className="h-3 w-3 mr-1" />}
              <span>
                Expires: {new Date(document.expiry_date).toLocaleDateString()}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Actions */}
      <div className="px-6 pb-4">
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <div className="flex space-x-2">
            <button
              onClick={(e) => {
                e.stopPropagation();
                onView(document);
              }}
              className="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="View details"
            >
              <Eye className="h-4 w-4" />
            </button>
            
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDownload(document);
              }}
              className="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-md transition-colors"
              title="Download"
            >
              <Download className="h-4 w-4" />
            </button>
          </div>

          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete(document);
              }}
              className="p-2 text-gray-500 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
              title="Delete"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default SelectableDocumentCard;
