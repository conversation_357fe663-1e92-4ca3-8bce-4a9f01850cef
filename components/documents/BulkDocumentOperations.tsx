"use client";

import React, { useState } from "react";
import { 
  CheckSquare, 
  Square, 
  Trash2, 
  FolderOpen, 
  X, 
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Folder
} from "lucide-react";
import { Document, FolderTreeItem } from "@/types/document";
import { moveDocumentsToFolder } from "@/lib/folders";
import { deleteDocument } from "@/lib/documents";
import { getFolderTree } from "@/lib/folders";
import toast from "react-hot-toast";

interface BulkDocumentOperationsProps {
  selectedDocuments: Document[];
  onSelectionChange: (documents: Document[]) => void;
  onBulkOperationComplete: () => void;
  allDocuments: Document[];
  className?: string;
}

interface FolderSelectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectFolder: (folderId: string) => void;
  selectedDocuments: Document[];
}

const FolderSelectModal: React.FC<FolderSelectModalProps> = ({
  isOpen,
  onClose,
  onSelectFolder,
  selectedDocuments,
}) => {
  const [folders, setFolders] = useState<FolderTreeItem[]>([]);
  const [loading, setLoading] = useState(false);

  React.useEffect(() => {
    if (isOpen) {
      loadFolders();
    }
  }, [isOpen]);

  const loadFolders = async () => {
    try {
      setLoading(true);
      const response = await getFolderTree(false);
      if (response.success && response.tree) {
        setFolders(response.tree);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
      toast.error("Failed to load folders");
    } finally {
      setLoading(false);
    }
  };

  const renderFolder = (folder: FolderTreeItem, level: number = 0) => (
    <div key={folder.folder_id}>
      <button
        onClick={() => onSelectFolder(folder.folder_id)}
        className="w-full text-left px-3 py-2 hover:bg-gray-100 rounded-md transition-colors flex items-center"
        style={{ paddingLeft: `${12 + level * 20}px` }}
      >
        <Folder className="h-4 w-4 text-gray-500 mr-2" />
        <span className="text-sm">{folder.folder_name}</span>
      </button>
      {folder.subfolders?.map((subfolder) => renderFolder(subfolder, level + 1))}
    </div>
  );

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-md w-full max-h-[70vh] overflow-hidden">
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            Move {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''} to folder
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-5 w-5" />
          </button>
        </div>
        
        <div className="p-4 max-h-96 overflow-y-auto">
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-sm text-gray-500 mt-2">Loading folders...</p>
            </div>
          ) : folders.length > 0 ? (
            <div className="space-y-1">
              {folders.map((folder) => renderFolder(folder))}
            </div>
          ) : (
            <p className="text-center text-gray-500 py-8">No folders available</p>
          )}
        </div>
      </div>
    </div>
  );
};

const BulkDocumentOperations: React.FC<BulkDocumentOperationsProps> = ({
  selectedDocuments,
  onSelectionChange,
  onBulkOperationComplete,
  allDocuments,
  className = "",
}) => {
  const [showFolderModal, setShowFolderModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);

  const isAllSelected = allDocuments.length > 0 && selectedDocuments.length === allDocuments.length;
  const isPartiallySelected = selectedDocuments.length > 0 && selectedDocuments.length < allDocuments.length;

  const handleSelectAll = () => {
    if (isAllSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(allDocuments);
    }
  };

  const handleBulkMove = async (folderId: string) => {
    try {
      setIsProcessing(true);
      const documentIds = selectedDocuments.map(doc => doc.document_id);
      
      const response = await moveDocumentsToFolder(folderId, { document_ids: documentIds });
      
      if (response.success) {
        toast.success(`Successfully moved ${documentIds.length} document${documentIds.length !== 1 ? 's' : ''}`);
        onSelectionChange([]);
        onBulkOperationComplete();
      } else {
        toast.error(response.error || "Failed to move documents");
      }
    } catch (error) {
      console.error("Error moving documents:", error);
      toast.error(error instanceof Error ? error.message : "Failed to move documents");
    } finally {
      setIsProcessing(false);
      setShowFolderModal(false);
    }
  };

  const handleBulkDelete = async () => {
    try {
      setIsProcessing(true);
      let successCount = 0;
      let failCount = 0;

      for (const document of selectedDocuments) {
        try {
          await deleteDocument(document.document_id);
          successCount++;
        } catch (error) {
          console.error(`Failed to delete document ${document.document_id}:`, error);
          failCount++;
        }
      }

      if (successCount > 0) {
        toast.success(`Successfully deleted ${successCount} document${successCount !== 1 ? 's' : ''}`);
      }
      if (failCount > 0) {
        toast.error(`Failed to delete ${failCount} document${failCount !== 1 ? 's' : ''}`);
      }

      onSelectionChange([]);
      onBulkOperationComplete();
    } catch (error) {
      console.error("Error during bulk delete:", error);
      toast.error("Failed to delete documents");
    } finally {
      setIsProcessing(false);
      setShowDeleteConfirm(false);
    }
  };

  if (selectedDocuments.length === 0 && !isPartiallySelected) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <button
          onClick={handleSelectAll}
          className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-600 hover:text-gray-900 transition-colors"
          disabled={allDocuments.length === 0}
        >
          <Square className="h-4 w-4" />
          <span>Select All ({allDocuments.length})</span>
        </button>
      </div>
    );
  }

  return (
    <>
      <div className={`bg-blue-50 border border-blue-200 rounded-lg p-3 ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={handleSelectAll}
              className="flex items-center space-x-2 text-blue-700 hover:text-blue-900 transition-colors"
            >
              {isAllSelected ? (
                <CheckSquare className="h-4 w-4" />
              ) : (
                <Square className="h-4 w-4" />
              )}
              <span className="text-sm font-medium">
                {selectedDocuments.length} of {allDocuments.length} selected
              </span>
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowFolderModal(true)}
              disabled={isProcessing}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <FolderOpen className="h-4 w-4" />
              <span>Move to Folder</span>
            </button>

            <button
              onClick={() => setShowDeleteConfirm(true)}
              disabled={isProcessing}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
            >
              <Trash2 className="h-4 w-4" />
              <span>Delete</span>
            </button>

            <button
              onClick={() => onSelectionChange([])}
              className="p-1.5 text-gray-500 hover:text-gray-700 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      <FolderSelectModal
        isOpen={showFolderModal}
        onClose={() => setShowFolderModal(false)}
        onSelectFolder={handleBulkMove}
        selectedDocuments={selectedDocuments}
      />

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl max-w-md w-full">
            <div className="flex items-center justify-between p-4 border-b border-gray-200">
              <div className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                <h3 className="text-lg font-semibold text-gray-900">Confirm Bulk Delete</h3>
              </div>
              <button onClick={() => setShowDeleteConfirm(false)} className="text-gray-400 hover:text-gray-600">
                <X className="h-5 w-5" />
              </button>
            </div>
            
            <div className="p-4">
              <p className="text-gray-700 mb-4">
                Are you sure you want to delete {selectedDocuments.length} document{selectedDocuments.length !== 1 ? 's' : ''}? 
                This action cannot be undone.
              </p>
              
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  disabled={isProcessing}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleBulkDelete}
                  disabled={isProcessing}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50"
                >
                  {isProcessing ? "Deleting..." : "Delete"}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default BulkDocumentOperations;
