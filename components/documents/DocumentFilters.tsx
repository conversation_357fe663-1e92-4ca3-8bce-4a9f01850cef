'use client';

import React, { useState } from 'react';
import { DocumentFilters as DocumentFiltersType, DOCUMENT_CATEGORIES } from '@/types/document';
import { Search, Filter, X, ChevronDown, ChevronUp } from 'lucide-react';

interface DocumentFiltersProps {
  filters: DocumentFiltersType;
  onFiltersChange: (filters: DocumentFiltersType) => void;
  employees?: Array<{ employee_id: string; full_name: string }>;
  className?: string;
}

interface CollapsibleDocumentFiltersProps extends DocumentFiltersProps {
  defaultExpanded?: boolean;
}

const DocumentFilters: React.FC<DocumentFiltersProps> = ({
  filters,
  onFiltersChange,
  employees = [],
  className = ''
}) => {
  const handleFilterChange = (key: keyof DocumentFiltersType, value: any) => {
    onFiltersChange({
      ...filters,
      [key]: value,
      page: 1 // Reset to first page when filters change
    });
  };

  const clearFilters = () => {
    onFiltersChange({
      page: 1,
      limit: filters.limit
    });
  };

  const hasActiveFilters = Boolean(
    filters.search ||
    filters.category ||
    filters.employee_id ||
    (filters.expiry_status && filters.expiry_status !== 'all') ||
    filters.date_from ||
    filters.date_to
  );

  return (
    <div className={`bg-white rounded-xl border border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5 text-gray-500" />
          <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors"
          >
            <X className="h-4 w-4 mr-1" />
            Clear All
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        {/* Search */}
        <div className="col-span-full">
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-1">
            Search Documents
          </label>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              id="search"
              type="text"
              placeholder="Search by name or description..."
              value={filters.search || ''}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Category */}
        <div>
          <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
            Category
          </label>
          <select
            id="category"
            value={filters.category || ''}
            onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Categories</option>
            {DOCUMENT_CATEGORIES.map((category) => (
              <option key={category.value} value={category.value}>
                {category.label}
              </option>
            ))}
          </select>
        </div>

        {/* Employee */}
        {employees.length > 0 && (
          <div>
            <label htmlFor="employee" className="block text-sm font-medium text-gray-700 mb-1">
              Employee
            </label>
            <select
              id="employee"
              value={filters.employee_id || ''}
              onChange={(e) => handleFilterChange('employee_id', e.target.value || undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="">All Employees</option>
              {employees.map((employee) => (
                <option key={employee.employee_id} value={employee.employee_id}>
                  {employee.full_name}
                </option>
              ))}
            </select>
          </div>
        )}

        {/* Expiry Status */}
        <div>
          <label htmlFor="expiry_status" className="block text-sm font-medium text-gray-700 mb-1">
            Expiry Status
          </label>
          <select
            id="expiry_status"
            value={filters.expiry_status || 'all'}
            onChange={(e) => handleFilterChange('expiry_status', e.target.value as any)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="all">All Documents</option>
            <option value="valid">Valid Documents</option>
            <option value="expiring">Expiring Soon</option>
            <option value="expired">Expired Documents</option>
          </select>
        </div>

        {/* Date From */}
        <div>
          <label htmlFor="date_from" className="block text-sm font-medium text-gray-700 mb-1">
            From Date
          </label>
          <input
            id="date_from"
            type="date"
            value={filters.date_from || ''}
            onChange={(e) => handleFilterChange('date_from', e.target.value || undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>

        {/* Date To */}
        <div>
          <label htmlFor="date_to" className="block text-sm font-medium text-gray-700 mb-1">
            To Date
          </label>
          <input
            id="date_to"
            type="date"
            value={filters.date_to || ''}
            onChange={(e) => handleFilterChange('date_to', e.target.value || undefined)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-100">
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-light bg-opacity-10 text-primary">
                Search: "{filters.search}"
                <button
                  onClick={() => handleFilterChange('search', undefined)}
                  className="ml-1 hover:text-primary-dark"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )}
            
            {filters.category && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-light bg-opacity-10 text-primary">
                Category: {DOCUMENT_CATEGORIES.find(c => c.value === filters.category)?.label}
                <button
                  onClick={() => handleFilterChange('category', undefined)}
                  className="ml-1 hover:text-primary-dark"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )}
            
            {filters.expiry_status && filters.expiry_status !== 'all' && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-light bg-opacity-10 text-primary">
                Status: {filters.expiry_status}
                <button
                  onClick={() => handleFilterChange('expiry_status', 'all')}
                  className="ml-1 hover:text-primary-dark"
                >
                  <X className="h-3 w-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Collapsible wrapper component
const CollapsibleDocumentFilters: React.FC<CollapsibleDocumentFiltersProps> = ({
  defaultExpanded = false,
  ...props
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  const hasActiveFilters = Boolean(
    props.filters.search ||
    props.filters.category ||
    props.filters.employee_id ||
    (props.filters.expiry_status && props.filters.expiry_status !== 'all') ||
    props.filters.date_from ||
    props.filters.date_to
  );

  return (
    <div className={props.className}>
      {/* Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="flex items-center space-x-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
      >
        <Filter className="h-4 w-4 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">
          Filters
          {hasActiveFilters && (
            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              Active
            </span>
          )}
        </span>
        {isExpanded ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {/* Collapsible Content */}
      {isExpanded && (
        <div className="mt-4">
          <DocumentFilters {...props} />
        </div>
      )}
    </div>
  );
};

export default DocumentFilters;
export { CollapsibleDocumentFilters };
