"use client";

import React from 'react';
import {
  X,
  Edit,
  Power,
  PowerOff,
  Copy,
  Award,
  Gift,
  Target,
  Calendar,
  Users,
  Clock,
  Info,
  AlertTriangle,
} from 'lucide-react';
import {
  Promotion,
  getRuleTypeLabel,
  getRewardTypeLabel,
  getPromotionStatusColor,
  getPromotionStatusLabel,
  getRuleTypeColor,
  getRewardTypeColor,
  formatPromotionName,
  formatTriggerValue,
  formatRewardValue,
  formatPromotionPeriod,
  isPromotionActive,
  isPromotionExpired,
  getApplicableSegments,
  getApplicableVisitTypes,
} from '@/types/promotion';
import { getCustomerSegmentLabel } from '@/types/customer';
import { getVisitTypeLabel } from '@/types/customer-visit';

interface PromotionDetailsModalProps {
  promotion: Promotion;
  onClose: () => void;
  onEdit: () => void;
  onActivate: () => void;
  onDeactivate: () => void;
  onDuplicate: () => void;
}

const PromotionDetailsModal: React.FC<PromotionDetailsModalProps> = ({
  promotion,
  onClose,
  onEdit,
  onActivate,
  onDeactivate,
  onDuplicate,
}) => {
  const applicableSegments = getApplicableSegments(promotion);
  const applicableVisitTypes = getApplicableVisitTypes(promotion);
  const isActive = isPromotionActive(promotion);
  const isExpired = isPromotionExpired(promotion);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-primary-light flex items-center justify-center">
                <Award className="h-6 w-6 text-primary" />
              </div>
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-900">
                {formatPromotionName(promotion)}
              </h2>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPromotionStatusColor(promotion.is_active)}`}>
                  {getPromotionStatusLabel(promotion.is_active)}
                </span>
                {isExpired && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Expired
                  </span>
                )}
                {!isActive && !isExpired && promotion.is_active && (
                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <Clock className="h-3 w-3 mr-1" />
                    Scheduled
                  </span>
                )}
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Main Information */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Info className="h-5 w-5 mr-2" />
                  Basic Information
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Promotion ID</label>
                    <p className="mt-1 text-sm text-gray-900 font-mono">{promotion.rule_id}</p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Priority</label>
                    <p className="mt-1 text-sm text-gray-900">{promotion.priority}</p>
                  </div>
                  
                  {promotion.description && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-500">Description</label>
                      <p className="mt-1 text-sm text-gray-900">{promotion.description}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Rule Configuration */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Target className="h-5 w-5 mr-2" />
                  Rule Configuration
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Rule Type</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRuleTypeColor(promotion.rule_type)}`}>
                        {getRuleTypeLabel(promotion.rule_type)}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Trigger Condition</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatTriggerValue(promotion.rule_type, promotion.trigger_value)}
                    </p>
                  </div>
                  
                  {promotion.trigger_period_days && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Trigger Period</label>
                      <p className="mt-1 text-sm text-gray-900 flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-gray-400" />
                        {promotion.trigger_period_days} days
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Reward Configuration */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Gift className="h-5 w-5 mr-2" />
                  Reward Configuration
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Reward Type</label>
                    <div className="mt-1">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRewardTypeColor(promotion.reward_type)}`}>
                        {getRewardTypeLabel(promotion.reward_type)}
                      </span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Reward Value</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatRewardValue(promotion.reward_type, promotion.reward_value)}
                    </p>
                  </div>
                  
                  {promotion.reward_description && (
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-500">Reward Description</label>
                      <p className="mt-1 text-sm text-gray-900">{promotion.reward_description}</p>
                    </div>
                  )}
                  
                  {promotion.reward_expiry_days && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Reward Expiry</label>
                      <p className="mt-1 text-sm text-gray-900 flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-gray-400" />
                        {promotion.reward_expiry_days} days after earning
                      </p>
                    </div>
                  )}
                  
                  {promotion.max_redemptions_per_customer && (
                    <div>
                      <label className="block text-sm font-medium text-gray-500">Max Redemptions</label>
                      <p className="mt-1 text-sm text-gray-900">
                        {promotion.max_redemptions_per_customer} per customer
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Applicability */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Applicability
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Customer Segments</label>
                    <div className="mt-1">
                      {applicableSegments.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {applicableSegments.map((segment) => (
                            <span
                              key={segment}
                              className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {getCustomerSegmentLabel(segment as any)}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">All segments</p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Visit Types</label>
                    <div className="mt-1">
                      {applicableVisitTypes.length > 0 ? (
                        <div className="flex flex-wrap gap-1">
                          {applicableVisitTypes.map((visitType) => (
                            <span
                              key={visitType}
                              className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                            >
                              {getVisitTypeLabel(visitType as any)}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">All visit types</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Validity Period */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Validity Period
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Valid From</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {promotion.valid_from 
                        ? new Date(promotion.valid_from).toLocaleDateString()
                        : 'No start date'
                      }
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Valid Until</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {promotion.valid_until 
                        ? new Date(promotion.valid_until).toLocaleDateString()
                        : 'No end date'
                      }
                    </p>
                  </div>
                  
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500">Period Summary</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {formatPromotionPeriod(promotion)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Timestamps */}
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Timestamps
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Created At</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(promotion.created_at).toLocaleString()}
                    </p>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-500">Updated At</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {new Date(promotion.updated_at).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions Sidebar */}
            <div className="lg:col-span-1">
              <div className="bg-white border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-medium text-gray-900 mb-4">Actions</h3>
                
                <div className="space-y-3">
                  <button
                    onClick={onEdit}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Promotion
                  </button>
                  
                  <button
                    onClick={onDuplicate}
                    className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </button>
                  
                  {promotion.is_active ? (
                    <button
                      onClick={onDeactivate}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <PowerOff className="h-4 w-4 mr-2" />
                      Deactivate
                    </button>
                  ) : (
                    <button
                      onClick={onActivate}
                      className="w-full inline-flex items-center justify-center px-4 py-2 border border-green-300 rounded-md shadow-sm text-sm font-medium text-green-700 bg-white hover:bg-green-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <Power className="h-4 w-4 mr-2" />
                      Activate
                    </button>
                  )}
                </div>

                {/* Status Information */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Status Information</h4>
                  <div className="space-y-2 text-xs text-gray-600">
                    <div className="flex justify-between">
                      <span>Active:</span>
                      <span className={promotion.is_active ? 'text-green-600' : 'text-red-600'}>
                        {promotion.is_active ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Currently Active:</span>
                      <span className={isActive ? 'text-green-600' : 'text-red-600'}>
                        {isActive ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Expired:</span>
                      <span className={isExpired ? 'text-red-600' : 'text-green-600'}>
                        {isExpired ? 'Yes' : 'No'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default PromotionDetailsModal;
