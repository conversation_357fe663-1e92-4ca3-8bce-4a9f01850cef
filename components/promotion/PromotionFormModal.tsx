"use client";

import React, { useState, useEffect } from 'react';
import {
  X,
  Save,
  AlertCircle,
  Info,
  Calendar,
  Target,
  Gift,
  Users,
} from 'lucide-react';
import {
  createPromotion,
  updatePromotion,
} from '@/lib/promotion';
import {
  Promotion,
  CreatePromotionRequest,
  UpdatePromotionRequest,
  RuleType,
  RewardType,
  RULE_TYPES,
  REWARD_TYPES,
  getRuleTypeDescription,
  getRewardTypeDescription,
} from '@/types/promotion';
import { CUSTOMER_SEGMENTS } from '@/types/customer';
import { VISIT_TYPES } from '@/types/customer-visit';

interface PromotionFormModalProps {
  promotion?: Promotion; // If provided, this is edit mode
  onSuccess: () => void;
  onCancel: () => void;
}

const PromotionFormModal: React.FC<PromotionFormModalProps> = ({
  promotion,
  onSuccess,
  onCancel,
}) => {
  const isEditMode = !!promotion;

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    rule_type: 'visit_count' as RuleType,
    trigger_value: 1,
    trigger_period_days: '',
    reward_type: 'free_visit' as RewardType,
    reward_value: 1,
    reward_description: '',
    reward_expiry_days: '',
    applicable_customer_segments: '',
    applicable_visit_types: '',
    max_redemptions_per_customer: '',
    priority: 0,
    valid_from: '',
    valid_until: '',
  });

  // Validation state
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data for edit mode
  useEffect(() => {
    if (promotion) {
      setFormData({
        name: promotion.name || '',
        description: promotion.description || '',
        rule_type: promotion.rule_type,
        trigger_value: promotion.trigger_value,
        trigger_period_days: promotion.trigger_period_days?.toString() || '',
        reward_type: promotion.reward_type,
        reward_value: promotion.reward_value,
        reward_description: promotion.reward_description || '',
        reward_expiry_days: promotion.reward_expiry_days?.toString() || '',
        applicable_customer_segments: promotion.applicable_customer_segments || '',
        applicable_visit_types: promotion.applicable_visit_types || '',
        max_redemptions_per_customer: promotion.max_redemptions_per_customer?.toString() || '',
        priority: promotion.priority,
        valid_from: promotion.valid_from ? promotion.valid_from.split('T')[0] : '',
        valid_until: promotion.valid_until ? promotion.valid_until.split('T')[0] : '',
      });
    }
  }, [promotion]);

  // Handle form field changes
  const handleChange = (field: string, value: string | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.name.trim()) {
      newErrors.name = 'Promotion name is required';
    }

    if (formData.trigger_value <= 0) {
      newErrors.trigger_value = 'Trigger value must be greater than 0';
    }

    if (formData.reward_value <= 0) {
      newErrors.reward_value = 'Reward value must be greater than 0';
    }

    // Date validation
    if (formData.valid_from && formData.valid_until) {
      const fromDate = new Date(formData.valid_from);
      const untilDate = new Date(formData.valid_until);
      if (fromDate >= untilDate) {
        newErrors.valid_until = 'End date must be after start date';
      }
    }

    // Numeric field validation
    if (formData.trigger_period_days && parseInt(formData.trigger_period_days) <= 0) {
      newErrors.trigger_period_days = 'Trigger period must be greater than 0';
    }

    if (formData.reward_expiry_days && parseInt(formData.reward_expiry_days) <= 0) {
      newErrors.reward_expiry_days = 'Reward expiry days must be greater than 0';
    }

    if (formData.max_redemptions_per_customer && parseInt(formData.max_redemptions_per_customer) <= 0) {
      newErrors.max_redemptions_per_customer = 'Max redemptions must be greater than 0';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      if (isEditMode && promotion) {
        // Update existing promotion
        const updateData: UpdatePromotionRequest = {};
        
        // Only include changed fields
        if (formData.name !== promotion.name) updateData.name = formData.name;
        if (formData.description !== (promotion.description || '')) updateData.description = formData.description || undefined;
        if (formData.trigger_value !== promotion.trigger_value) updateData.trigger_value = formData.trigger_value;
        if (formData.reward_value !== promotion.reward_value) updateData.reward_value = formData.reward_value;
        if (formData.reward_description !== (promotion.reward_description || '')) updateData.reward_description = formData.reward_description || undefined;
        if (formData.applicable_customer_segments !== (promotion.applicable_customer_segments || '')) updateData.applicable_customer_segments = formData.applicable_customer_segments || undefined;
        if (formData.applicable_visit_types !== (promotion.applicable_visit_types || '')) updateData.applicable_visit_types = formData.applicable_visit_types || undefined;
        if (formData.priority !== promotion.priority) updateData.priority = formData.priority;

        // Handle numeric fields
        const triggerPeriodDays = formData.trigger_period_days ? parseInt(formData.trigger_period_days) : null;
        if (triggerPeriodDays !== promotion.trigger_period_days) updateData.trigger_period_days = triggerPeriodDays || undefined;

        const rewardExpiryDays = formData.reward_expiry_days ? parseInt(formData.reward_expiry_days) : null;
        if (rewardExpiryDays !== promotion.reward_expiry_days) updateData.reward_expiry_days = rewardExpiryDays || undefined;

        const maxRedemptions = formData.max_redemptions_per_customer ? parseInt(formData.max_redemptions_per_customer) : null;
        if (maxRedemptions !== promotion.max_redemptions_per_customer) updateData.max_redemptions_per_customer = maxRedemptions || undefined;

        // Handle dates
        const validFrom = formData.valid_from || null;
        const validUntil = formData.valid_until || null;
        if (validFrom !== (promotion.valid_from ? promotion.valid_from.split('T')[0] : null)) updateData.valid_from = validFrom || undefined;
        if (validUntil !== (promotion.valid_until ? promotion.valid_until.split('T')[0] : null)) updateData.valid_until = validUntil || undefined;

        await updatePromotion(promotion.rule_id, updateData);
      } else {
        // Create new promotion
        const createData: CreatePromotionRequest = {
          name: formData.name,
          description: formData.description || undefined,
          rule_type: formData.rule_type,
          trigger_value: formData.trigger_value,
          trigger_period_days: formData.trigger_period_days ? parseInt(formData.trigger_period_days) : undefined,
          reward_type: formData.reward_type,
          reward_value: formData.reward_value,
          reward_description: formData.reward_description || undefined,
          reward_expiry_days: formData.reward_expiry_days ? parseInt(formData.reward_expiry_days) : undefined,
          applicable_customer_segments: formData.applicable_customer_segments || undefined,
          applicable_visit_types: formData.applicable_visit_types || undefined,
          max_redemptions_per_customer: formData.max_redemptions_per_customer ? parseInt(formData.max_redemptions_per_customer) : undefined,
          priority: formData.priority,
          valid_from: formData.valid_from || undefined,
          valid_until: formData.valid_until || undefined,
        };

        await createPromotion(createData);
      }

      onSuccess();
    } catch (error) {
      console.error('Error saving promotion:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to save promotion' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-bold text-gray-900">
            {isEditMode ? 'Edit Promotion' : 'Create New Promotion'}
          </h2>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Info className="h-5 w-5 mr-2" />
                Basic Information
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Promotion Name *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => handleChange('name', e.target.value)}
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.name ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter promotion name"
                  />
                  {errors.name && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => handleChange('description', e.target.value)}
                    rows={3}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                    placeholder="Enter promotion description"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Priority
                  </label>
                  <input
                    type="number"
                    value={formData.priority}
                    onChange={(e) => handleChange('priority', parseInt(e.target.value) || 0)}
                    min="0"
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                  />
                  <p className="mt-1 text-xs text-gray-500">Higher numbers = higher priority</p>
                </div>
              </div>
            </div>

            {/* Rule Configuration */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Target className="h-5 w-5 mr-2" />
                Rule Configuration
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Rule Type *
                  </label>
                  <select
                    value={formData.rule_type}
                    onChange={(e) => handleChange('rule_type', e.target.value)}
                    disabled={isEditMode} // Can't change rule type in edit mode
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary disabled:bg-gray-100"
                  >
                    {RULE_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    {getRuleTypeDescription(formData.rule_type)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Trigger Value *
                  </label>
                  <input
                    type="number"
                    value={formData.trigger_value}
                    onChange={(e) => handleChange('trigger_value', parseInt(e.target.value) || 0)}
                    min="1"
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.trigger_value ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.trigger_value && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.trigger_value}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Trigger Period (Days)
                  </label>
                  <input
                    type="number"
                    value={formData.trigger_period_days}
                    onChange={(e) => handleChange('trigger_period_days', e.target.value)}
                    min="1"
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.trigger_period_days ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Optional"
                  />
                  {errors.trigger_period_days && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.trigger_period_days}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">Time window for the trigger condition</p>
                </div>
              </div>
            </div>

            {/* Reward Configuration */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Gift className="h-5 w-5 mr-2" />
                Reward Configuration
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reward Type *
                  </label>
                  <select
                    value={formData.reward_type}
                    onChange={(e) => handleChange('reward_type', e.target.value)}
                    disabled={isEditMode} // Can't change reward type in edit mode
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary disabled:bg-gray-100"
                  >
                    {REWARD_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    {getRewardTypeDescription(formData.reward_type)}
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reward Value *
                  </label>
                  <input
                    type="number"
                    value={formData.reward_value}
                    onChange={(e) => handleChange('reward_value', parseInt(e.target.value) || 0)}
                    min="1"
                    step={formData.reward_type === 'discount_percentage' ? '0.01' : '1'}
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.reward_value ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.reward_value && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.reward_value}
                    </p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reward Description
                  </label>
                  <input
                    type="text"
                    value={formData.reward_description}
                    onChange={(e) => handleChange('reward_description', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                    placeholder="Optional description of the reward"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reward Expiry (Days)
                  </label>
                  <input
                    type="number"
                    value={formData.reward_expiry_days}
                    onChange={(e) => handleChange('reward_expiry_days', e.target.value)}
                    min="1"
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.reward_expiry_days ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Optional"
                  />
                  {errors.reward_expiry_days && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.reward_expiry_days}
                    </p>
                  )}
                  <p className="mt-1 text-xs text-gray-500">How long the reward is valid after earning</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Max Redemptions per Customer
                  </label>
                  <input
                    type="number"
                    value={formData.max_redemptions_per_customer}
                    onChange={(e) => handleChange('max_redemptions_per_customer', e.target.value)}
                    min="1"
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.max_redemptions_per_customer ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Unlimited"
                  />
                  {errors.max_redemptions_per_customer && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.max_redemptions_per_customer}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Applicability */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Applicability
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Segments
                  </label>
                  <select
                    value={formData.applicable_customer_segments}
                    onChange={(e) => handleChange('applicable_customer_segments', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                  >
                    <option value="">All Segments</option>
                    {CUSTOMER_SEGMENTS.map((segment) => (
                      <option key={segment.value} value={segment.value}>
                        {segment.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Leave empty to apply to all customer segments</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Visit Types
                  </label>
                  <select
                    value={formData.applicable_visit_types}
                    onChange={(e) => handleChange('applicable_visit_types', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                  >
                    <option value="">All Visit Types</option>
                    {VISIT_TYPES.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Leave empty to apply to all visit types</p>
                </div>
              </div>
            </div>

            {/* Validity Period */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Validity Period
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valid From
                  </label>
                  <input
                    type="date"
                    value={formData.valid_from}
                    onChange={(e) => handleChange('valid_from', e.target.value)}
                    className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Valid Until
                  </label>
                  <input
                    type="date"
                    value={formData.valid_until}
                    onChange={(e) => handleChange('valid_until', e.target.value)}
                    className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary ${
                      errors.valid_until ? 'border-red-300' : 'border-gray-300'
                    }`}
                  />
                  {errors.valid_until && (
                    <p className="mt-1 text-sm text-red-600 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {errors.valid_until}
                    </p>
                  )}
                </div>
              </div>
              <p className="mt-2 text-xs text-gray-500">Leave empty for no expiration date</p>
            </div>

            {/* Submit Error */}
            {errors.submit && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <p className="text-sm text-red-600 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {errors.submit}
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? 'Update Promotion' : 'Create Promotion'}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default PromotionFormModal;
