'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import {
  OrgUnit,
  CreateOrgUnitRequest,
  UpdateOrgUnitRequest,
  DepartmentOption,
  ParentUnitOption,
  OrgUnitFormData,
} from '@/types/org-unit';
import { createOrgUnit, updateOrgUnit, getAvailableParentUnits } from '@/lib/org-units';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface OrgUnitFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  orgUnit?: OrgUnit | null;
  isEditing?: boolean;
  selectedDepartment?: DepartmentOption | null;
  selectedParentUnit?: ParentUnitOption | null;
}

const OrgUnitForm: React.FC<OrgUnitFormProps> = ({
  onSuccess,
  onCancel,
  orgUnit = null,
  isEditing = false,
  selectedDepartment = null,
  selectedParentUnit = null,
}) => {
  const { companies } = useAuth();
  const [formData, setFormData] = useState<OrgUnitFormData>({
    name: '',
    description: '',
    department_id: '',
    parent_id: '',
    level: 1,
    manager_id: '',
  });
  const [departments, setDepartments] = useState<Department[]>([]);
  const [parentUnits, setParentUnits] = useState<OrgUnit[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingParentUnits, setIsLoadingParentUnits] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Initialize form data if editing an existing unit
  useEffect(() => {
    if (isEditing && orgUnit) {
      setFormData({
        name: orgUnit.name,
        description: orgUnit.description || '',
        department_id: orgUnit.department_id,
        parent_id: orgUnit.parent_id || '',
        level: orgUnit.level,
        manager_id: orgUnit.manager_id || '',
      });
    } else if (selectedDepartment) {
      setFormData(prev => ({
        ...prev,
        department_id: selectedDepartment.department_id,
        level: selectedParentUnit ? selectedParentUnit.level + 1 : 1,
        parent_id: selectedParentUnit ? selectedParentUnit.org_unit_id : '',
      }));
    }
  }, [isEditing, orgUnit, selectedDepartment, selectedParentUnit]);

  // Fetch departments
  useEffect(() => {
    const fetchDepartments = async () => {
      try {
        setIsLoadingDepartments(true);
        const { apiGet } = await import('@/lib/api');
        const { getAccessToken } = await import('@/lib/auth');
        const token = getAccessToken();

        if (!token) {
          throw new Error('Authentication required');
        }

        const companyId = companies && companies.length > 0 ? companies[0].company_id : null;
        if (!companyId) {
          throw new Error('No company found');
        }

        const response = await apiGet<any>(`api/departments?company_id=${companyId}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });

        if (response.success && response.departments) {
          setDepartments(response.departments);
        }
      } catch (error) {
        console.error('Error fetching departments:', error);
      } finally {
        setIsLoadingDepartments(false);
      }
    };

    if (!isEditing) {
      fetchDepartments();
    }
  }, [companies, isEditing]);

  // Fetch parent units when department changes
  useEffect(() => {
    const fetchParentUnits = async () => {
      if (!formData.department_id) {
        setParentUnits([]);
        return;
      }

      try {
        setIsLoadingParentUnits(true);
        const units = await getAvailableParentUnits(formData.department_id);
        setParentUnits(units);
      } catch (error) {
        console.error('Error fetching parent units:', error);
        setParentUnits([]);
      } finally {
        setIsLoadingParentUnits(false);
      }
    };

    if (formData.department_id && !isEditing) {
      fetchParentUnits();
    }
  }, [formData.department_id, isEditing]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'parent_id') {
      const selectedParent = parentUnits.find(unit => unit.org_unit_id === value);
      const newLevel = selectedParent ? selectedParent.level + 1 : 1;
      
      setFormData({
        ...formData,
        [name]: value,
        level: newLevel,
      });
    } else {
      setFormData({
        ...formData,
        [name]: value,
      });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setSuccessMessage('');

    // Basic validation
    if (!formData.name.trim()) {
      setError('Unit name is required');
      setIsLoading(false);
      return;
    }

    if (!formData.department_id) {
      setError('Department selection is required');
      setIsLoading(false);
      return;
    }

    try {
      if (isEditing && orgUnit) {
        // Update existing unit
        const updateData: UpdateOrgUnitRequest = {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          manager_id: formData.manager_id || undefined,
        };

        await updateOrgUnit(orgUnit.org_unit_id, updateData);
        setSuccessMessage('Organizational unit updated successfully!');
      } else {
        // Create new unit
        const createData: CreateOrgUnitRequest = {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined,
          department_id: formData.department_id,
          parent_id: formData.parent_id || undefined,
          level: formData.level,
          manager_id: formData.manager_id || undefined,
        };

        await createOrgUnit(createData);
        setSuccessMessage('Organizational unit created successfully!');
      }

      // Reset the form if not editing
      if (!isEditing) {
        setFormData({
          name: '',
          description: '',
          department_id: selectedDepartment?.department_id || '',
          parent_id: selectedParentUnit?.org_unit_id || '',
          level: selectedParentUnit ? selectedParentUnit.level + 1 : 1,
          manager_id: '',
        });
      }

      // Call the onSuccess callback if provided
      if (onSuccess) {
        setTimeout(() => {
          onSuccess();
        }, 1500);
      }
    } catch (error: any) {
      console.error('Error saving organizational unit:', error);
      setError(error.message || 'An error occurred while saving the organizational unit');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md">
          {successMessage}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Unit Name */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-secondary-dark mb-1">
            Unit Name *
          </label>
          <input
            id="name"
            name="name"
            type="text"
            required
            value={formData.name}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter unit name"
          />
        </div>

        {/* Description */}
        <div>
          <label htmlFor="description" className="block text-sm font-medium text-secondary-dark mb-1">
            Description
          </label>
          <textarea
            id="description"
            name="description"
            rows={3}
            value={formData.description}
            onChange={handleChange}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            placeholder="Enter unit description (optional)"
          />
        </div>

        {/* Department Selection */}
        {!isEditing && (
          <div>
            <label htmlFor="department_id" className="block text-sm font-medium text-secondary-dark mb-1">
              Department *
            </label>
            <select
              id="department_id"
              name="department_id"
              required
              value={formData.department_id}
              onChange={handleChange}
              disabled={isLoadingDepartments || !!selectedDepartment}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="">Select a department</option>
              {departments.map((dept) => (
                <option key={dept.department_id} value={dept.department_id}>
                  {dept.name}
                </option>
              ))}
            </select>
            {isLoadingDepartments && (
              <p className="text-sm text-secondary mt-1">Loading departments...</p>
            )}
          </div>
        )}

        {/* Parent Unit Selection */}
        {!isEditing && formData.department_id && (
          <div>
            <label htmlFor="parent_id" className="block text-sm font-medium text-secondary-dark mb-1">
              Parent Unit (Optional)
            </label>
            <select
              id="parent_id"
              name="parent_id"
              value={formData.parent_id}
              onChange={handleChange}
              disabled={isLoadingParentUnits || !!selectedParentUnit}
              className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:bg-gray-100 disabled:cursor-not-allowed"
            >
              <option value="">No parent (Top-level unit)</option>
              {parentUnits.map((unit) => (
                <option key={unit.org_unit_id} value={unit.org_unit_id}>
                  {unit.name} (Level {unit.level})
                </option>
              ))}
            </select>
            {isLoadingParentUnits && (
              <p className="text-sm text-secondary mt-1">Loading parent units...</p>
            )}
            {formData.parent_id && (
              <p className="text-sm text-secondary mt-1">
                This will create a Level {formData.level} unit
              </p>
            )}
          </div>
        )}

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-4">
          {onCancel && (
            <button
              type="button"
              onClick={onCancel}
              disabled={isLoading}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Cancel
            </button>
          )}
          <button
            type="submit"
            disabled={isLoading}
            className="btn-primary py-2 px-4 text-sm font-medium rounded-md transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {isEditing ? 'Updating...' : 'Creating...'}
              </div>
            ) : (
              isEditing ? 'Update Unit' : 'Create Unit'
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default OrgUnitForm;
