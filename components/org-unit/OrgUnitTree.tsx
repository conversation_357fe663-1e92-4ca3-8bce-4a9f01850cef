'use client';

import React, { useState } from 'react';
import { 
  ChevronDown, 
  ChevronRight, 
  Building2, 
  Users, 
  Plus, 
  Edit, 
  Trash2,
  User
} from 'lucide-react';
import { DepartmentStructure, OrgUnitTreeItem, OrgUnit } from '@/types/org-unit';

interface OrgUnitTreeProps {
  departmentStructure: DepartmentStructure;
  onAddUnit: (departmentId: string, parentUnit?: OrgUnit) => void;
  onEditUnit: (unit: OrgUnit) => void;
  onDeleteUnit: (unit: OrgUnit) => void;
  isLoading?: boolean;
  showDepartmentHeader?: boolean;
}

interface TreeNodeProps {
  unit: OrgUnitTreeItem;
  level: number;
  departmentId: string;
  onAddUnit: (departmentId: string, parentUnit?: OrgUnit) => void;
  onEditUnit: (unit: OrgUnit) => void;
  onDeleteUnit: (unit: OrgUnit) => void;
}

const TreeNode: React.FC<TreeNodeProps> = ({
  unit,
  level,
  departmentId,
  onAddUnit,
  onEditUnit,
  onDeleteUnit,
}) => {
  const [isExpanded, setIsExpanded] = useState(true);
  const hasChildren = unit.children && unit.children.length > 0;
  
  // Calculate indentation based on level
  const indentationClass = level === 0 ? 'pl-4' : `pl-${Math.min(level * 6 + 4, 20)}`;
  
  const handleToggleExpand = () => {
    if (hasChildren) {
      setIsExpanded(!isExpanded);
    }
  };

  const handleAddSubUnit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onAddUnit(departmentId, unit as OrgUnit);
  };

  const handleEditUnit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEditUnit(unit as OrgUnit);
  };

  const handleDeleteUnit = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDeleteUnit(unit as OrgUnit);
  };

  return (
    <div className="border-l-2 border-gray-100">
      {/* Unit Row */}
      <div 
        className={`${indentationClass} py-3 border-b border-gray-50 hover:bg-gray-50 transition-colors cursor-pointer`}
        onClick={handleToggleExpand}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            {/* Expand/Collapse Icon */}
            <div className="w-5 h-5 flex items-center justify-center">
              {hasChildren ? (
                isExpanded ? (
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                ) : (
                  <ChevronRight className="w-4 h-4 text-gray-500" />
                )
              ) : (
                <div className="w-4 h-4" />
              )}
            </div>

            {/* Unit Icon */}
            <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
              <Users className="w-4 h-4 text-primary" />
            </div>

            {/* Unit Info */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <h4 className="font-medium text-gray-900">{unit.name}</h4>
                <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                  Level {unit.level}
                </span>
              </div>
              {unit.description && (
                <p className="text-sm text-gray-600 mt-1">{unit.description}</p>
              )}
              {unit.manager_id && (
                <div className="flex items-center space-x-1 mt-1">
                  <User className="w-3 h-3 text-gray-400" />
                  <span className="text-xs text-gray-500">Manager assigned</span>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
            <button
              onClick={handleAddSubUnit}
              className="p-1.5 text-gray-400 hover:text-primary hover:bg-primary/10 rounded-md transition-colors"
              title="Add Sub-Unit"
            >
              <Plus className="w-4 h-4" />
            </button>
            <button
              onClick={handleEditUnit}
              className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-md transition-colors"
              title="Edit Unit"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={handleDeleteUnit}
              className="p-1.5 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-md transition-colors"
              title="Delete Unit"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Children */}
      {hasChildren && isExpanded && (
        <div className="ml-4">
          {unit.children.map((childUnit) => (
            <TreeNode
              key={childUnit.org_unit_id}
              unit={childUnit}
              level={level + 1}
              departmentId={departmentId}
              onAddUnit={onAddUnit}
              onEditUnit={onEditUnit}
              onDeleteUnit={onDeleteUnit}
            />
          ))}
        </div>
      )}
    </div>
  );
};

const OrgUnitTree: React.FC<OrgUnitTreeProps> = ({
  departmentStructure,
  onAddUnit,
  onEditUnit,
  onDeleteUnit,
  isLoading = false,
  showDepartmentHeader = true,
}) => {
  if (isLoading) {
    return (
      <div className="py-8 text-center">
        <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <p className="mt-2 text-secondary">Loading organizational structure...</p>
      </div>
    );
  }

  if (!departmentStructure.units || departmentStructure.units.length === 0) {
    return (
      <div className="py-8 text-center">
        <Building2 className="w-12 h-12 text-gray-300 mx-auto mb-4" />
        <p className="text-secondary">No organizational units found in this department.</p>
        <button
          onClick={() => onAddUnit(departmentStructure.department_id)}
          className="mt-2 text-primary hover:text-primary-dark"
        >
          Add your first unit
        </button>
      </div>
    );
  }

  if (showDepartmentHeader) {
    return (
      <div className="bg-white rounded-lg border border-gray-200">
        {/* Department Header */}
        <div className="p-4 border-b border-gray-200 bg-gray-50/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <Building2 className="w-5 h-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{departmentStructure.department_name}</h3>
                <p className="text-sm text-gray-600">
                  {departmentStructure.units.length} unit{departmentStructure.units.length !== 1 ? 's' : ''}
                </p>
              </div>
            </div>
            <button
              onClick={() => onAddUnit(departmentStructure.department_id)}
              className="btn-primary py-2 px-3 text-sm font-medium rounded-md transition-all flex items-center"
            >
              <Plus className="w-4 h-4 mr-1" />
              Add Unit
            </button>
          </div>
        </div>

        {/* Tree Structure */}
        <div className="group">
          {departmentStructure.units.map((unit) => (
            <TreeNode
              key={unit.org_unit_id}
              unit={unit}
              level={0}
              departmentId={departmentStructure.department_id}
              onAddUnit={onAddUnit}
              onEditUnit={onEditUnit}
              onDeleteUnit={onDeleteUnit}
            />
          ))}
        </div>
      </div>
    );
  }

  // Simplified version without department header for embedded use
  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Users className="w-5 h-5 text-primary" />
          <span className="text-sm font-medium text-gray-700">
            {departmentStructure.units.length} organizational unit{departmentStructure.units.length !== 1 ? 's' : ''}
          </span>
        </div>
        <button
          onClick={() => onAddUnit(departmentStructure.department_id)}
          className="bg-primary text-white px-3 py-1 text-sm rounded-md hover:bg-primary-dark transition-colors flex items-center"
        >
          <Plus className="w-4 h-4 mr-1" />
          Add Unit
        </button>
      </div>

      <div className="space-y-1">
        {departmentStructure.units.map((unit) => (
          <TreeNode
            key={unit.org_unit_id}
            unit={unit}
            level={0}
            departmentId={departmentStructure.department_id}
            onAddUnit={onAddUnit}
            onEditUnit={onEditUnit}
            onDeleteUnit={onDeleteUnit}
          />
        ))}
      </div>
    </div>
  );
};

export default OrgUnitTree;
