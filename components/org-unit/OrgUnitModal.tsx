'use client';

import React from 'react';
import { X } from 'lucide-react';
import OrgUnitForm from './OrgUnitForm';
import { OrgUnit, DepartmentOption, ParentUnitOption } from '@/types/org-unit';

interface OrgUnitModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  orgUnit?: OrgUnit | null;
  isEditing?: boolean;
  selectedDepartment?: DepartmentOption | null;
  selectedParentUnit?: ParentUnitOption | null;
}

const OrgUnitModal: React.FC<OrgUnitModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  orgUnit = null,
  isEditing = false,
  selectedDepartment = null,
  selectedParentUnit = null,
}) => {
  if (!isOpen) return null;

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const getModalTitle = () => {
    if (isEditing) {
      return 'Edit Organizational Unit';
    }
    
    if (selectedParentUnit) {
      return `Add Sub-Unit to ${selectedParentUnit.name}`;
    }
    
    if (selectedDepartment) {
      return `Add Unit to ${selectedDepartment.name}`;
    }
    
    return 'Add Organizational Unit';
  };

  const getModalSubtitle = () => {
    if (isEditing && orgUnit) {
      return `Editing: ${orgUnit.name}`;
    }
    
    if (selectedParentUnit) {
      return `This will create a Level ${selectedParentUnit.level + 1} unit`;
    }
    
    if (selectedDepartment) {
      return 'This will create a Level 1 unit';
    }
    
    return 'Create a new organizational unit';
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-xl border border-gray-200 w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50/50">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {getModalTitle()}
            </h2>
            <p className="text-sm text-secondary mt-1">
              {getModalSubtitle()}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-lg p-1 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Modal Body */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
          <OrgUnitForm
            onSuccess={onSuccess}
            onCancel={onClose}
            orgUnit={orgUnit}
            isEditing={isEditing}
            selectedDepartment={selectedDepartment}
            selectedParentUnit={selectedParentUnit}
          />
        </div>
      </div>
    </div>
  );
};

export default OrgUnitModal;
