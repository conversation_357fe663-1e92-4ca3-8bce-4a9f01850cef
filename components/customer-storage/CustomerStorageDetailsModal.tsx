"use client";

import React from "react";
import {
  X,
  Package,
  Calendar,
  User,
  MapPin,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  FileText,
  Archive,
  Eye,
} from "lucide-react";
import {
  CustomerStorage,
  getStorageStatusLabel,
  getStorageStatusColor,
  getStorageDuration,
  formatStorageDuration,
  isStorageActive,
  isStorageOverdue,
} from "@/types/customer-storage";

interface CustomerStorageDetailsModalProps {
  customerStorage: CustomerStorage;
  onClose: () => void;
}

const CustomerStorageDetailsModal: React.FC<
  CustomerStorageDetailsModalProps
> = ({ customerStorage, onClose }) => {
  const status = customerStorage.status;
  const duration = getStorageDuration(customerStorage);
  const isActive = isStorageActive(customerStorage);
  const isOverdue = isStorageOverdue(customerStorage);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Package className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Storage Record Details
              </h2>
              <p className="text-sm text-gray-600">
                {customerStorage.customer.first_name}{" "}
                {customerStorage.customer.last_name} -{" "}
                {customerStorage.location.location_number}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Banner */}
          <div
            className={`p-4 rounded-lg border ${
              status === "stored"
                ? "bg-blue-50 border-blue-200"
                : status === "retrieved"
                ? "bg-green-50 border-green-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div className="flex items-center">
              {status === "stored" ? (
                <Archive className="w-5 h-5 text-blue-600 mr-3" />
              ) : status === "retrieved" ? (
                <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600 mr-3" />
              )}
              <div>
                <h3
                  className={`font-medium ${
                    status === "stored"
                      ? "text-blue-800"
                      : status === "retrieved"
                      ? "text-green-800"
                      : "text-red-800"
                  }`}
                >
                  {getStorageStatusLabel(status)}
                </h3>
                <p
                  className={`text-sm mt-1 ${
                    status === "stored"
                      ? "text-blue-700"
                      : status === "retrieved"
                      ? "text-green-700"
                      : "text-red-700"
                  }`}
                >
                  {status === "stored"
                    ? `Item has been stored for ${formatStorageDuration(
                        duration
                      )}`
                    : status === "retrieved"
                    ? `Item was retrieved after ${formatStorageDuration(
                        duration
                      )}`
                    : `Item was abandoned after ${formatStorageDuration(
                        duration
                      )}`}
                </p>
              </div>
            </div>
          </div>

          {/* Overdue Warning */}
          {isOverdue && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                </div>
                <div className="ml-3">
                  <h4 className="text-sm font-medium text-red-900">
                    Overdue Storage
                  </h4>
                  <p className="text-sm text-red-700 mt-1">
                    This item has been stored for{" "}
                    {formatStorageDuration(duration)}, which exceeds the typical
                    storage period. Consider contacting the customer for
                    retrieval.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Customer Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Name
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <User className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    {customerStorage.customer.first_name}{" "}
                    {customerStorage.customer.last_name}
                  </span>
                </div>
              </div>

              {/* Customer Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-900">
                    {customerStorage.customer.email}
                  </span>
                </div>
              </div>

              {/* Customer Phone */}
              {customerStorage.customer.phone_number && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-900">
                      {customerStorage.customer.phone_number}
                    </span>
                  </div>
                </div>
              )}

              {/* Customer Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <User className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    {customerStorage.customer.first_name}{" "}
                    {customerStorage.customer.last_name}
                  </span>
                  {customerStorage.customer.email && (
                    <span className="text-sm text-gray-500 ml-2">
                      ({customerStorage.customer.email})
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Storage Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Storage Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Storage Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Location
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <MapPin className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {customerStorage.location.location_number}
                    </div>
                    {customerStorage.location.notes && (
                      <div className="text-xs text-gray-500">
                        {customerStorage.location.notes}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Stored Date */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stored Date
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(customerStorage.stored_at).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(customerStorage.stored_at).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Duration */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Duration
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <span
                    className={`text-sm ${
                      isOverdue ? "text-red-600 font-medium" : "text-gray-900"
                    }`}
                  >
                    {formatStorageDuration(duration)}
                  </span>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Current Status
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStorageStatusColor(
                      status
                    )}`}
                  >
                    {getStorageStatusLabel(status)}
                  </span>
                </div>
              </div>

              {/* Storage Reference */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Storage Reference
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Package className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    Location #{customerStorage.location.location_number}
                  </span>
                  <span className="text-sm text-gray-500 ml-2">
                    • {new Date(customerStorage.stored_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* Visit Information */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Associated Visit
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    Visit on{" "}
                    {new Date(customerStorage.stored_at).toLocaleDateString()}
                  </span>
                  <span className="text-sm text-gray-500 ml-2">
                    • {new Date(customerStorage.stored_at).toLocaleTimeString()}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Item Description */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Item Description
            </h3>
            <div className="p-4 bg-gray-50 rounded-lg">
              <div className="flex items-start">
                <Package className="w-4 h-4 text-gray-500 mr-2 mt-0.5" />
                <p className="text-sm text-gray-900 leading-relaxed">
                  {customerStorage.items_description}
                </p>
              </div>
            </div>
          </div>

          {/* Visit Information */}
          {customerStorage.visit && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Related Visit Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Visit Date
                  </label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                    <div>
                      <div className="text-sm text-gray-900">
                        {new Date(
                          customerStorage.visit.visit_date
                        ).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(
                          customerStorage.visit.visit_time
                        ).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Visit Type
                  </label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <span className="text-sm text-gray-900 capitalize">
                      {customerStorage.visit.visit_type}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Retrieval Information */}
          {customerStorage.retrieved_at && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Retrieval Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Retrieved Date
                  </label>
                  <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                    <div>
                      <div className="text-sm text-gray-900">
                        {new Date(
                          customerStorage.retrieved_at
                        ).toLocaleDateString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {new Date(
                          customerStorage.retrieved_at
                        ).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          {customerStorage.notes && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start">
                  <FileText className="w-4 h-4 text-gray-500 mr-2 mt-0.5" />
                  <p className="text-sm text-gray-900 leading-relaxed">
                    {customerStorage.notes}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Record Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Record Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Created At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Created At
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(
                        customerStorage.created_at
                      ).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(
                        customerStorage.created_at
                      ).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Updated At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Updated
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(
                        customerStorage.updated_at
                      ).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(
                        customerStorage.updated_at
                      ).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerStorageDetailsModal;
