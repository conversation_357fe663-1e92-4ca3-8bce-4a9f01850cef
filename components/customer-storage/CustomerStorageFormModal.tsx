"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  Package,
  AlertCircle,
  User,
  MapPin,
  Calendar,
  FileText,
  Eye,
} from "lucide-react";
import {
  createCustomerStorage,
  updateCustomerStorage,
} from "@/lib/customer-storage";
import {
  CustomerStorage,
  CreateCustomerStorageRequest,
  UpdateCustomerStorageRequest,
  CustomerStorageFormData,
  validateCustomerStorageForm,
  getDefaultCustomerStorageFormData,
} from "@/types/customer-storage";
import { Customer } from "@/types/customer";
import { StorageLocation } from "@/types/storage-location";

interface CustomerStorageFormModalProps {
  customerStorage?: CustomerStorage;
  customers: Customer[];
  storageLocations: StorageLocation[];
  onSuccess: () => void;
  onCancel: () => void;
}

const CustomerStorageFormModal: React.FC<CustomerStorageFormModalProps> = ({
  customerStorage,
  customers,
  storageLocations,
  onSuccess,
  onCancel,
}) => {
  const isEditMode = !!customerStorage;
  const [formData, setFormData] = useState<CustomerStorageFormData>(
    getDefaultCustomerStorageFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get available storage locations (for create) or all locations (for edit)
  const availableLocations = isEditMode
    ? storageLocations
    : storageLocations.filter((loc) => loc.is_available);

  // Initialize form data for edit mode
  useEffect(() => {
    if (customerStorage) {
      setFormData({
        customer_id: customerStorage.customer_id,
        location_id: customerStorage.location_id,
        visit_id: customerStorage.visit_id,
        items_description: customerStorage.items_description,
        notes: customerStorage.notes || "",
      });
    }
  }, [customerStorage]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateCustomerStorageForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditMode && customerStorage) {
        // Update existing customer storage
        const updateData: UpdateCustomerStorageRequest = {
          location_id: formData.location_id,
          items_description: formData.items_description.trim(),
          notes: formData.notes.trim() || undefined,
        };
        await updateCustomerStorage(customerStorage.storage_id, updateData);
      } else {
        // Create new customer storage
        const createData: CreateCustomerStorageRequest = {
          customer_id: formData.customer_id,
          location_id: formData.location_id,
          visit_id: formData.visit_id,
          items_description: formData.items_description.trim(),
          notes: formData.notes.trim() || undefined,
        };
        await createCustomerStorage(createData);
      }

      onSuccess();
    } catch (error: any) {
      console.error("Error saving customer storage:", error);
      const errorMessage =
        error?.message ||
        "Failed to save customer storage record. Please try again.";
      setErrors({
        submit: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Package className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditMode ? "Edit Storage Record" : "Store Customer Item"}
              </h2>
              <p className="text-sm text-gray-600">
                {isEditMode
                  ? "Update customer storage information"
                  : "Create a new storage record for customer belongings"}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Selection (only for create mode) */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="customer_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Customer *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  id="customer_id"
                  name="customer_id"
                  required
                  value={formData.customer_id}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.customer_id ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting}
                >
                  <option value="">Select a customer</option>
                  {customers.map((customer) => (
                    <option
                      key={customer.customer_id}
                      value={customer.customer_id}
                    >
                      {customer.first_name} {customer.last_name} -{" "}
                      {customer.email}
                    </option>
                  ))}
                </select>
              </div>
              {errors.customer_id && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.customer_id}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Select the customer who owns the item being stored
              </p>
            </div>
          )}

          {/* Visit ID (only for create mode) */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="visit_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Visit ID *
              </label>
              <div className="relative">
                <Eye className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  id="visit_id"
                  name="visit_id"
                  type="text"
                  required
                  value={formData.visit_id}
                  onChange={handleChange}
                  placeholder="Enter visit ID"
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.visit_id ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting}
                />
              </div>
              {errors.visit_id && (
                <p className="mt-1 text-sm text-red-600">{errors.visit_id}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Enter the visit ID associated with this storage
              </p>
            </div>
          )}

          {/* Storage Location */}
          <div>
            <label
              htmlFor="location_id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Storage Location *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                id="location_id"
                name="location_id"
                required
                value={formData.location_id}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.location_id ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              >
                <option value="">Select a storage location</option>
                {availableLocations.map((location) => (
                  <option
                    key={location.location_id}
                    value={location.location_id}
                  >
                    {location.location_number}
                    {!isEditMode &&
                      (location.is_available ? " (Available)" : " (Taken)")}
                  </option>
                ))}
              </select>
            </div>
            {errors.location_id && (
              <p className="mt-1 text-sm text-red-600">{errors.location_id}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {isEditMode
                ? "Change the storage location if needed"
                : "Choose an available location where the item will be stored"}
            </p>
          </div>

          {/* Item Description */}
          <div>
            <label
              htmlFor="items_description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Item Description *
            </label>
            <div className="relative">
              <Package className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="items_description"
                name="items_description"
                rows={3}
                required
                value={formData.items_description}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.items_description
                    ? "border-red-300"
                    : "border-gray-300"
                }`}
                placeholder="Describe the item being stored"
                disabled={isSubmitting}
              />
            </div>
            {errors.items_description && (
              <p className="mt-1 text-sm text-red-600">
                {errors.items_description}
              </p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Provide a detailed description of the item (3-500 characters)
            </p>
          </div>

          {/* Notes */}
          <div>
            <label
              htmlFor="notes"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="notes"
                name="notes"
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.notes ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Additional notes about the storage (optional)"
                disabled={isSubmitting}
              />
            </div>
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Any additional information about the storage (max 1000 characters)
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Storing..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? "Update Storage" : "Store Item"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CustomerStorageFormModal;
