"use client";

import React from "react";
import {
  X,
  Activity,
  User,
  Package,
  Calendar,
  Hash,
  DollarSign,
  FileText,
  Clock,
  MapPin,
} from "lucide-react";
import {
  ServiceConsumption,
  formatCurrency,
} from "@/types/service-consumption";

interface ServiceConsumptionDetailsModalProps {
  serviceConsumption: ServiceConsumption;
  onClose: () => void;
}

const ServiceConsumptionDetailsModal: React.FC<
  ServiceConsumptionDetailsModalProps
> = ({ serviceConsumption, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Activity className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Service Consumption Details
              </h2>
              <p className="text-sm text-gray-600">
                View detailed information about this service consumption
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Customer Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <User className="w-5 h-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Customer Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Name</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.first_name}{" "}
                  {serviceConsumption.customer.last_name}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Email</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.email || "Not provided"}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Phone</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.customer.phone_number || "Not provided"}
                </p>
              </div>
            </div>
          </div>

          {/* Service Information */}
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Package className="w-5 h-5 text-blue-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Service Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Service Name
                </p>
                <p className="text-sm text-gray-900 font-medium">
                  {serviceConsumption.service.name}
                </p>
              </div>
              {serviceConsumption.service.description && (
                <div className="md:col-span-2">
                  <p className="text-sm font-medium text-gray-600">
                    Description
                  </p>
                  <p className="text-sm text-gray-900">
                    {serviceConsumption.service.description}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Consumption Details */}
          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Activity className="w-5 h-5 text-green-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Consumption Details
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Consumption Date
                </p>
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {new Date(
                      serviceConsumption.consumed_at
                    ).toLocaleDateString()}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Quantity</p>
                <div className="flex items-center">
                  <Hash className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {serviceConsumption.quantity}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">Unit Price</p>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-sm text-gray-900">
                    {formatCurrency(
                      serviceConsumption.price.price_amount,
                      serviceConsumption.price.currency
                    )}
                  </p>
                </div>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Total Amount
                </p>
                <div className="flex items-center">
                  <DollarSign className="w-4 h-4 text-gray-500 mr-2" />
                  <p className="text-lg font-bold text-green-600">
                    {formatCurrency(
                      serviceConsumption.total_amount,
                      serviceConsumption.price.currency
                    )}
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-3 pt-3 border-t border-green-200">
              <p className="text-xs text-green-700">
                Calculation: {serviceConsumption.quantity} ×{" "}
                {formatCurrency(
                  serviceConsumption.price.price_amount,
                  serviceConsumption.price.currency
                )}{" "}
                ={" "}
                {formatCurrency(
                  serviceConsumption.total_amount,
                  serviceConsumption.price.currency
                )}
              </p>
            </div>
          </div>

          {/* Price Details */}
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <DollarSign className="w-5 h-5 text-purple-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Price Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Currency</p>
                <p className="text-sm text-gray-900">
                  {serviceConsumption.price.currency}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Effective From
                </p>
                <p className="text-sm text-gray-900">
                  {new Date(
                    serviceConsumption.price.effective_from
                  ).toLocaleDateString()}
                </p>
              </div>
              {serviceConsumption.price.effective_to && (
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Effective To
                  </p>
                  <p className="text-sm text-gray-900">
                    {new Date(
                      serviceConsumption.price.effective_to
                    ).toLocaleDateString()}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Notes */}
          {serviceConsumption.notes && (
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center mb-3">
                <FileText className="w-5 h-5 text-gray-600 mr-2" />
                <h3 className="text-lg font-medium text-gray-900">Notes</h3>
              </div>
              <p className="text-sm text-gray-900 whitespace-pre-wrap">
                {serviceConsumption.notes}
              </p>
            </div>
          )}

          {/* Record Information */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center mb-3">
              <Clock className="w-5 h-5 text-gray-600 mr-2" />
              <h3 className="text-lg font-medium text-gray-900">
                Record Information
              </h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-gray-600">Created At</p>
                <p className="text-sm text-gray-900">
                  {new Date(serviceConsumption.created_at).toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">
                  Last Updated
                </p>
                <p className="text-sm text-gray-900">
                  {new Date(serviceConsumption.updated_at).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ServiceConsumptionDetailsModal;
