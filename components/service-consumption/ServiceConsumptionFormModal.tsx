"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  Activity,
  AlertCircle,
  User,
  Package,
  Calendar,
  Hash,
  DollarSign,
  FileText,
  MapPin,
} from "lucide-react";
import {
  createServiceConsumption,
  updateServiceConsumption,
} from "@/lib/service-consumption";
import { getCurrentServicePrice } from "@/lib/service-price";
import {
  ServiceConsumption,
  CreateServiceConsumptionRequest,
  UpdateServiceConsumptionRequest,
  ServiceConsumptionFormData,
  validateServiceConsumptionForm,
  getDefaultServiceConsumptionFormData,
  calculateTotalAmount,
  formatCurrency,
  ServiceConsumptionItem,
} from "@/types/service-consumption";
import { Customer } from "@/types/customer";
import { Service } from "@/types/service";
import { formatDateForBackend } from "@/utils/dateUtils";

interface ServiceConsumptionFormModalProps {
  serviceConsumption?: ServiceConsumption; // If provided, this is edit mode
  customers: Customer[];
  services: Service[];
  onSuccess: () => void;
  onCancel: () => void;
}

const ServiceConsumptionFormModal: React.FC<
  ServiceConsumptionFormModalProps
> = ({ serviceConsumption, customers, services, onSuccess, onCancel }) => {
  const isEditMode = !!serviceConsumption;
  const [formData, setFormData] = useState<ServiceConsumptionFormData>(
    getDefaultServiceConsumptionFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loadingPrice, setLoadingPrice] = useState(false);
  const [unitPrice, setUnitPrice] = useState<number>(0);

  // Initialize form data for edit mode
  useEffect(() => {
    if (serviceConsumption) {
      setFormData({
        customer_id: serviceConsumption.customer_id,
        service_id: serviceConsumption.service_id,
        visit_id: serviceConsumption.visit_id || "",
        consumed_at: serviceConsumption.consumed_at.split("T")[0], // Convert to YYYY-MM-DD format
        quantity: serviceConsumption.quantity.toString(),
        payment_status: "pending", // Default for form, not used in API
        notes: serviceConsumption.notes || "",
      });
      setUnitPrice(serviceConsumption.price.price_amount);
    }
  }, [serviceConsumption]);

  // Auto-load service price when service is selected
  useEffect(() => {
    if (formData.service_id && !isEditMode) {
      loadServicePrice();
    }
  }, [formData.service_id, isEditMode]);

  const loadServicePrice = async () => {
    try {
      setLoadingPrice(true);
      const response = await getCurrentServicePrice(formData.service_id);
      if (response.extend.current_price) {
        setUnitPrice(response.extend.current_price.price_amount);
      } else {
        // No current price found, set price to 0
        setUnitPrice(0);
      }
    } catch (error) {
      console.error("Error loading service price:", error);
      // Set price to 0 when there's an error (e.g., no current price found)
      setUnitPrice(0);
    } finally {
      setLoadingPrice(false);
    }
  };

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Calculate total amount
  const totalAmount = calculateTotalAmount(
    parseFloat(formData.quantity) || 0,
    unitPrice || 0
  );

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateServiceConsumptionForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditMode && serviceConsumption) {
        // Update existing service consumption
        const updateData: UpdateServiceConsumptionRequest = {
          service_id: formData.service_id,
          consumed_at: formatDateForBackend(formData.consumed_at),
          quantity: parseInt(formData.quantity),
          notes: formData.notes.trim() || undefined,
        };
        await updateServiceConsumption(
          serviceConsumption.consumption_id,
          updateData
        );
      } else {
        // Create new service consumption (bulk format)
        const serviceItem: ServiceConsumptionItem = {
          service_id: formData.service_id,
          quantity: parseInt(formData.quantity),
          notes: formData.notes.trim() || undefined,
        };

        const createData: CreateServiceConsumptionRequest = {
          customer_id: formData.customer_id,
          visit_id: formData.visit_id,
          consumed_at: formatDateForBackend(formData.consumed_at),
          services: [serviceItem],
        };
        await createServiceConsumption(createData);
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving service consumption:", error);
      setErrors({
        submit: "Failed to save service consumption record. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <Activity className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditMode
                  ? "Edit Service Consumption"
                  : "Record Service Consumption"}
              </h2>
              <p className="text-sm text-gray-600">
                {isEditMode
                  ? "Update service consumption information"
                  : "Record a new service consumption for a customer"}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Customer Selection (only for create mode) */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="customer_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Customer *
              </label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  id="customer_id"
                  name="customer_id"
                  required
                  value={formData.customer_id}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.customer_id ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting}
                >
                  <option value="">Select a customer</option>
                  {customers.map((customer) => (
                    <option
                      key={customer.customer_id}
                      value={customer.customer_id}
                    >
                      {customer.first_name} {customer.last_name} -{" "}
                      {customer.email}
                    </option>
                  ))}
                </select>
              </div>
              {errors.customer_id && (
                <p className="mt-1 text-sm text-red-600">
                  {errors.customer_id}
                </p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                Select the customer who consumed the service
              </p>
            </div>
          )}

          {/* Visit ID (only for create mode) */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="visit_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Visit ID *
              </label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  id="visit_id"
                  name="visit_id"
                  type="text"
                  required
                  value={formData.visit_id}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.visit_id ? "border-red-300" : "border-gray-300"
                  }`}
                  placeholder="Enter visit ID"
                  disabled={isSubmitting}
                />
              </div>
              {errors.visit_id && (
                <p className="mt-1 text-sm text-red-600">{errors.visit_id}</p>
              )}
              <p className="mt-1 text-xs text-gray-500">
                The visit ID associated with this consumption
              </p>
            </div>
          )}

          {/* Service Selection */}
          <div>
            <label
              htmlFor="service_id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Service *
            </label>
            <div className="relative">
              <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                id="service_id"
                name="service_id"
                required
                value={formData.service_id}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.service_id ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting || loadingPrice}
              >
                <option value="">Select a service</option>
                {services.map((service) => (
                  <option key={service.service_id} value={service.service_id}>
                    {service.name}
                  </option>
                ))}
              </select>
              {loadingPrice && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                </div>
              )}
            </div>
            {errors.service_id && (
              <p className="mt-1 text-sm text-red-600">{errors.service_id}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Choose the service that was consumed
            </p>
          </div>

          {/* Consumption Date */}
          <div>
            <label
              htmlFor="consumed_at"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Consumption Date *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="consumed_at"
                name="consumed_at"
                type="date"
                required
                value={formData.consumed_at}
                onChange={handleChange}
                max={new Date().toISOString().split("T")[0]} // Cannot be future date
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.consumed_at ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              />
            </div>
            {errors.consumed_at && (
              <p className="mt-1 text-sm text-red-600">{errors.consumed_at}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              When was the service consumed? (Cannot be in the future)
            </p>
          </div>

          {/* Quantity */}
          <div>
            <label
              htmlFor="quantity"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Quantity *
            </label>
            <div className="relative">
              <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="quantity"
                name="quantity"
                type="number"
                min="1"
                max="1000"
                step="1"
                required
                value={formData.quantity}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.quantity ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Enter quantity"
                disabled={isSubmitting}
              />
            </div>
            {errors.quantity && (
              <p className="mt-1 text-sm text-red-600">{errors.quantity}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Number of units consumed (1-1000)
            </p>
          </div>

          {/* Unit Price Display (Read-only) */}
          {unitPrice > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Unit Price
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  value={formatCurrency(unitPrice, "RWF")}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md bg-gray-50"
                  disabled
                  readOnly
                />
              </div>
              <p className="mt-1 text-xs text-gray-500">
                Current price from service pricing (automatically loaded)
              </p>
            </div>
          )}

          {/* Total Amount Display */}
          {totalAmount > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center">
                <DollarSign className="w-5 h-5 text-blue-600 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">
                    Total Amount
                  </h4>
                  <p className="text-lg font-bold text-blue-900 mt-1">
                    {formatCurrency(totalAmount, "RWF")}
                  </p>
                  <p className="text-xs text-blue-700 mt-1">
                    {formData.quantity} × {formatCurrency(unitPrice, "RWF")}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Notes */}
          <div>
            <label
              htmlFor="notes"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="notes"
                name="notes"
                rows={4}
                value={formData.notes}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.notes ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Additional notes about the consumption (optional)"
                disabled={isSubmitting}
              />
            </div>
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Any additional information about the consumption (max 1000
              characters)
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || loadingPrice}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Recording..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? "Update Consumption" : "Record Consumption"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServiceConsumptionFormModal;
