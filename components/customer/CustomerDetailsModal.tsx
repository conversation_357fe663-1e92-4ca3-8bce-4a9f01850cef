"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  User,
  Mail,
  Phone,
  Calendar,
  Award,
  MessageSquare,
  Check,
  BarChart3,
  TrendingUp,
  Gift,
  Clock,
  MapPin,
  Star,
} from "lucide-react";
import { getCustomerStatistics } from "@/lib/customer";
import { getCustomerVisitHistory } from "@/lib/customer-visit";
import { getCustomerLoyaltySafe, getCustomerRewardsSafe } from "@/lib/loyalty";
import {
  Customer,
  CustomerStatistics,
  formatCustomerName,
  getCustomerSegmentLabel,
  getCustomerStatusLabel,
  getCustomerSegmentColor,
  getCustomerStatusColor,
  getContactMethodLabel,
} from "@/types/customer";
import {
  CustomerVisit,
  formatVisitTime,
  formatVisitDate,
  getVisitTypeLabel,
  getVisitSourceLabel,
  getVisitTypeColor,
  getVisitSourceColor,
  isRecentVisit,
} from "@/types/customer-visit";
import {
  CustomerLoyalty,
  CustomerRewards,
  formatRewardValue,
  REWARD_TYPE_LABELS,
} from "@/types/loyalty";

interface CustomerDetailsModalProps {
  customer: Customer;
  onClose: () => void;
}

const CustomerDetailsModal: React.FC<CustomerDetailsModalProps> = ({
  customer,
  onClose,
}) => {
  const [statistics, setStatistics] = useState<CustomerStatistics | null>(null);
  const [isLoadingStats, setIsLoadingStats] = useState(true);
  const [statsError, setStatsError] = useState("");

  // Visit history state
  const [recentVisits, setRecentVisits] = useState<CustomerVisit[]>([]);
  const [isLoadingVisits, setIsLoadingVisits] = useState(true);
  const [visitsError, setVisitsError] = useState("");

  // Loyalty state
  const [loyalty, setLoyalty] = useState<CustomerLoyalty | null>(null);
  const [rewards, setRewards] = useState<CustomerRewards | null>(null);
  const [isLoadingLoyalty, setIsLoadingLoyalty] = useState(true);
  const [loyaltyError, setLoyaltyError] = useState("");

  // Fetch customer statistics
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setIsLoadingStats(true);
        setStatsError("");
        const response = await getCustomerStatistics(customer.customer_id);
        if (response.extend) {
          setStatistics(response.extend.statistics);
        }
      } catch (error) {
        console.error("Error fetching customer statistics:", error);
        setStatsError("Failed to load customer statistics");
      } finally {
        setIsLoadingStats(false);
      }
    };

    if (customer.customer_id) {
      fetchStatistics();
    }
  }, [customer.customer_id]);

  // Fetch recent visit history
  useEffect(() => {
    const fetchRecentVisits = async () => {
      try {
        setIsLoadingVisits(true);
        setVisitsError("");

        // Get visits from the last 30 days
        const endDate = new Date().toISOString().split("T")[0];
        const startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
          .toISOString()
          .split("T")[0];

        const response = await getCustomerVisitHistory(customer.customer_id, {
          start_date: startDate,
          end_date: endDate,
          per_page: 5, // Only get the 5 most recent visits
        });

        setRecentVisits(response.visits || []);
      } catch (error) {
        console.error("Error fetching recent visits:", error);
        setVisitsError("Failed to load recent visits");
      } finally {
        setIsLoadingVisits(false);
      }
    };

    if (customer.customer_id) {
      fetchRecentVisits();
    }
  }, [customer.customer_id]);

  // Fetch loyalty information
  useEffect(() => {
    const fetchLoyaltyData = async () => {
      try {
        setIsLoadingLoyalty(true);
        setLoyaltyError("");

        // Fetch loyalty and rewards data in parallel
        const [loyaltyResponse, rewardsResponse] = await Promise.all([
          getCustomerLoyaltySafe(customer.customer_id),
          getCustomerRewardsSafe(customer.customer_id),
        ]);

        if (loyaltyResponse.data) {
          setLoyalty(loyaltyResponse.data);
        }

        if (rewardsResponse.data) {
          setRewards(rewardsResponse.data);
        }

        // Set error if both failed
        if (loyaltyResponse.error && rewardsResponse.error) {
          setLoyaltyError("Failed to load loyalty information");
        }
      } catch (error) {
        console.error("Error fetching loyalty data:", error);
        setLoyaltyError("Failed to load loyalty information");
      } finally {
        setIsLoadingLoyalty(false);
      }
    };

    if (customer.customer_id) {
      fetchLoyaltyData();
    }
  }, [customer.customer_id]);

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Not provided";
    return new Date(dateString).toLocaleDateString();
  };

  // Format date with time
  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between pb-4 border-b border-gray-200">
          <div className="flex items-center">
            <div className="flex-shrink-0 h-12 w-12 mr-4">
              <div className="h-12 w-12 rounded-full bg-primary-light flex items-center justify-center">
                <span className="text-lg font-medium text-primary-dark">
                  {customer.first_name.charAt(0)}
                  {customer.last_name.charAt(0)}
                </span>
              </div>
            </div>
            <div>
              <h3 className="text-xl font-medium text-gray-900">
                {formatCustomerName(customer)}
              </h3>
              {customer.membership_number && (
                <p className="text-sm text-gray-500">
                  Member #{customer.membership_number}
                </p>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 focus:outline-none"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Customer Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <User className="h-5 w-5 mr-2" />
                Basic Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    First Name
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {customer.first_name}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Last Name
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {customer.last_name}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Date of Birth
                  </label>
                  <p className="mt-1 text-sm text-gray-900 flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                    {formatDate(customer.date_of_birth || null)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Registration Date
                  </label>
                  <p className="mt-1 text-sm text-gray-900 flex items-center">
                    <Calendar className="h-4 w-4 mr-1 text-gray-400" />
                    {formatDate(customer.registration_date)}
                  </p>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Contact Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Email Address
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {customer.email ? (
                      <a
                        href={`mailto:${customer.email}`}
                        className="text-primary hover:text-primary-dark"
                      >
                        {customer.email}
                      </a>
                    ) : (
                      "Not provided"
                    )}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Phone Number
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {customer.phone_number ? (
                      <a
                        href={`tel:${customer.phone_number}`}
                        className="text-primary hover:text-primary-dark"
                      >
                        {customer.phone_number}
                      </a>
                    ) : (
                      "Not provided"
                    )}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Preferred Contact Method
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {customer.preferred_contact_method
                      ? getContactMethodLabel(customer.preferred_contact_method)
                      : "Not specified"}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Marketing Consent
                  </label>
                  <p className="mt-1 text-sm text-gray-900 flex items-center">
                    {customer.marketing_consent ? (
                      <>
                        <Check className="h-4 w-4 mr-1 text-green-500" />
                        Yes
                      </>
                    ) : (
                      <>
                        <X className="h-4 w-4 mr-1 text-red-500" />
                        No
                      </>
                    )}
                  </p>
                </div>
              </div>
            </div>

            {/* Customer Details */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Customer Details
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Customer Segment
                  </label>
                  <div className="mt-1">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerSegmentColor(
                        customer.customer_segment
                      )}`}
                    >
                      <Star className="h-3 w-3 mr-1" />
                      {getCustomerSegmentLabel(customer.customer_segment)}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Status
                  </label>
                  <div className="mt-1">
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCustomerStatusColor(
                        customer.status
                      )}`}
                    >
                      {getCustomerStatusLabel(customer.status)}
                    </span>
                  </div>
                </div>
                {customer.available_rewards !== undefined && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Available Rewards
                    </label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <Gift className="h-4 w-4 mr-1 text-gray-400" />
                      {customer.available_rewards}
                    </p>
                  </div>
                )}
                {customer.visit_count !== undefined && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Total Visits
                    </label>
                    <p className="mt-1 text-sm text-gray-900 flex items-center">
                      <MapPin className="h-4 w-4 mr-1 text-gray-400" />
                      {customer.visit_count}
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Notes */}
            {customer.notes && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <MessageSquare className="h-5 w-5 mr-2" />
                  Notes
                </h4>
                <p className="text-sm text-gray-900 whitespace-pre-wrap">
                  {customer.notes}
                </p>
              </div>
            )}

            {/* Timestamps */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Record Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Created At
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDateTime(customer.created_at)}
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">
                    Last Updated
                  </label>
                  <p className="mt-1 text-sm text-gray-900">
                    {formatDateTime(customer.updated_at)}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Customer Statistics */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Visit Statistics
              </h4>

              {isLoadingStats ? (
                <div className="text-center py-8">
                  <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <p className="mt-2 text-sm text-gray-600">
                    Loading statistics...
                  </p>
                </div>
              ) : statsError ? (
                <div className="text-center py-8">
                  <div className="text-red-500 mb-2">
                    <TrendingUp className="h-8 w-8 mx-auto" />
                  </div>
                  <p className="text-sm text-red-600">{statsError}</p>
                </div>
              ) : statistics ? (
                <div className="space-y-4">
                  {/* Total Visits */}
                  <div className="bg-blue-50 rounded-lg p-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-blue-900">
                        Total Visits
                      </span>
                      <span className="text-lg font-bold text-blue-600">
                        {statistics.total_visits}
                      </span>
                    </div>
                  </div>

                  {/* Visit Breakdown */}
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">
                      Visit Breakdown
                    </h5>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Regular Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.regular_visits}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Loyalty Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.loyalty_visits}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Reward Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.reward_visits}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Complimentary Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.complimentary_visits}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Biometric Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.biometric_visits}
                      </span>
                    </div>

                    <div className="flex items-center justify-between py-1">
                      <span className="text-sm text-gray-600">
                        Manual Visits
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        {statistics.manual_visits}
                      </span>
                    </div>
                  </div>

                  {/* Date Range */}
                  {(statistics.start_date || statistics.end_date) && (
                    <div className="pt-4 border-t border-gray-200">
                      <h5 className="text-sm font-medium text-gray-700 mb-2">
                        Statistics Period
                      </h5>
                      <div className="text-xs text-gray-600">
                        {statistics.start_date && (
                          <div>From: {formatDate(statistics.start_date)}</div>
                        )}
                        {statistics.end_date && (
                          <div>To: {formatDate(statistics.end_date)}</div>
                        )}
                        {!statistics.start_date && !statistics.end_date && (
                          <div>All time</div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <TrendingUp className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                  <p className="text-sm text-gray-600">
                    No statistics available
                  </p>
                </div>
              )}
            </div>

            {/* Loyalty Information */}
            <div className="mt-6 bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Award className="h-5 w-5 mr-2" />
                Loyalty & Rewards
              </h4>

              {isLoadingLoyalty ? (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <p className="mt-2 text-xs text-gray-600">
                    Loading loyalty data...
                  </p>
                </div>
              ) : loyaltyError ? (
                <div className="text-center py-4">
                  <div className="text-red-500 mb-2">
                    <Award className="h-6 w-6 mx-auto" />
                  </div>
                  <p className="text-xs text-red-600">{loyaltyError}</p>
                </div>
              ) : loyalty ? (
                <div className="space-y-4">
                  {/* Loyalty Summary */}
                  <div className="bg-white border border-blue-100 rounded-lg p-3">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-blue-700">
                        Total Rewards Available
                      </span>
                      <span className="text-lg font-bold text-blue-900">
                        {loyalty.total_rewards_available}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div className="text-blue-600">
                        Earned:{" "}
                        <span className="font-medium text-blue-900">
                          {loyalty.total_rewards_earned}
                        </span>
                      </div>
                      <div className="text-blue-600">
                        Redeemed:{" "}
                        <span className="font-medium text-blue-900">
                          {loyalty.total_rewards_redeemed}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Recent Redemptions */}
                  {loyalty.recent_redemptions &&
                    loyalty.recent_redemptions.length > 0 && (
                      <div>
                        <h5 className="text-sm font-medium text-gray-700 mb-2">
                          Recent Redemptions
                        </h5>
                        <div className="space-y-2">
                          {loyalty.recent_redemptions
                            .slice(0, 2)
                            .map((redemption) => (
                              <div
                                key={redemption.redemption_id}
                                className="flex items-center justify-between py-1 px-2 bg-gray-50 rounded"
                              >
                                <span className="text-xs text-gray-600">
                                  {REWARD_TYPE_LABELS[
                                    redemption.reward_type as keyof typeof REWARD_TYPE_LABELS
                                  ] || redemption.reward_type}
                                </span>
                                <span className="text-xs text-gray-500">
                                  {new Date(
                                    redemption.redeemed_at
                                  ).toLocaleDateString()}
                                </span>
                              </div>
                            ))}
                        </div>
                      </div>
                    )}

                  {/* View All Link */}
                  <div className="pt-2 border-t border-gray-200">
                    <a
                      href={`/dashboard/hr/loyalty?customer_id=${customer.customer_id}`}
                      className="text-xs text-primary hover:text-primary-dark font-medium flex items-center"
                    >
                      <Gift className="h-3 w-3 mr-1" />
                      View all loyalty details
                    </a>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <Award className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                  <p className="text-xs text-gray-600">
                    No loyalty data available
                  </p>
                </div>
              )}
            </div>

            {/* Recent Visits */}
            <div className="mt-6 bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Recent Visits
              </h4>

              {isLoadingVisits ? (
                <div className="text-center py-4">
                  <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <p className="mt-2 text-xs text-gray-600">
                    Loading visits...
                  </p>
                </div>
              ) : visitsError ? (
                <div className="text-center py-4">
                  <div className="text-red-500 mb-2">
                    <Clock className="h-6 w-6 mx-auto" />
                  </div>
                  <p className="text-xs text-red-600">{visitsError}</p>
                </div>
              ) : recentVisits.length > 0 ? (
                <div className="space-y-3">
                  {recentVisits.map((visit) => (
                    <div
                      key={visit.visit_id}
                      className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
                    >
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitTypeColor(
                              visit.visit_type
                            )}`}
                          >
                            {getVisitTypeLabel(visit.visit_type)}
                          </span>
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitSourceColor(
                              visit.source
                            )}`}
                          >
                            {getVisitSourceLabel(visit.source)}
                          </span>
                          {visit.is_loyalty_visit && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              <Award className="h-3 w-3 mr-1" />
                              Loyalty
                            </span>
                          )}
                          {visit.reward_redeemed && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              <Gift className="h-3 w-3 mr-1" />
                              Reward
                            </span>
                          )}
                          {/* {isRecentVisit(visit.visit_time) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Recent
                            </span>
                          )} */}
                        </div>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatVisitDate(visit.visit_date)} at{" "}
                          {formatVisitTime(visit.visit_time)}
                          {visit.loyalty_points_earned > 0 && (
                            <span className="ml-2">
                              +{visit.loyalty_points_earned} points
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                  <div className="pt-2 text-center">
                    <a
                      href={`/dashboard/hr/customer-visits?customer_id=${customer.customer_id}`}
                      className="text-xs text-primary hover:text-primary-dark"
                    >
                      View all visits →
                    </a>
                  </div>
                </div>
              ) : (
                <div className="text-center py-4">
                  <Clock className="h-6 w-6 mx-auto text-gray-400 mb-2" />
                  <p className="text-xs text-gray-600">No recent visits</p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 bg-white border border-gray-200 rounded-lg p-4">
              <h4 className="text-lg font-medium text-gray-900 mb-4">
                Quick Actions
              </h4>
              <div className="space-y-2">
                {customer.email && (
                  <a
                    href={`mailto:${customer.email}`}
                    className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Mail className="h-4 w-4 mr-2" />
                    Send Email
                  </a>
                )}
                {customer.phone_number && (
                  <a
                    href={`tel:${customer.phone_number}`}
                    className="w-full inline-flex items-center justify-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <Phone className="h-4 w-4 mr-2" />
                    Call Customer
                  </a>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default CustomerDetailsModal;
