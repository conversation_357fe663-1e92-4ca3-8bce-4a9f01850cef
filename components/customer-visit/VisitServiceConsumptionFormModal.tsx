"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  Bar<PERSON>hart3,
  AlertCircle,
  Package,
  Hash,
  DollarSign,
  FileText,
  Plus,
  Trash2,
} from "lucide-react";
import { createServiceConsumption } from "@/lib/service-consumption";
import { getCurrentServicePrice } from "@/lib/service-price";
import { getServices } from "@/lib/service";
import {
  CreateServiceConsumptionRequest,
  ServiceConsumptionItem,
  calculateTotalAmount,
  formatCurrency,
} from "@/types/service-consumption";
import { Service } from "@/types/service";
import { CustomerVisit } from "@/types/customer-visit";
import { formatDateForBackend } from "@/utils/dateUtils";

interface VisitServiceConsumptionFormModalProps {
  visit: CustomerVisit;
  onSuccess: () => void;
  onCancel: () => void;
}

interface ServiceItem {
  service_id: string;
  quantity: number;
  notes: string;
  unitPrice: number;
  loading: boolean;
}

const VisitServiceConsumptionFormModal: React.FC<
  VisitServiceConsumptionFormModalProps
> = ({ visit, onSuccess, onCancel }) => {
  const [services, setServices] = useState<Service[]>([]);
  const [serviceItems, setServiceItems] = useState<ServiceItem[]>([
    { service_id: "", quantity: 1, notes: "", unitPrice: 0, loading: false },
  ]);
  const [consumedAt, setConsumedAt] = useState(
    new Date().toISOString().split("T")[0]
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingServices, setIsLoadingServices] = useState(true);

  // Load services
  useEffect(() => {
    const loadServices = async () => {
      try {
        setIsLoadingServices(true);
        const response = await getServices({ is_active: true });
        setServices(response.extend.services);
      } catch (error) {
        console.error("Error loading services:", error);
        setErrors({
          submit: "Failed to load services. Please try again.",
        });
      } finally {
        setIsLoadingServices(false);
      }
    };

    loadServices();
  }, []);

  // Load service price when service is selected
  const loadServicePrice = async (index: number, serviceId: string) => {
    if (!serviceId) return;

    setServiceItems((prev) =>
      prev.map((item, i) => (i === index ? { ...item, loading: true } : item))
    );

    try {
      const response = await getCurrentServicePrice(serviceId);
      if (response.extend.current_price) {
        setServiceItems((prev) =>
          prev.map((item, i) =>
            i === index
              ? {
                  ...item,
                  unitPrice: response.extend.current_price.price_amount,
                  loading: false,
                }
              : item
          )
        );
      } else {
        // No current price found, set price to 0
        setServiceItems((prev) =>
          prev.map((item, i) =>
            i === index ? { ...item, unitPrice: 0, loading: false } : item
          )
        );
      }
    } catch (error) {
      console.error("Error loading service price:", error);
      // Set price to 0 when there's an error (e.g., no current price found)
      setServiceItems((prev) =>
        prev.map((item, i) =>
          i === index ? { ...item, unitPrice: 0, loading: false } : item
        )
      );
    }
  };

  // Handle service item changes
  const handleServiceItemChange = (
    index: number,
    field: keyof ServiceItem,
    value: string | number
  ) => {
    setServiceItems((prev) =>
      prev.map((item, i) => {
        if (i === index) {
          const updatedItem = { ...item, [field]: value };

          // Load price when service is selected
          if (field === "service_id" && typeof value === "string") {
            loadServicePrice(index, value);
          }

          return updatedItem;
        }
        return item;
      })
    );

    // Clear errors
    if (errors[`service_${index}`]) {
      setErrors((prev) => ({
        ...prev,
        [`service_${index}`]: "",
      }));
    }
  };

  // Add new service item
  const addServiceItem = () => {
    setServiceItems((prev) => [
      ...prev,
      { service_id: "", quantity: 1, notes: "", unitPrice: 0, loading: false },
    ]);
  };

  // Remove service item
  const removeServiceItem = (index: number) => {
    if (serviceItems.length > 1) {
      setServiceItems((prev) => prev.filter((_, i) => i !== index));
    }
  };

  // Calculate total amount
  const totalAmount = serviceItems.reduce(
    (sum, item) => sum + calculateTotalAmount(item.quantity, item.unitPrice),
    0
  );

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!consumedAt) {
      newErrors.consumed_at = "Consumption date is required";
    }

    serviceItems.forEach((item, index) => {
      if (!item.service_id) {
        newErrors[`service_${index}`] = "Service is required";
      }
      if (item.quantity < 1) {
        newErrors[`quantity_${index}`] = "Quantity must be at least 1";
      }
    });

    return newErrors;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const serviceConsumptionItems: ServiceConsumptionItem[] =
        serviceItems.map((item) => ({
          service_id: item.service_id,
          quantity: item.quantity,
          notes: item.notes.trim() || undefined,
        }));

      const createData: CreateServiceConsumptionRequest = {
        customer_id: visit.customer.customer_id,
        visit_id: visit.visit_id,
        consumed_at: formatDateForBackend(consumedAt),
        services: serviceConsumptionItems,
      };

      await createServiceConsumption(createData);
      onSuccess();
    } catch (error) {
      console.error("Error recording service consumption:", error);
      setErrors({
        submit: "Failed to record service consumption. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-slate-100 rounded-lg mr-3">
              <BarChart3 className="w-6 h-6 text-slate-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Record Service Consumption
              </h2>
              <p className="text-sm text-gray-600">
                Record services consumed during this visit
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Visit Information */}
          <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
            <h3 className="text-slate-800 font-medium mb-2">
              Visit Information
            </h3>
            <div className="space-y-1 text-sm text-slate-700">
              <p>
                <strong>Customer:</strong> {visit.customer.first_name}{" "}
                {visit.customer.last_name}
              </p>
              <p>
                <strong>Visit Date:</strong>{" "}
                {new Date(visit.visit_time).toLocaleDateString()}
              </p>
            </div>
          </div>

          {/* Consumption Date */}
          <div>
            <label
              htmlFor="consumed_at"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Consumption Date *
            </label>
            <input
              id="consumed_at"
              name="consumed_at"
              type="date"
              required
              value={consumedAt}
              onChange={(e) => setConsumedAt(e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent ${
                errors.consumed_at ? "border-red-300" : "border-gray-300"
              }`}
              disabled={isSubmitting}
            />
            {errors.consumed_at && (
              <p className="mt-1 text-sm text-red-600">{errors.consumed_at}</p>
            )}
          </div>

          {/* Service Items */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Services *
              </label>
              <button
                type="button"
                onClick={addServiceItem}
                className="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-slate-600 bg-slate-100 hover:bg-slate-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                disabled={isSubmitting}
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Service
              </button>
            </div>

            <div className="space-y-4">
              {serviceItems.map((item, index) => (
                <div
                  key={index}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="text-sm font-medium text-gray-700">
                      Service {index + 1}
                    </h4>
                    {serviceItems.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeServiceItem(index)}
                        className="text-red-600 hover:text-red-800"
                        disabled={isSubmitting}
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Service Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Service *
                      </label>
                      <div className="relative">
                        <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <select
                          value={item.service_id}
                          onChange={(e) =>
                            handleServiceItemChange(
                              index,
                              "service_id",
                              e.target.value
                            )
                          }
                          className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent ${
                            errors[`service_${index}`]
                              ? "border-red-300"
                              : "border-gray-300"
                          }`}
                          disabled={isSubmitting || isLoadingServices}
                        >
                          <option value="">
                            {isLoadingServices
                              ? "Loading..."
                              : "Select service"}
                          </option>
                          {services.map((service) => (
                            <option
                              key={service.service_id}
                              value={service.service_id}
                            >
                              {service.name}
                            </option>
                          ))}
                        </select>
                      </div>
                      {errors[`service_${index}`] && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors[`service_${index}`]}
                        </p>
                      )}
                    </div>

                    {/* Quantity */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Quantity *
                      </label>
                      <div className="relative">
                        <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <input
                          type="number"
                          min="1"
                          max="1000"
                          value={item.quantity}
                          onChange={(e) =>
                            handleServiceItemChange(
                              index,
                              "quantity",
                              parseInt(e.target.value) || 1
                            )
                          }
                          className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent ${
                            errors[`quantity_${index}`]
                              ? "border-red-300"
                              : "border-gray-300"
                          }`}
                          disabled={isSubmitting}
                        />
                      </div>
                      {errors[`quantity_${index}`] && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors[`quantity_${index}`]}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Price Display */}
                  {item.unitPrice > 0 && (
                    <div className="mt-3 p-3 bg-gray-50 rounded-md">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Unit Price:</span>
                        <span className="font-medium">
                          {formatCurrency(item.unitPrice)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm font-medium">
                        <span className="text-gray-900">Subtotal:</span>
                        <span className="text-gray-900">
                          {formatCurrency(
                            calculateTotalAmount(item.quantity, item.unitPrice)
                          )}
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Notes */}
                  <div className="mt-3">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Notes (Optional)
                    </label>
                    <div className="relative">
                      <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
                      <textarea
                        rows={2}
                        value={item.notes}
                        onChange={(e) =>
                          handleServiceItemChange(
                            index,
                            "notes",
                            e.target.value
                          )
                        }
                        className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-slate-500 focus:border-transparent resize-none"
                        placeholder="Additional notes for this service"
                        disabled={isSubmitting}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Total Amount */}
          {totalAmount > 0 && (
            <div className="bg-slate-50 border border-slate-200 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <span className="text-lg font-medium text-gray-900">
                  Total Amount:
                </span>
                <span className="text-xl font-bold text-slate-600">
                  {formatCurrency(totalAmount)}
                </span>
              </div>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-slate-600 hover:bg-slate-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || isLoadingServices}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Recording...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Record Consumption
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VisitServiceConsumptionFormModal;
