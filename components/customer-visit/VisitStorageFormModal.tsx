"use client";

import React, { useState, useEffect } from "react";
import { X, Save, Package, AlertCircle, MapPin, FileText } from "lucide-react";
import { createCustomerStorage } from "@/lib/customer-storage";
import { getStorageLocations } from "@/lib/storage-location";
import {
  CreateCustomerStorageRequest,
  CustomerStorageFormData,
  validateCustomerStorageForm,
} from "@/types/customer-storage";
import { StorageLocation } from "@/types/storage-location";
import { CustomerVisit } from "@/types/customer-visit";

interface VisitStorageFormModalProps {
  visit: CustomerVisit;
  onSuccess: () => void;
  onCancel: () => void;
}

const VisitStorageFormModal: React.FC<VisitStorageFormModalProps> = ({
  visit,
  onSuccess,
  onCancel,
}) => {
  const [formData, setFormData] = useState<CustomerStorageFormData>({
    customer_id: visit.customer.customer_id,
    location_id: "",
    visit_id: visit.visit_id,
    items_description: "",
    notes: "",
  });
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingLocations, setIsLoadingLocations] = useState(true);

  // Load storage locations
  useEffect(() => {
    const loadStorageLocations = async () => {
      try {
        setIsLoadingLocations(true);
        const response = await getStorageLocations({ is_available: true });
        setStorageLocations(response.extend.storage_locations);
      } catch (error) {
        console.error("Error loading storage locations:", error);
        setErrors({
          submit: "Failed to load storage locations. Please try again.",
        });
      } finally {
        setIsLoadingLocations(false);
      }
    };

    loadStorageLocations();
  }, []);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear field error when user starts typing
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateCustomerStorageForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const createData: CreateCustomerStorageRequest = {
        customer_id: formData.customer_id,
        location_id: formData.location_id,
        visit_id: formData.visit_id,
        items_description: formData.items_description.trim(),
        notes: formData.notes.trim() || undefined,
      };

      await createCustomerStorage(createData);
      onSuccess();
    } catch (error) {
      console.error("Error storing items:", error);
      setErrors({
        submit: "Failed to store items. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg mr-3">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Store Items for Visit
              </h2>
              <p className="text-sm text-gray-600">
                Store customer belongings for this visit
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Visit Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="text-blue-800 font-medium mb-2">
              Visit Information
            </h3>
            <div className="space-y-1 text-sm text-blue-700">
              <p>
                <strong>Customer:</strong> {visit.customer.first_name}{" "}
                {visit.customer.last_name}
              </p>
              <p>
                <strong>Visit Date:</strong>{" "}
                {new Date(visit.visit_time).toLocaleDateString()}
              </p>
              <p>
                <strong>Visit ID:</strong> {visit.visit_id}
              </p>
            </div>
          </div>

          {/* Storage Location */}
          <div>
            <label
              htmlFor="location_id"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Storage Location *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <select
                id="location_id"
                name="location_id"
                required
                value={formData.location_id}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.location_id ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting || isLoadingLocations}
              >
                <option value="">
                  {isLoadingLocations
                    ? "Loading locations..."
                    : "Select a storage location"}
                </option>
                {storageLocations.map((location) => (
                  <option
                    key={location.location_id}
                    value={location.location_id}
                  >
                    Location #{location.location_number}
                    {location.notes && ` - ${location.notes}`}
                  </option>
                ))}
              </select>
            </div>
            {errors.location_id && (
              <p className="mt-1 text-sm text-red-600">{errors.location_id}</p>
            )}
          </div>

          {/* Items Description */}
          <div>
            <label
              htmlFor="items_description"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Items Description *
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="items_description"
                name="items_description"
                required
                rows={4}
                value={formData.items_description}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                  errors.items_description
                    ? "border-red-300"
                    : "border-gray-300"
                }`}
                placeholder="Describe the items being stored (e.g., laptop bag with charger, water bottle, and documents)"
                disabled={isSubmitting}
              />
            </div>
            {errors.items_description && (
              <p className="mt-1 text-sm text-red-600">
                {errors.items_description}
              </p>
            )}
          </div>

          {/* Notes */}
          <div>
            <label
              htmlFor="notes"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Additional Notes (Optional)
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 text-gray-400 h-4 w-4" />
              <textarea
                id="notes"
                name="notes"
                rows={3}
                value={formData.notes}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                  errors.notes ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Any additional notes about the storage (e.g., special handling instructions, expected retrieval date)"
                disabled={isSubmitting}
              />
            </div>
            {errors.notes && (
              <p className="mt-1 text-sm text-red-600">{errors.notes}</p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting || isLoadingLocations}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Storing...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Store Items
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default VisitStorageFormModal;
