"use client";

import React, { useState, useEffect } from "react";
import {
  X,
  Save,
  DollarSign,
  AlertCircle,
  Calendar,
  Settings,
} from "lucide-react";
import { createServicePrice, updateServicePrice } from "@/lib/service-price";
import {
  ServicePrice,
  CreateServicePriceRequest,
  UpdateServicePriceRequest,
  ServicePriceFormData,
  validateServicePriceForm,
  getDefaultServicePriceFormData,
  SUPPORTED_CURRENCIES,
} from "@/types/service-price";
import { Service } from "@/types/service";

interface ServicePriceFormModalProps {
  servicePrice?: ServicePrice; // If provided, this is edit mode
  services: Service[];
  onSuccess: () => void;
  onCancel: () => void;
}

const ServicePriceFormModal: React.FC<ServicePriceFormModalProps> = ({
  servicePrice,
  services,
  onSuccess,
  onCancel,
}) => {
  const isEditMode = !!servicePrice;
  const [formData, setFormData] = useState<ServicePriceFormData>(
    getDefaultServicePriceFormData()
  );
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data for edit mode
  useEffect(() => {
    if (servicePrice) {
      setFormData({
        service_id: servicePrice.service_id,
        price_amount: servicePrice.price_amount.toString(),
        effective_from: servicePrice.effective_from.split('T')[0], // Convert to date format
        effective_to: servicePrice.effective_to ? servicePrice.effective_to.split('T')[0] : '',
      });
    }
  }, [servicePrice]);

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for this field
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validationErrors = validateServicePriceForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      if (isEditMode && servicePrice) {
        // Update existing service price
        const updateData: UpdateServicePriceRequest = {
          price_amount: parseFloat(formData.price_amount),
          effective_from: formData.effective_from,
          effective_to: formData.effective_to || undefined,
        };
        await updateServicePrice(servicePrice.price_id, updateData);
      } else {
        // Create new service price
        const createData: CreateServicePriceRequest = {
          service_id: formData.service_id,
          price_amount: parseFloat(formData.price_amount),
          effective_from: formData.effective_from,
          effective_to: formData.effective_to || undefined,
        };
        await createServicePrice(createData);
      }

      onSuccess();
    } catch (error) {
      console.error("Error saving service price:", error);
      setErrors({
        submit: "Failed to save service price. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <DollarSign className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {isEditMode ? "Edit Service Price" : "Create New Service Price"}
              </h2>
              <p className="text-sm text-gray-600">
                {isEditMode
                  ? "Update service price information"
                  : "Set pricing for a service with effective dates"}
              </p>
            </div>
          </div>
          <button
            onClick={onCancel}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Submit Error */}
          {errors.submit && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <AlertCircle className="w-5 h-5 text-red-600 mr-3" />
                <div>
                  <h3 className="text-red-800 font-medium">Error</h3>
                  <p className="text-red-700 text-sm mt-1">{errors.submit}</p>
                </div>
              </div>
            </div>
          )}

          {/* Service Selection */}
          {!isEditMode && (
            <div>
              <label
                htmlFor="service_id"
                className="block text-sm font-medium text-gray-700 mb-1"
              >
                Service *
              </label>
              <div className="relative">
                <Settings className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <select
                  id="service_id"
                  name="service_id"
                  required
                  value={formData.service_id}
                  onChange={handleChange}
                  className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                    errors.service_id ? "border-red-300" : "border-gray-300"
                  }`}
                  disabled={isSubmitting}
                >
                  <option value="">Select a service</option>
                  {services.map((service) => (
                    <option key={service.service_id} value={service.service_id}>
                      {service.name}
                    </option>
                  ))}
                </select>
              </div>
              {errors.service_id && (
                <p className="mt-1 text-sm text-red-600">{errors.service_id}</p>
              )}
            </div>
          )}

          {/* Price Amount */}
          <div>
            <label
              htmlFor="price_amount"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Price Amount *
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="price_amount"
                name="price_amount"
                type="number"
                step="0.01"
                min="0"
                required
                value={formData.price_amount}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.price_amount ? "border-red-300" : "border-gray-300"
                }`}
                placeholder="Enter price amount"
                disabled={isSubmitting}
              />
            </div>
            {errors.price_amount && (
              <p className="mt-1 text-sm text-red-600">{errors.price_amount}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Enter the price amount in RWF (Rwandan Francs)
            </p>
          </div>

          {/* Effective From Date */}
          <div>
            <label
              htmlFor="effective_from"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Effective From *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="effective_from"
                name="effective_from"
                type="date"
                required
                value={formData.effective_from}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.effective_from ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              />
            </div>
            {errors.effective_from && (
              <p className="mt-1 text-sm text-red-600">{errors.effective_from}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              The date when this price becomes effective
            </p>
          </div>

          {/* Effective To Date */}
          <div>
            <label
              htmlFor="effective_to"
              className="block text-sm font-medium text-gray-700 mb-1"
            >
              Effective To
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                id="effective_to"
                name="effective_to"
                type="date"
                value={formData.effective_to}
                onChange={handleChange}
                className={`w-full pl-10 pr-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent ${
                  errors.effective_to ? "border-red-300" : "border-gray-300"
                }`}
                disabled={isSubmitting}
              />
            </div>
            {errors.effective_to && (
              <p className="mt-1 text-sm text-red-600">{errors.effective_to}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              Optional: The date when this price expires (leave empty for no expiration)
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditMode ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isEditMode ? "Update Price" : "Create Price"}
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ServicePriceFormModal;
