import React from "react";
import {
  REWARD_TYPE_LABELS,
  RULE_TYPE_LABELS,
  LoyaltyRule,
  getActiveStatusColor,
  isRuleExpiringSoon,
  formatRewardValue,
} from "@/types/loyalty";

interface LoyaltyRuleDetailsModalProps {
  open: boolean;
  onClose: () => void;
  rule: LoyaltyRule | null;
}

const LoyaltyRuleDetailsModal: React.FC<LoyaltyRuleDetailsModalProps> = ({
  open,
  onClose,
  rule,
}) => {
  if (!open || !rule) return null;

  const isExpiringSoon = rule.valid_until
    ? isRuleExpiringSoon(rule.valid_until)
    : false;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex justify-center items-center z-50">
      <div className="bg-white rounded-2xl shadow-lg p-6 w-[90%] max-w-lg relative">
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-3 right-3 text-gray-500 hover:text-gray-800 text-lg font-bold"
        >
          ✕
        </button>

        {/* Header */}
        <h2 className="text-lg font-semibold">{rule.name}</h2>
        <p className="text-sm text-gray-500 mb-4">
          {rule.description || "No description available."}
        </p>

        {/* Details */}
        <div className="space-y-3">
          {/* Rule Type */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">
              Rule Type:
            </span>
            <span className="px-2 py-1 rounded-full bg-gray-200 text-gray-800 text-xs capitalize">
              {RULE_TYPE_LABELS[
                rule.rule_type as keyof typeof RULE_TYPE_LABELS
              ] || rule.rule_type}
            </span>
          </div>

          {/* Reward Type */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">
              Reward Type:
            </span>
            <span className="px-2 py-1 rounded-full bg-gray-200 text-gray-800 text-xs capitalize">
              {REWARD_TYPE_LABELS[
                rule.reward_type as keyof typeof REWARD_TYPE_LABELS
              ] || rule.reward_type}
            </span>
          </div>

          {/* Reward Value */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">
              Reward Value:
            </span>
            <span className="text-sm font-semibold">
              {formatRewardValue(rule.reward_type, rule.reward_value)}
            </span>
          </div>

          {/* Reward Expiry */}
          {rule.reward_expiry_days && (
            <div className="flex justify-between">
              <span className="text-sm font-medium text-gray-600">
                Reward Expiry:
              </span>
              <span className="text-sm">{rule.reward_expiry_days} days</span>
            </div>
          )}

          {/* Trigger */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">Trigger:</span>
            <span className="text-sm">
              {rule.trigger_value}{" "}
              {rule.trigger_period_days
                ? `in ${rule.trigger_period_days} days`
                : ""}
            </span>
          </div>

          {/* Valid From */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">
              Valid From:
            </span>
            <span className="text-sm">
              {new Date(rule.valid_from).toLocaleDateString()}
            </span>
          </div>

          {/* Valid Until */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">
              Valid Until:
            </span>
            <span
              className={`text-sm ${
                isExpiringSoon ? "text-red-500 font-semibold" : ""
              }`}
            >
              {new Date(rule.valid_until).toLocaleDateString()}
              {isExpiringSoon && " (Expiring Soon)"}
            </span>
          </div>

          {/* Status */}
          <div className="flex justify-between">
            <span className="text-sm font-medium text-gray-600">Status:</span>
            <span
              className={`px-2 py-1 rounded-full text-xs font-semibold capitalize ${getActiveStatusColor(
                rule.is_active
              )}`}
            >
              {rule.is_active ? "Active" : "Inactive"}
            </span>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 rounded-lg transition"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoyaltyRuleDetailsModal;
