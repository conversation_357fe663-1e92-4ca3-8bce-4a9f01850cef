"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Activity,
  RefreshCw,
  DollarSign,
  ChevronLeft,
  ChevronRight,
  Trash2,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getServiceConsumptions,
  deleteServiceConsumption,
  getServiceConsumptionTotal,
} from "@/lib/service-consumption";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import { useDeleteConfirmation } from "@/hooks/useConfirmation";
import { getCustomers } from "@/lib/customer";
import { getServices } from "@/lib/service";
import {
  ServiceConsumption,
  ServiceConsumptionFilters,
  formatCurrency,
  SERVICE_CONSUMPTION_SORT_OPTIONS,
} from "@/types/service-consumption";
import { Customer } from "@/types/customer";
import { Service } from "@/types/service";
import ServiceConsumptionFormModal from "@/components/service-consumption/ServiceConsumptionFormModal";
import ServiceConsumptionDetailsModal from "@/components/service-consumption/ServiceConsumptionDetailsModal";

interface ServiceConsumptionContentProps {}

const ServiceConsumptionContent: React.FC<
  ServiceConsumptionContentProps
> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // Confirmation hooks
  const deleteConfirmation = useDeleteConfirmation();

  // State management
  const [serviceConsumptions, setServiceConsumptions] = useState<
    ServiceConsumption[]
  >([]);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<ServiceConsumptionFilters>({});
  const [sortBy, setSortBy] = useState<string>("consumed_at_desc");
  const [totalRevenue, setTotalRevenue] = useState<number>(0);
  const [currency, setCurrency] = useState<string>("RWF");

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedServiceConsumption, setSelectedServiceConsumption] =
    useState<ServiceConsumption | null>(null);

  // Selection states for bulk operations
  const [selectedConsumptions, setSelectedConsumptions] = useState<string[]>(
    []
  );

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data
  useEffect(() => {
    loadData();
  }, [filters, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [consumptionsResponse, customersResponse, servicesResponse] =
        await Promise.all([
          getServiceConsumptions({
            ...filters,
            search: searchTerm || undefined,
          }),
          getCustomers({ status: "active" }),
          getServices(),
        ]);

      setServiceConsumptions(consumptionsResponse.extend.service_consumptions);
      setCustomers(customersResponse.extend.customers);
      setServices(servicesResponse.extend.services);

      // Load total revenue
      const totalResponse = await getServiceConsumptionTotal(filters);
      setTotalRevenue(totalResponse.total_amount);
      setCurrency(totalResponse.currency);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load service consumption records. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (
    newFilters: Partial<ServiceConsumptionFilters>
  ) => {
    setFilters({ ...filters, ...newFilters });
    setCurrentPage(1);
  };

  // Handle selection
  const handleSelectConsumption = (consumptionId: string) => {
    setSelectedConsumptions((prev) =>
      prev.includes(consumptionId)
        ? prev.filter((id) => id !== consumptionId)
        : [...prev, consumptionId]
    );
  };

  const handleSelectAll = () => {
    if (selectedConsumptions.length === paginatedServiceConsumptions.length) {
      setSelectedConsumptions([]);
    } else {
      setSelectedConsumptions(
        paginatedServiceConsumptions.map((c) => c.consumption_id)
      );
    }
  };

  // Handle delete consumption
  const handleDeleteConsumption = async (consumption: ServiceConsumption) => {
    const confirmed = await deleteConfirmation.confirmDelete(
      `${consumption.service.name} consumption`,
      "service consumption record"
    );

    if (!confirmed) {
      deleteConfirmation.hideConfirmation();
      return;
    }

    try {
      deleteConfirmation.setLoading(true);
      await deleteServiceConsumption(consumption.consumption_id);
      await loadData();
      deleteConfirmation.hideConfirmation();
    } catch (error) {
      console.error("Error deleting consumption:", error);
      deleteConfirmation.hideConfirmation();
      // TODO: Implement error toast notification
    }
  };

  // Handle view consumption
  const handleViewConsumption = (consumption: ServiceConsumption) => {
    setSelectedServiceConsumption(consumption);
    setShowDetailsModal(true);
  };

  // Handle edit consumption
  const handleEditConsumption = (consumption: ServiceConsumption) => {
    setSelectedServiceConsumption(consumption);
    setShowEditModal(true);
  };

  // Handle form success
  const handleFormSuccess = async () => {
    await loadData();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedServiceConsumption(null);
  };

  // Sort service consumptions
  const sortedServiceConsumptions = [...serviceConsumptions].sort((a, b) => {
    switch (sortBy) {
      case "consumed_at_desc":
        return (
          new Date(b.consumed_at).getTime() - new Date(a.consumed_at).getTime()
        );
      case "consumed_at_asc":
        return (
          new Date(a.consumed_at).getTime() - new Date(b.consumed_at).getTime()
        );
      case "customer_name_asc":
        return `${a.customer.first_name} ${a.customer.last_name}`.localeCompare(
          `${b.customer.first_name} ${b.customer.last_name}`
        );
      case "customer_name_desc":
        return `${b.customer.first_name} ${b.customer.last_name}`.localeCompare(
          `${a.customer.first_name} ${a.customer.last_name}`
        );
      case "service_name_asc":
        return a.service.name.localeCompare(b.service.name);
      case "total_amount_desc":
        return b.total_amount - a.total_amount;
      case "total_amount_asc":
        return a.total_amount - b.total_amount;
      default:
        return 0;
    }
  });

  // Paginate service consumptions
  const totalPages = Math.ceil(sortedServiceConsumptions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedServiceConsumptions = sortedServiceConsumptions.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Service Consumption Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Track and manage customer service consumption
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => loadData()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          {/* <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Record Consumption
          </button> */}
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Total Consumptions
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {serviceConsumptions.length}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Activity className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-green-600">
                {formatCurrency(totalRevenue, currency)}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Average Per Transaction
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {serviceConsumptions.length > 0
                  ? formatCurrency(
                      totalRevenue / serviceConsumptions.length,
                      currency
                    )
                  : formatCurrency(0, currency)}
              </p>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <DollarSign className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search customers, services..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Customer Filter */}
          <select
            value={filters.customer_id || ""}
            onChange={(e) =>
              handleFilterChange({ customer_id: e.target.value || undefined })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Customers</option>
            {customers.map((customer) => (
              <option key={customer.customer_id} value={customer.customer_id}>
                {customer.first_name} {customer.last_name}
              </option>
            ))}
          </select>

          {/* Service Filter */}
          <select
            value={filters.service_id || ""}
            onChange={(e) =>
              handleFilterChange({ service_id: e.target.value || undefined })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Services</option>
            {services.map((service) => (
              <option key={service.service_id} value={service.service_id}>
                {service.name}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range Filters */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              From Date
            </label>
            <input
              type="date"
              value={filters.consumption_date_from || ""}
              onChange={(e) =>
                handleFilterChange({
                  consumption_date_from: e.target.value || undefined,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* End Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              To Date
            </label>
            <input
              type="date"
              value={filters.consumption_date_to || ""}
              onChange={(e) =>
                handleFilterChange({
                  consumption_date_to: e.target.value || undefined,
                })
              }
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Clear Filters Button */}
        {(Object.keys(filters).length > 0 || searchTerm) && (
          <div className="mt-4 flex justify-end">
            <button
              onClick={() => {
                setFilters({});
                setSearchTerm("");
                setCurrentPage(1);
              }}
              className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              Clear All Filters
            </button>
          </div>
        )}
      </div>

      {/* Sort and Selection */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 mt-6">
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <span className="text-sm text-gray-700">Sort by:</span>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                {SERVICE_CONSUMPTION_SORT_OPTIONS.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            {paginatedServiceConsumptions.length > 0 && (
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={
                    selectedConsumptions.length ===
                    paginatedServiceConsumptions.length
                  }
                  onChange={handleSelectAll}
                  className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                />
                <span className="text-sm text-gray-700">
                  Select All ({selectedConsumptions.length} selected)
                </span>
              </div>
            )}
          </div>
          <div className="text-sm text-gray-500">
            Showing {paginatedServiceConsumptions.length} of{" "}
            {sortedServiceConsumptions.length} records
          </div>
        </div>
      </div>

      {/* Service Consumption Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">
              Loading service consumption records...
            </p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-600 mb-2">
              <Activity className="h-8 w-8 mx-auto" />
            </div>
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={() => loadData()}
              className="mt-2 text-sm text-primary hover:text-primary-dark"
            >
              Try again
            </button>
          </div>
        ) : paginatedServiceConsumptions.length === 0 ? (
          <div className="p-8 text-center">
            <Activity className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              {searchTerm || Object.keys(filters).length > 0
                ? "No service consumption records match your search criteria."
                : "No service consumption records found. Record your first consumption to get started."}
            </p>
            {!searchTerm && Object.keys(filters).length === 0 && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-2 text-sm text-primary hover:text-primary-dark"
              >
                Record First Consumption
              </button>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      <input
                        type="checkbox"
                        checked={
                          selectedConsumptions.length ===
                          paginatedServiceConsumptions.length
                        }
                        onChange={handleSelectAll}
                        className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quantity
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedServiceConsumptions.map((consumption) => (
                    <tr
                      key={consumption.consumption_id}
                      className="hover:bg-gray-50"
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={selectedConsumptions.includes(
                            consumption.consumption_id
                          )}
                          onChange={() =>
                            handleSelectConsumption(consumption.consumption_id)
                          }
                          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {consumption.customer.first_name}{" "}
                          {consumption.customer.last_name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {consumption.customer.email}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {consumption.service.name}
                        </div>
                        {consumption.service.description && (
                          <div className="text-sm text-gray-500 max-w-xs truncate">
                            {consumption.service.description}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {new Date(consumption.consumed_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {consumption.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatCurrency(
                            consumption.total_amount,
                            consumption.price.currency
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {formatCurrency(
                            consumption.price.price_amount,
                            consumption.price.currency
                          )}{" "}
                          × {consumption.quantity}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewConsumption(consumption)}
                            className="text-gray-600 hover:text-gray-900"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          {/* <button
                            onClick={() => handleEditConsumption(consumption)}
                            className="text-blue-600 hover:text-blue-900"
                            title="Edit Consumption"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteConsumption(consumption)}
                            className="text-red-600 hover:text-red-900"
                            title="Delete Consumption"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button> */}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">{startIndex + 1}</span> to{" "}
                      <span className="font-medium">
                        {Math.min(
                          startIndex + itemsPerPage,
                          sortedServiceConsumptions.length
                        )}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">
                        {sortedServiceConsumptions.length}
                      </span>{" "}
                      results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? "z-10 bg-primary border-primary text-white"
                                : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        )
                      )}
                      <button
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && (
        <ServiceConsumptionFormModal
          customers={customers}
          services={services}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedServiceConsumption && (
        <ServiceConsumptionFormModal
          serviceConsumption={selectedServiceConsumption}
          customers={customers}
          services={services}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedServiceConsumption(null);
          }}
        />
      )}

      {showDetailsModal && selectedServiceConsumption && (
        <ServiceConsumptionDetailsModal
          serviceConsumption={selectedServiceConsumption}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedServiceConsumption(null);
          }}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmation.confirmationState.isOpen}
        onClose={deleteConfirmation.confirmationState.onCancel}
        onConfirm={deleteConfirmation.confirmationState.onConfirm}
        title={deleteConfirmation.confirmationState.title}
        message={deleteConfirmation.confirmationState.message}
        confirmText={deleteConfirmation.confirmationState.confirmText}
        cancelText={deleteConfirmation.confirmationState.cancelText}
        type={deleteConfirmation.confirmationState.type}
        isLoading={deleteConfirmation.confirmationState.isLoading}
      />
    </div>
  );
};

export default ServiceConsumptionContent;
