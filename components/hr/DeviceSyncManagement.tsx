'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import ErrorDisplay from '@/components/ui/ErrorDisplay';
import {
  getSyncFailures,
  getPersonSyncStatus,
  retrySync,
  formatSyncStatus,
  canRetrySync
} from '@/lib/device-sync';
import {
  DeviceSyncState,
  SyncFailuresResponse,
  SyncStatusResponse,
  PersonType
} from '@/types/device-sync';
import {
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  Search,
  Users,
  UserCheck
} from 'lucide-react';
import toast from 'react-hot-toast';

const DeviceSyncManagement = () => {
  const { companies } = useAuth();
  const [state, setState] = useState<DeviceSyncState>({
    isLoading: true,
    error: null,
    syncStatus: null,
    syncFailures: null,
    selectedPersonId: null,
    selectedPersonType: null,
    isRetrying: false,
    retryResult: null
  });

  const [searchForm, setSearchForm] = useState({
    personId: '',
    personType: 'employee' as PersonType
  });

  const [activeTab, setActiveTab] = useState<'search' | 'failures'>('failures');

  // Fetch sync failures on component mount
  useEffect(() => {
    fetchSyncFailures();
  }, []);

  const fetchSyncFailures = async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const failures = await getSyncFailures();
      setState(prev => ({
        ...prev,
        syncFailures: failures,
        isLoading: false
      }));
    } catch (error: any) {
      console.error('Error fetching sync failures:', error);
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to fetch sync failures',
        isLoading: false
      }));
    }
  };

  const handleSearchSync = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!searchForm.personId.trim()) {
      toast.error('Please enter a person ID');
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));
      const syncStatus = await getPersonSyncStatus(
        searchForm.personId.trim(),
        searchForm.personType
      );
      setState(prev => ({
        ...prev,
        syncStatus,
        selectedPersonId: searchForm.personId.trim(),
        selectedPersonType: searchForm.personType,
        isLoading: false
      }));
      setActiveTab('search');
    } catch (error: any) {
      console.error('Error fetching sync status:', error);
      setState(prev => ({
        ...prev,
        error: error.message || 'Failed to fetch sync status',
        isLoading: false
      }));
      toast.error('Failed to fetch sync status');
    }
  };

  const handleRetrySync = async (personId: string, personType: PersonType) => {
    try {
      setState(prev => ({ ...prev, isRetrying: true }));
      const result = await retrySync(personId, personType);
      setState(prev => ({ ...prev, retryResult: result, isRetrying: false }));
      
      if (result.status === 'success') {
        toast.success(`Sync retry successful for ${result.person_name}`);
        // Refresh data
        await fetchSyncFailures();
        if (state.selectedPersonId === personId) {
          const updatedStatus = await getPersonSyncStatus(personId, personType);
          setState(prev => ({ ...prev, syncStatus: updatedStatus }));
        }
      } else {
        toast.error(result.message || 'Retry failed');
      }
    } catch (error: any) {
      console.error('Error retrying sync:', error);
      setState(prev => ({ ...prev, isRetrying: false }));
      toast.error(error.message || 'Failed to retry sync');
    }
  };

  const renderSyncSummary = () => {
    if (!state.syncFailures) return null;

    const { failed_by_type, total_failed_syncs } = state.syncFailures;

    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle className="h-8 w-8 text-red-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-red-800">Total Failed Syncs</p>
              <p className="text-2xl font-bold text-red-900">{total_failed_syncs}</p>
            </div>
          </div>
        </div>

        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-orange-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-orange-800">Failed Employees</p>
              <p className="text-2xl font-bold text-orange-900">{failed_by_type.employees}</p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center">
            <UserCheck className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <p className="text-sm font-medium text-blue-800">Failed Customers</p>
              <p className="text-2xl font-bold text-blue-900">{failed_by_type.customers}</p>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderSearchForm = () => (
    <DashboardCard title={
      <div className="flex items-center">
        <Search className="h-5 w-5 mr-2" />
        Search Sync Status
      </div>
    }>
      <form onSubmit={handleSearchSync} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-2">
            <label htmlFor="personId" className="block text-sm font-medium text-gray-700 mb-1">
              Person ID
            </label>
            <input
              type="text"
              id="personId"
              value={searchForm.personId}
              onChange={(e) => setSearchForm(prev => ({ ...prev, personId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              placeholder="Enter employee or customer ID"
              required
            />
          </div>
          <div>
            <label htmlFor="personType" className="block text-sm font-medium text-gray-700 mb-1">
              Person Type
            </label>
            <select
              id="personType"
              value={searchForm.personType}
              onChange={(e) => setSearchForm(prev => ({ ...prev, personType: e.target.value as PersonType }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="employee">Employee</option>
              <option value="customer">Customer</option>
            </select>
          </div>
        </div>
        <button
          type="submit"
          disabled={state.isLoading}
          className="w-full md:w-auto px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
        >
          {state.isLoading ? (
            <RefreshCw className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <Search className="h-4 w-4 mr-2" />
          )}
          Search Sync Status
        </button>
      </form>
    </DashboardCard>
  );

  const renderSyncStatusResults = () => {
    if (!state.syncStatus) return null;

    const formatted = formatSyncStatus(state.syncStatus);
    const canRetry = canRetrySync(state.syncStatus);

    return (
      <DashboardCard
        title={
          <div className="flex items-center">
            <UserCheck className="h-5 w-5 mr-2" />
            Sync Status - {state.syncStatus.person_name}
          </div>
        }
      >
        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className="text-2xl font-bold text-green-600">{formatted.syncedDevices}</p>
              <p className="text-sm text-gray-600">Synced</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-yellow-600">{formatted.pendingDevices}</p>
              <p className="text-sm text-gray-600">Pending</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-red-600">{formatted.failedDevices}</p>
              <p className="text-sm text-gray-600">Failed</p>
            </div>
            <div className="text-center">
              <p className="text-2xl font-bold text-blue-600">{formatted.syncPercentage}%</p>
              <p className="text-sm text-gray-600">Success Rate</p>
            </div>
          </div>

          {/* Retry Button */}
          {canRetry && (
            <div className="flex justify-center">
              <button
                onClick={() => handleRetrySync(state.selectedPersonId!, state.selectedPersonType!)}
                disabled={state.isRetrying}
                className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50 flex items-center"
              >
                {state.isRetrying ? (
                  <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                Retry Failed Syncs
              </button>
            </div>
          )}

          {/* Device Details */}
          <div className="space-y-3">
            <h4 className="font-medium text-gray-900">Device Details</h4>
            {formatted.devices.map((device, index) => (
              <div key={device.serialNumber} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="font-medium">{device.serialNumber}</span>
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      device.connected ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {device.connected ? 'Connected' : 'Disconnected'}
                    </span>
                    {device.hasFailures && (
                      <span className="px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
                        Has Failures
                      </span>
                    )}
                    {device.hasPending && (
                      <span className="px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-800">
                        Has Pending
                      </span>
                    )}
                  </div>
                </div>
                <p className="text-sm text-gray-600">
                  {device.commandCount} command(s)
                  {device.lastCommand && (
                    <span className="ml-2">
                      Last: {device.lastCommand.command_type} ({device.lastCommand.status})
                    </span>
                  )}
                </p>
              </div>
            ))}
          </div>
        </div>
      </DashboardCard>
    );
  };

  const renderSyncFailuresTable = () => {
    if (!state.syncFailures) return null;

    const { failures } = state.syncFailures;

    if (failures.length === 0) {
      return (
        <DashboardCard title={
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 mr-2 text-green-500" />
            Sync Failures
          </div>
        }>
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <p className="text-gray-600">No sync failures found. All devices are synced successfully!</p>
          </div>
        </DashboardCard>
      );
    }

    return (
      <DashboardCard title={
        <div className="flex items-center">
          <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
          Sync Failures
        </div>
      }>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Person
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Device
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Command
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Error
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Attempt
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {failures.map((failure, index) => (
                <tr key={`${failure.person_id}-${failure.device_sn}-${index}`}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{failure.person_name}</div>
                      <div className="text-sm text-gray-500">
                        {failure.person_type} • {failure.person_id}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {failure.device_sn}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {failure.command_type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-red-600">{failure.error_message}</div>
                    <div className="text-xs text-gray-500">Attempts: {failure.err_count}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(failure.last_attempt).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {failure.can_retry ? (
                      <button
                        onClick={() => handleRetrySync(failure.person_id, failure.person_type)}
                        disabled={state.isRetrying}
                        className="text-orange-600 hover:text-orange-900 disabled:opacity-50"
                      >
                        {state.isRetrying ? 'Retrying...' : 'Retry'}
                      </button>
                    ) : (
                      <span className="text-gray-400">Cannot retry</span>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </DashboardCard>
    );
  };

  if (state.error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Device Sync Management</h1>
          <button
            onClick={fetchSyncFailures}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark flex items-center"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
        <ErrorDisplay 
          message={state.error} 
          onRetry={fetchSyncFailures}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">Device Sync Management</h1>
        <button
          onClick={fetchSyncFailures}
          disabled={state.isLoading}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark disabled:opacity-50 flex items-center"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${state.isLoading ? 'animate-spin' : ''}`} />
          Refresh
        </button>
      </div>

      {renderSyncSummary()}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('failures')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'failures'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Sync Failures
          </button>
          <button
            onClick={() => setActiveTab('search')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'search'
                ? 'border-primary text-primary'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Search Sync Status
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'search' && (
        <div className="space-y-6">
          {renderSearchForm()}
          {state.syncStatus && renderSyncStatusResults()}
        </div>
      )}

      {activeTab === 'failures' && (
        <div className="space-y-6">
          {renderSyncFailuresTable()}
        </div>
      )}
    </div>
  );
};

export default DeviceSyncManagement;
