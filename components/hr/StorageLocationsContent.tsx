"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  MapPin,
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getStorageLocations,
  deleteStorageLocation,
  getStorageLocationStatistics,
} from "@/lib/storage-location";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import { useDeleteConfirmation } from "@/hooks/useConfirmation";
import {
  StorageLocation,
  StorageLocationFilters,
  getAvailabilityLabel,
  getAvailabilityColor,
  STORAGE_LOCATION_SORT_OPTIONS,
  StorageLocationStatistics,
} from "@/types/storage-location";
import StorageLocationFormModal from "@/components/storage-location/StorageLocationFormModal";
import StorageLocationDetailsModal from "@/components/storage-location/StorageLocationDetailsModal";

const StorageLocationsContent: React.FC = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // Confirmation hooks
  const deleteConfirmation = useDeleteConfirmation();

  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<StorageLocationFilters>({});
  const [sortBy, setSortBy] = useState<string>("number_asc");
  const [statistics, setStatistics] = useState<StorageLocationStatistics>({
    total: 0,
    available: 0,
    unavailable: 0,
  });

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedStorageLocation, setSelectedStorageLocation] =
    useState<StorageLocation | null>(null);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  useEffect(() => {
    loadData();
  }, [filters, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await getStorageLocations({
        ...filters,
        search: searchTerm || undefined,
      });

      setStorageLocations(response.extend.storage_locations);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load storage locations. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await getStorageLocationStatistics();
      setStatistics(stats);
    } catch (err) {
      console.error("Error loading statistics:", err);
    }
  };

  useEffect(() => {
    loadStatistics();
  }, []);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleFilterChange = (newFilters: Partial<StorageLocationFilters>) => {
    setFilters({ ...filters, ...newFilters });
    setCurrentPage(1);
  };

  const handleDeleteStorageLocation = async (
    storageLocation: StorageLocation
  ) => {
    const confirmed = await deleteConfirmation.confirmDelete(
      `Location ${storageLocation.location_number}`,
      "storage location"
    );

    if (!confirmed) {
      deleteConfirmation.hideConfirmation();
      return;
    }

    try {
      deleteConfirmation.setLoading(true);
      await deleteStorageLocation(storageLocation.location_id);
      await loadData();
      await loadStatistics();
      deleteConfirmation.hideConfirmation();
    } catch (error) {
      console.error("Error deleting storage location:", error);
      deleteConfirmation.hideConfirmation();
      // TODO: Implement error toast notification
    }
  };

  // Note: Toggle availability is not supported by the API
  // Availability is managed automatically when items are stored/retrieved

  const handleViewStorageLocation = (storageLocation: StorageLocation) => {
    setSelectedStorageLocation(storageLocation);
    setShowDetailsModal(true);
  };

  const handleEditStorageLocation = (storageLocation: StorageLocation) => {
    setSelectedStorageLocation(storageLocation);
    setShowEditModal(true);
  };

  const handleFormSuccess = async () => {
    await loadData();
    await loadStatistics();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedStorageLocation(null);
  };

  const sortedStorageLocations = [...storageLocations].sort((a, b) => {
    switch (sortBy) {
      case "number_asc":
        return a.location_number.localeCompare(b.location_number);
      case "number_desc":
        return b.location_number.localeCompare(a.location_number);
      case "available_first":
        return a.is_available === b.is_available ? 0 : a.is_available ? -1 : 1;
      case "unavailable_first":
        return a.is_available === b.is_available ? 0 : a.is_available ? 1 : -1;
      case "created_desc":
        return (
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      case "created_asc":
        return (
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        );
      default:
        return 0;
    }
  });

  const totalPages = Math.ceil(sortedStorageLocations.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedStorageLocations = sortedStorageLocations.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="space-y-6">
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Storage Locations Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Manage storage locations and track availability.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => loadData()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center px-4 py-2 rounded-md text-sm font-medium text-white bg-primary hover:bg-primary-dark"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Location
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1 max-w-lg">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search locations..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="number_asc">Location Number (A-Z)</option>
            <option value="number_desc">Location Number (Z-A)</option>
            <option value="created_desc">Newest First</option>
            <option value="created_asc">Oldest First</option>
          </select>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg border">
          <p className="text-sm text-gray-600">Total Locations</p>
          <p className="text-2xl font-bold">{statistics.total}</p>
        </div>
        <div className="bg-white p-6 rounded-lg border">
          <p className="text-sm text-gray-600">Available</p>
          <p className="text-2xl font-bold text-green-600">
            {statistics.available}
          </p>
        </div>
        <div className="bg-white p-6 rounded-lg border">
          <p className="text-sm text-gray-600">Unavailable</p>
          <p className="text-2xl font-bold text-red-600">
            {statistics.unavailable}
          </p>
        </div>
      </div>

      {/* Storage Locations Table */}
      {loading ? (
        <div className="bg-white rounded-lg border border-gray-200 p-8">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-8 w-8 text-gray-400 animate-spin" />
            <span className="ml-2 text-gray-600">
              Loading storage locations...
            </span>
          </div>
        </div>
      ) : error ? (
        <div className="bg-white rounded-lg border border-gray-200 p-8">
          <div className="text-center">
            <div className="text-red-600 mb-2">
              <XCircle className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Data
            </h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => loadData()}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </button>
          </div>
        </div>
      ) : (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          {storageLocations.length === 0 ? (
            <div className="p-8 text-center">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No Storage Locations
              </h3>
              <p className="text-gray-600 mb-4">
                Get started by creating your first storage location.
              </p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Storage Location
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Notes
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Created
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {storageLocations.map((location) => (
                    <tr key={location.location_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-primary-light flex items-center justify-center">
                              <MapPin className="h-5 w-5 text-primary" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              Location #{location.location_number}
                            </div>
                            <div className="text-sm text-gray-500">
                              Storage Location
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAvailabilityColor(
                            location.is_available
                          )}`}
                        >
                          {location.is_available ? (
                            <CheckCircle className="h-3 w-3 mr-1" />
                          ) : (
                            <XCircle className="h-3 w-3 mr-1" />
                          )}
                          {getAvailabilityLabel(location.is_available)}
                        </span>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">
                          {location.notes || (
                            <span className="text-gray-400 italic">
                              No notes
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(location.created_at).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewStorageLocation(location)}
                            className="text-primary hover:text-primary-dark p-1 rounded"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditStorageLocation(location)}
                            className="text-blue-600 hover:text-blue-900 p-1 rounded"
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleDeleteStorageLocation(location)
                            }
                            className="text-red-600 hover:text-red-900 p-1 rounded"
                            title="Delete"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      )}

      {/* Modals */}
      {showCreateModal && (
        <StorageLocationFormModal
          onSuccess={() => {
            setShowCreateModal(false);
            loadData();
            loadStatistics();
          }}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedStorageLocation && (
        <StorageLocationFormModal
          storageLocation={selectedStorageLocation}
          onSuccess={() => {
            setShowEditModal(false);
            setSelectedStorageLocation(null);
            loadData();
            loadStatistics();
          }}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedStorageLocation(null);
          }}
        />
      )}

      {showDetailsModal && selectedStorageLocation && (
        <StorageLocationDetailsModal
          storageLocation={selectedStorageLocation}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedStorageLocation(null);
          }}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmation.confirmationState.isOpen}
        onClose={deleteConfirmation.confirmationState.onCancel}
        onConfirm={deleteConfirmation.confirmationState.onConfirm}
        title={deleteConfirmation.confirmationState.title}
        message={deleteConfirmation.confirmationState.message}
        confirmText={deleteConfirmation.confirmationState.confirmText}
        cancelText={deleteConfirmation.confirmationState.cancelText}
        type={deleteConfirmation.confirmationState.type}
        isLoading={deleteConfirmation.confirmationState.isLoading}
      />
    </div>
  );
};

export default StorageLocationsContent;
