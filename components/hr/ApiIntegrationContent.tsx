"use client";

import React, { useState, useEffect } from "react";
import { Plus, Key, ExternalLink, AlertCircle } from "lucide-react";
import {
  ApiIntegrationState,
  ApiClient,
  CreateApiClientResponse,
  ResetSecretResponse,
} from "@/types/api-integration";
import {
  getAllApiClients,
  resetClientSecret,
  deleteApiClient,
  extractAppName,
} from "@/lib/api-integration";
import toast from "react-hot-toast";
import DashboardCard from "@/components/ui/DashboardCard";
import ErrorDisplay from "@/components/ui/ErrorDisplay";
import ApiClientTable from "./api-integration/ApiClientTable";
import CreateApiClientModal from "./api-integration/CreateApiClientModal";
import SecretDisplay from "./api-integration/SecretDisplay";
import ConfirmationModal from "./api-integration/ConfirmationModal";

const ApiIntegrationContent: React.FC = () => {
  const [state, setState] = useState<ApiIntegrationState>({
    clients: [],
    loading: true,
    error: null,
    showCreateModal: false,
    showSecretDisplay: false,
    currentSecret: null,
    showDeleteConfirm: false,
    showResetConfirm: false,
    clientToDelete: null,
    clientToReset: null,
    isDeleting: false,
    isResetting: false,
  });

  // Fetch API clients on component mount
  useEffect(() => {
    fetchApiClients();
  }, []);

  const fetchApiClients = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await getAllApiClients();
      setState((prev) => ({
        ...prev,
        clients: response.clients || [],
        loading: false,
      }));
    } catch (error: any) {
      console.error("Error fetching API clients:", error);
      setState((prev) => ({
        ...prev,
        error: error.message || "Failed to load API clients",
        loading: false,
      }));
    }
  };

  // Handle create API client success
  const handleCreateSuccess = (response: CreateApiClientResponse) => {
    setState((prev) => ({
      ...prev,
      currentSecret: response,
      showSecretDisplay: true,
      showCreateModal: false,
    }));

    // Refresh the clients list
    fetchApiClients();
  };

  // Handle reset secret
  const handleResetSecret = (client: ApiClient) => {
    setState((prev) => ({
      ...prev,
      clientToReset: client,
      showResetConfirm: true,
    }));
  };

  const confirmResetSecret = async () => {
    if (!state.clientToReset) return;

    try {
      setState((prev) => ({ ...prev, isResetting: true }));
      const response = await resetClientSecret(state.clientToReset.client_id);

      setState((prev) => ({
        ...prev,
        currentSecret: response,
        showSecretDisplay: true,
        showResetConfirm: false,
        clientToReset: null,
        isResetting: false,
      }));

      toast.success("Client secret reset successfully!");

      // Refresh the clients list
      fetchApiClients();
    } catch (error: any) {
      console.error("Error resetting client secret:", error);
      toast.error(error.message || "Failed to reset client secret");
      setState((prev) => ({
        ...prev,
        isResetting: false,
        showResetConfirm: false,
        clientToReset: null,
      }));
    }
  };

  // Handle delete client
  const handleDeleteClient = (client: ApiClient) => {
    setState((prev) => ({
      ...prev,
      clientToDelete: client,
      showDeleteConfirm: true,
    }));
  };

  const confirmDeleteClient = async () => {
    if (!state.clientToDelete) return;

    try {
      setState((prev) => ({ ...prev, isDeleting: true }));
      await deleteApiClient(state.clientToDelete.client_id);

      setState((prev) => ({
        ...prev,
        showDeleteConfirm: false,
        clientToDelete: null,
        isDeleting: false,
      }));

      toast.success("API client deleted successfully!");

      // Refresh the clients list
      fetchApiClients();
    } catch (error: any) {
      console.error("Error deleting API client:", error);
      toast.error(error.message || "Failed to delete API client");
      setState((prev) => ({
        ...prev,
        isDeleting: false,
        showDeleteConfirm: false,
        clientToDelete: null,
      }));
    }
  };

  // Modal handlers
  const openCreateModal = () =>
    setState((prev) => ({ ...prev, showCreateModal: true }));
  const closeCreateModal = () =>
    setState((prev) => ({ ...prev, showCreateModal: false }));

  const closeSecretDisplay = () =>
    setState((prev) => ({
      ...prev,
      showSecretDisplay: false,
      currentSecret: null,
    }));

  const closeDeleteConfirm = () =>
    setState((prev) => ({
      ...prev,
      showDeleteConfirm: false,
      clientToDelete: null,
    }));

  const closeResetConfirm = () =>
    setState((prev) => ({
      ...prev,
      showResetConfirm: false,
      clientToReset: null,
    }));

  if (state.error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              API Integration
            </h1>
            <p className="text-gray-600 mt-1">
              Manage API client credentials for your applications
            </p>
          </div>
        </div>

        <ErrorDisplay
          title="Failed to Load API Clients"
          message={state.error}
          onRetry={fetchApiClients}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">API Integration</h1>
          <p className="text-gray-600 mt-1">
            Manage API client credentials for your applications
          </p>
        </div>
        <button
          onClick={openCreateModal}
          className="inline-flex items-center px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
        >
          <Plus className="h-5 w-5 mr-2" />
          Create New App
        </button>
      </div>

      {/* Overview Card */}
      <DashboardCard title="API Integration Overview">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <Key className="h-6 w-6 text-primary mt-1" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  Secure API Access
                </h3>
                <p className="text-sm text-gray-600">
                  Create API clients to securely access KaziSync data from your
                  applications. Each client receives a unique ID and secret for
                  authentication.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <AlertCircle className="h-6 w-6 text-amber-500 mt-1" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900 mb-2">
                  One-Time Secret Display
                </h3>
                <p className="text-sm text-gray-600">
                  Client secrets are shown only once after creation or reset.
                  Make sure to copy and store them securely.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <ExternalLink className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold text-blue-900">
                  API Documentation
                </h3>
              </div>
              <p className="text-sm text-blue-700 mb-3">
                Learn how to integrate with the KaziSync API using your client
                credentials.
              </p>
              <a
                href="https://api.kazisync.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
              >
                View Documentation
                <ExternalLink className="ml-1 h-4 w-4" />
              </a>
            </div>

            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-2">Quick Stats</h3>
              <div className="text-sm text-gray-600">
                <div className="flex justify-between">
                  <span>Total API Clients:</span>
                  <span className="font-medium">{state.clients.length}</span>
                </div>
                <div className="flex justify-between mt-1">
                  <span>Active Clients:</span>
                  <span className="font-medium">
                    {state.clients.filter((c) => c.last_used_at).length}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardCard>

      {/* API Clients Table */}
      <ApiClientTable
        clients={state.clients}
        loading={state.loading}
        onResetSecret={handleResetSecret}
        onDeleteClient={handleDeleteClient}
      />

      {/* Modals */}
      <CreateApiClientModal
        isOpen={state.showCreateModal}
        onClose={closeCreateModal}
        onSuccess={handleCreateSuccess}
      />

      {state.showSecretDisplay && state.currentSecret && (
        <SecretDisplay
          clientId={state.currentSecret.client_id}
          clientSecret={state.currentSecret.client_secret}
          onClose={closeSecretDisplay}
          isReset={"reset_at" in state.currentSecret}
        />
      )}

      <ConfirmationModal
        isOpen={state.showDeleteConfirm}
        onClose={closeDeleteConfirm}
        onConfirm={confirmDeleteClient}
        title="Delete API Client"
        message={`Are you sure you want to delete "${
          state.clientToDelete ? extractAppName(state.clientToDelete.name) : ""
        }"? This will permanently revoke access for this client and cannot be undone.`}
        confirmText="Delete Client"
        isDestructive={true}
        isLoading={state.isDeleting}
      />

      <ConfirmationModal
        isOpen={state.showResetConfirm}
        onClose={closeResetConfirm}
        onConfirm={confirmResetSecret}
        title="Reset Client Secret"
        message={`Are you sure you want to reset the secret for "${
          state.clientToReset ? extractAppName(state.clientToReset.name) : ""
        }"? The current secret will be invalidated and a new one will be generated.`}
        confirmText="Reset Secret"
        isDestructive={false}
        isLoading={state.isResetting}
      />
    </div>
  );
};

export default ApiIntegrationContent;
