'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import OrgUnitModal from '@/components/org-unit/OrgUnitModal';
import OrgUnitTree from '@/components/org-unit/OrgUnitTree';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import { Building2, AlertTriangle } from 'lucide-react';
import {
  OrgUnit,
  DepartmentStructure,
  DepartmentOption,
  ParentUnitOption,
  OrgUnitModalState,
} from '@/types/org-unit';
import { getDepartmentStructure, deleteOrgUnit } from '@/lib/org-units';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface DepartmentResponse {
  departments: Department[];
  pagination: {
    has_next: boolean;
    has_prev: boolean;
    page: number;
    pages: number;
    per_page: number;
    total_count: number;
  };
  success: boolean;
}

const OrganizationalUnitsContent: React.FC = () => {
  const { companies } = useAuth();
  
  // State management
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [departmentStructure, setDepartmentStructure] = useState<DepartmentStructure | null>(null);
  
  // Loading states
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isLoadingStructure, setIsLoadingStructure] = useState(false);
  const [isDeletingUnit, setIsDeletingUnit] = useState(false);
  
  // Modal states
  const [modalState, setModalState] = useState<OrgUnitModalState>({
    isOpen: false,
    mode: 'create',
    unit: null,
    parentUnit: null,
    department: null,
  });
  
  // Confirmation modal state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    unit: OrgUnit | null;
  }>({
    isOpen: false,
    unit: null,
  });
  
  // Error handling
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // Fetch departments on component mount
  useEffect(() => {
    fetchDepartments();
  }, [companies]);

  // Fetch department structure when department is selected
  useEffect(() => {
    if (selectedDepartment) {
      fetchDepartmentStructure(selectedDepartment.department_id);
    } else {
      setDepartmentStructure(null);
    }
  }, [selectedDepartment]);

  const fetchDepartments = async () => {
    try {
      setIsLoadingDepartments(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const response = await apiGet<DepartmentResponse>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.success && response.departments) {
        setDepartments(response.departments);
        
        // Auto-select first department if available
        if (response.departments.length > 0 && !selectedDepartment) {
          setSelectedDepartment(response.departments[0]);
        }
      } else {
        throw new Error('Failed to fetch departments');
      }
    } catch (error: any) {
      console.error('Error fetching departments:', error);

      // Import the utility to check if error should be displayed
      const { shouldDisplayError } = await import('@/lib/auth-utils');

      // Only show error if it's not a session expiration or expected empty state
      if (shouldDisplayError(error)) {
        setError(error.message || 'Failed to fetch departments');
      }
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  const fetchDepartmentStructure = async (departmentId: string) => {
    try {
      setIsLoadingStructure(true);
      setError('');

      const structure = await getDepartmentStructure(departmentId);
      setDepartmentStructure(structure);
    } catch (error: any) {
      console.error('Error fetching department structure:', error);

      // Import the utility to check if error should be displayed
      const { shouldDisplayError } = await import('@/lib/auth-utils');

      // Only show error if it's not a session expiration or expected empty state
      if (shouldDisplayError(error)) {
        setError(error.message || 'Failed to fetch organizational structure');
      }

      setDepartmentStructure(null);
    } finally {
      setIsLoadingStructure(false);
    }
  };

  const handleDepartmentChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const departmentId = e.target.value;
    const department = departments.find(d => d.department_id === departmentId);
    setSelectedDepartment(department || null);
  };

  const handleAddUnit = (departmentId: string, parentUnit?: OrgUnit) => {
    const department = departments.find(d => d.department_id === departmentId);
    
    setModalState({
      isOpen: true,
      mode: 'create',
      unit: null,
      parentUnit: parentUnit ? {
        org_unit_id: parentUnit.org_unit_id,
        name: parentUnit.name,
        level: parentUnit.level,
        full_path: `${department?.name} > ${parentUnit.name}`,
      } : null,
      department: department ? {
        department_id: department.department_id,
        name: department.name,
      } : null,
    });
  };

  const handleEditUnit = (unit: OrgUnit) => {
    setModalState({
      isOpen: true,
      mode: 'edit',
      unit,
      parentUnit: null,
      department: null,
    });
  };

  const handleDeleteUnit = (unit: OrgUnit) => {
    setDeleteConfirmation({
      isOpen: true,
      unit,
    });
  };

  const confirmDeleteUnit = async () => {
    if (!deleteConfirmation.unit) return;

    try {
      setIsDeletingUnit(true);
      setError('');

      await deleteOrgUnit(deleteConfirmation.unit.org_unit_id);
      
      setSuccessMessage('Organizational unit deleted successfully');
      
      // Refresh the structure
      if (selectedDepartment) {
        await fetchDepartmentStructure(selectedDepartment.department_id);
      }
      
      // Close confirmation modal
      setDeleteConfirmation({ isOpen: false, unit: null });
      
      // Clear success message after 3 seconds
      setTimeout(() => setSuccessMessage(''), 3000);
    } catch (error: any) {
      console.error('Error deleting unit:', error);
      setError(error.message || 'Failed to delete organizational unit');
    } finally {
      setIsDeletingUnit(false);
    }
  };

  const handleModalSuccess = async () => {
    setModalState({ isOpen: false, mode: 'create', unit: null, parentUnit: null, department: null });
    
    // Refresh the structure
    if (selectedDepartment) {
      await fetchDepartmentStructure(selectedDepartment.department_id);
    }
    
    setSuccessMessage('Operation completed successfully');
    setTimeout(() => setSuccessMessage(''), 3000);
  };

  const handleModalClose = () => {
    setModalState({ isOpen: false, mode: 'create', unit: null, parentUnit: null, department: null });
  };

  return (
    <div className="space-y-6">
      {/* Modals */}
      <OrgUnitModal
        isOpen={modalState.isOpen}
        onClose={handleModalClose}
        onSuccess={handleModalSuccess}
        orgUnit={modalState.unit}
        isEditing={modalState.mode === 'edit'}
        selectedDepartment={modalState.department}
        selectedParentUnit={modalState.parentUnit}
      />

      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        onClose={() => setDeleteConfirmation({ isOpen: false, unit: null })}
        onConfirm={confirmDeleteUnit}
        title="Delete Organizational Unit"
        message={
          deleteConfirmation.unit
            ? `Are you sure you want to delete "${deleteConfirmation.unit.name}"? This action cannot be undone and will also delete all sub-units within this unit.`
            : ''
        }
        confirmText="Delete Unit"
        cancelText="Cancel"
        type="danger"
        isLoading={isDeletingUnit}
      />

      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">Organizational Units</h1>
          <p className="text-secondary mt-1">
            Manage your organizational structure with departments, units, and sub-units
          </p>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-md">
          {successMessage}
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-md">
          {error}
        </div>
      )}

      {/* Department Selection */}
      <DashboardCard title="Select Department">
        {isLoadingDepartments ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading departments...</p>
          </div>
        ) : departments.length === 0 ? (
          <div className="py-8 text-center">
            <Building2 className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-secondary">No departments found.</p>
            <p className="text-sm text-gray-500 mt-1">
              Please create a department first before managing organizational units.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div>
              <label htmlFor="department-select" className="block text-sm font-medium text-secondary-dark mb-2">
                Choose a department to manage its organizational units:
              </label>
              <select
                id="department-select"
                value={selectedDepartment?.department_id || ''}
                onChange={handleDepartmentChange}
                className="w-full max-w-md px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="">Select a department</option>
                {departments.map((dept) => (
                  <option key={dept.department_id} value={dept.department_id}>
                    {dept.name}
                  </option>
                ))}
              </select>
            </div>

            {selectedDepartment && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="flex items-start space-x-3">
                  <Building2 className="w-5 h-5 text-blue-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-blue-900">{selectedDepartment.name}</h4>
                    {selectedDepartment.description && (
                      <p className="text-sm text-blue-700 mt-1">{selectedDepartment.description}</p>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </DashboardCard>

      {/* Organizational Structure */}
      {selectedDepartment && (
        <DashboardCard title="Organizational Structure">
          {departmentStructure ? (
            <OrgUnitTree
              departmentStructure={departmentStructure}
              onAddUnit={handleAddUnit}
              onEditUnit={handleEditUnit}
              onDeleteUnit={handleDeleteUnit}
              isLoading={isLoadingStructure}
            />
          ) : isLoadingStructure ? (
            <div className="py-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <p className="mt-2 text-secondary">Loading organizational structure...</p>
            </div>
          ) : (
            <div className="py-8 text-center">
              <AlertTriangle className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-secondary">Failed to load organizational structure.</p>
              <button
                onClick={() => selectedDepartment && fetchDepartmentStructure(selectedDepartment.department_id)}
                className="mt-2 text-primary hover:text-primary-dark"
              >
                Try again
              </button>
            </div>
          )}
        </DashboardCard>
      )}

      {/* Instructions */}
      {!selectedDepartment && departments.length > 0 && (
        <DashboardCard title="Getting Started">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-sm font-semibold text-primary">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Select a Department</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Choose a department from the dropdown above to view and manage its organizational units.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-sm font-semibold text-primary">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Create Units</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Add organizational units to structure your department. You can create multiple levels of units and sub-units.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                <span className="text-sm font-semibold text-primary">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900">Manage Hierarchy</h4>
                <p className="text-sm text-gray-600 mt-1">
                  Use the tree view to visualize your organizational structure and manage units at different levels.
                </p>
              </div>
            </div>
          </div>
        </DashboardCard>
      )}
    </div>
  );
};

export default OrganizationalUnitsContent;
