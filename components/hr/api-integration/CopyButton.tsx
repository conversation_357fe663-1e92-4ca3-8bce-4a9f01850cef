'use client';

import React, { useState } from 'react';
import { Copy, Check, AlertCircle } from 'lucide-react';
import { copyToClipboard } from '@/lib/api-integration';
import { CopyButtonProps } from '@/types/api-integration';
import toast from 'react-hot-toast';

const CopyButton: React.FC<CopyButtonProps> = ({
  text,
  label = 'Copy',
  className = '',
  size = 'md'
}) => {
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleCopy = async () => {
    if (isLoading || copied) return;

    try {
      setIsLoading(true);
      await copyToClipboard(text);
      setCopied(true);
      toast.success(`${label} copied to clipboard!`);
      
      // Reset the copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    } catch (error: any) {
      toast.error(error.message || 'Failed to copy to clipboard');
    } finally {
      setIsLoading(false);
    }
  };

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6'
  };

  const buttonSizeClasses = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3'
  };

  const getIcon = () => {
    if (isLoading) {
      return (
        <div className={`animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 ${sizeClasses[size]}`} />
      );
    }
    
    if (copied) {
      return <Check className={`${sizeClasses[size]} text-green-600`} />;
    }
    
    return <Copy className={`${sizeClasses[size]} text-gray-500`} />;
  };

  const getButtonClasses = () => {
    const baseClasses = `inline-flex items-center justify-center rounded-md border transition-all duration-200 ${buttonSizeClasses[size]} ${className}`;
    
    if (copied) {
      return `${baseClasses} border-green-300 bg-green-50 hover:bg-green-100`;
    }
    
    if (isLoading) {
      return `${baseClasses} border-gray-300 bg-gray-50 cursor-not-allowed`;
    }
    
    return `${baseClasses} border-gray-300 bg-white hover:bg-gray-50 hover:border-gray-400`;
  };

  return (
    <button
      onClick={handleCopy}
      disabled={isLoading || copied}
      className={getButtonClasses()}
      title={copied ? 'Copied!' : `Copy ${label.toLowerCase()}`}
      type="button"
    >
      {getIcon()}
    </button>
  );
};

export default CopyButton;
