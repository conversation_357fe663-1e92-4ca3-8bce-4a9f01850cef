'use client';

import React from 'react';
import { X, AlertTriangle, ExternalLink, Eye, EyeOff } from 'lucide-react';
import { SecretDisplayProps } from '@/types/api-integration';
import CopyButton from './CopyButton';

const SecretDisplay: React.FC<SecretDisplayProps> = ({
  clientId,
  clientSecret,
  onClose,
  isReset = false
}) => {
  const [showSecret, setShowSecret] = React.useState(true);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-6 w-6 text-amber-600" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900">
              {isReset ? 'Client Secret Reset' : 'API Client Created'}
            </h3>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Warning Banner */}
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4 mb-6">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-amber-400 mt-0.5" />
              <div className="ml-3">
                <h4 className="text-sm font-medium text-amber-800">
                  Important: Save Your Credentials Now
                </h4>
                <p className="text-sm text-amber-700 mt-1">
                  This is the only time you will see the client secret. Make sure to copy and store it securely. 
                  If you lose it, you'll need to reset the secret to get a new one.
                </p>
              </div>
            </div>
          </div>

          {/* Success Message */}
          <div className="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <div className="w-5 h-5 bg-green-400 rounded-full flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-green-800">
                  {isReset ? 'Client secret has been reset successfully!' : 'API client created successfully!'}
                </h4>
                <p className="text-sm text-green-700 mt-1">
                  You can now use these credentials to authenticate with the KaziSync API.
                </p>
              </div>
            </div>
          </div>

          {/* Credentials */}
          <div className="space-y-4">
            {/* Client ID */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Client ID
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-50 border border-gray-200 rounded-md p-3">
                  <code className="text-sm font-mono text-gray-900 break-all">
                    {clientId}
                  </code>
                </div>
                <CopyButton text={clientId} label="Client ID" />
              </div>
            </div>

            {/* Client Secret */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Client Secret
              </label>
              <div className="flex items-center space-x-2">
                <div className="flex-1 bg-gray-50 border border-gray-200 rounded-md p-3">
                  <code className="text-sm font-mono text-gray-900 break-all">
                    {showSecret ? clientSecret : '•'.repeat(clientSecret.length)}
                  </code>
                </div>
                <button
                  onClick={() => setShowSecret(!showSecret)}
                  className="p-2 border border-gray-300 bg-white rounded-md hover:bg-gray-50 transition-colors"
                  title={showSecret ? 'Hide secret' : 'Show secret'}
                >
                  {showSecret ? (
                    <EyeOff className="h-5 w-5 text-gray-500" />
                  ) : (
                    <Eye className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                <CopyButton text={clientSecret} label="Client Secret" />
              </div>
            </div>
          </div>

          {/* API Documentation Link */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-start space-x-3">
              <ExternalLink className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-1">
                  Ready to integrate?
                </h4>
                <p className="text-sm text-blue-700 mb-2">
                  Check out our API documentation to learn how to use these credentials.
                </p>
                <a
                  href="https://api.kazisync.com/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500"
                >
                  View API Documentation
                  <ExternalLink className="ml-1 h-4 w-4" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
            >
              I've Saved My Credentials
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecretDisplay;
