'use client';

import React from 'react';
import { RotateCcw, Trash2, Clock, Calendar, ExternalLink } from 'lucide-react';
import { ApiClientTableProps } from '@/types/api-integration';
import { formatApiClientDate, extractAppName } from '@/lib/api-integration';
import CopyButton from './CopyButton';
import LoadingSpinner from '@/components/ui/LoadingSpinner';

const ApiClientTable: React.FC<ApiClientTableProps> = ({
  clients,
  loading,
  onResetSecret,
  onDeleteClient
}) => {
  if (loading) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-8">
        <LoadingSpinner size="lg" message="Loading API clients..." />
      </div>
    );
  }

  if (clients.length === 0) {
    return (
      <div className="bg-white rounded-xl border border-gray-200 p-8">
        <div className="text-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <ExternalLink className="h-8 w-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            No API Clients Yet
          </h3>
          <p className="text-gray-600 mb-4">
            Create your first API client to start integrating with the KaziSync API.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4 max-w-md mx-auto">
            <p className="text-sm text-blue-700">
              API clients allow you to authenticate and access KaziSync data programmatically. 
              Each client gets a unique ID and secret for secure access.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <h3 className="text-lg font-semibold text-gray-900">
          API Clients ({clients.length})
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          Manage your API client credentials and access tokens
        </p>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Application
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client ID
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Used
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {clients.map((client) => (
              <tr key={client.client_id} className="hover:bg-gray-50">
                {/* Application Name */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-primary-light bg-opacity-20 rounded-lg flex items-center justify-center">
                        <ExternalLink className="h-4 w-4 text-primary" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">
                        {extractAppName(client.name)}
                      </div>
                    </div>
                  </div>
                </td>

                {/* Client ID */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center space-x-2">
                    <code className="text-sm font-mono text-gray-600 bg-gray-100 px-2 py-1 rounded">
                      {client.client_id.substring(0, 8)}...
                    </code>
                    <CopyButton 
                      text={client.client_id} 
                      label="Client ID" 
                      size="sm"
                    />
                  </div>
                </td>

                {/* Created Date */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="h-4 w-4 mr-2" />
                    {formatApiClientDate(client.created_at)}
                  </div>
                </td>

                {/* Last Used */}
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="h-4 w-4 mr-2" />
                    {client.last_used_at ? (
                      formatApiClientDate(client.last_used_at)
                    ) : (
                      <span className="text-gray-400 italic">Never</span>
                    )}
                  </div>
                </td>

                {/* Actions */}
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    {/* Reset Secret Button */}
                    <button
                      onClick={() => onResetSecret(client)}
                      className="inline-flex items-center px-3 py-1.5 border border-amber-300 text-amber-700 bg-amber-50 rounded-md hover:bg-amber-100 transition-colors text-xs font-medium"
                      title="Reset client secret"
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Reset Secret
                    </button>

                    {/* Delete Button */}
                    <button
                      onClick={() => onDeleteClient(client)}
                      className="inline-flex items-center px-3 py-1.5 border border-red-300 text-red-700 bg-red-50 rounded-md hover:bg-red-100 transition-colors text-xs font-medium"
                      title="Delete API client"
                    >
                      <Trash2 className="h-3 w-3 mr-1" />
                      Delete
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer with API Documentation Link */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center text-sm text-gray-600">
            <ExternalLink className="h-4 w-4 mr-2" />
            <span>Need help integrating?</span>
          </div>
          <a
            href="https://api.kazisync.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center text-sm font-medium text-primary hover:text-primary-dark"
          >
            View API Documentation
            <ExternalLink className="ml-1 h-4 w-4" />
          </a>
        </div>
      </div>
    </div>
  );
};

export default ApiClientTable;
