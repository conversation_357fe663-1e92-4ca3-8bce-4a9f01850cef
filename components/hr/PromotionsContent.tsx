"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Power,
  PowerOff,
  Copy,
  RefreshCw,
  Award,
  Gift,
  TrendingUp,
  Calendar,
  Target,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getPromotions,
  activatePromotion,
  deactivatePromotion,
  duplicatePromotion,
} from "@/lib/promotion";
import {
  Promotion,
  PromotionFilters,
  getRuleTypeLabel,
  getRewardTypeLabel,
  getPromotionStatusColor,
  getPromotionStatusLabel,
  getRuleTypeColor,
  getRewardTypeColor,
  formatPromotionName,
  formatTriggerValue,
  formatRewardValue,
  formatPromotionPeriod,
  isPromotionActive,
  isPromotionExpired,
  RULE_TYPES,
  REWARD_TYPES,
} from "@/types/promotion";
import { CUSTOMER_SEGMENTS } from "@/types/customer";
import PromotionFormModal from "@/components/promotion/PromotionFormModal";
import PromotionDetailsModal from "@/components/promotion/PromotionDetailsModal";

interface PromotionsContentProps {}

const PromotionsContent: React.FC<PromotionsContentProps> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);
  // State management
  const [promotions, setPromotions] = useState<Promotion[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<PromotionFilters>({});

  // Modal state
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState<Promotion | null>(
    null
  );

  // Load promotions
  const loadPromotions = async (newFilters?: PromotionFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      const filtersToUse = { ...newFilters };
      if (searchTerm.trim()) {
        filtersToUse.search = searchTerm;
      }

      const response = await getPromotions(filtersToUse);
      setPromotions(response.extend.promotions);
    } catch (err) {
      console.error("Error loading promotions:", err);
      setError(
        err instanceof Error ? err.message : "Failed to load promotions"
      );
      setPromotions([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadPromotions();
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    loadPromotions(filters);
  };

  // Handle filter change
  const handleFilterChange = (key: keyof PromotionFilters, value: any) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    loadPromotions(newFilters);
  };

  // Clear filters
  const clearFilters = () => {
    const clearedFilters: PromotionFilters = {};
    setFilters(clearedFilters);
    setSearchTerm("");
    loadPromotions(clearedFilters);
  };

  // Handle promotion actions
  const handleActivatePromotion = async (ruleId: string) => {
    try {
      await activatePromotion(ruleId);
      loadPromotions(filters);
    } catch (err) {
      console.error("Error activating promotion:", err);
      alert("Failed to activate promotion");
    }
  };

  const handleDeactivatePromotion = async (ruleId: string) => {
    if (confirm("Are you sure you want to deactivate this promotion?")) {
      try {
        await deactivatePromotion(ruleId);
        loadPromotions(filters);
      } catch (err) {
        console.error("Error deactivating promotion:", err);
        alert("Failed to deactivate promotion");
      }
    }
  };

  const handleDuplicatePromotion = async (ruleId: string, name: string) => {
    try {
      await duplicatePromotion(ruleId, `${name} (Copy)`);
      loadPromotions(filters);
    } catch (err) {
      console.error("Error duplicating promotion:", err);
      alert("Failed to duplicate promotion");
    }
  };

  // Handle modal actions
  const handleCreatePromotion = () => {
    setSelectedPromotion(null);
    setShowCreateModal(true);
  };

  const handleEditPromotion = (promotion: Promotion) => {
    setSelectedPromotion(promotion);
    setShowEditModal(true);
  };

  const handleViewPromotion = (promotion: Promotion) => {
    setSelectedPromotion(promotion);
    setShowDetailsModal(true);
  };

  const handlePromotionSaved = () => {
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedPromotion(null);
    loadPromotions(filters);
  };

  // Refresh data
  const handleRefresh = () => {
    loadPromotions(filters);
  };

  // Get promotion statistics
  const activePromotions = promotions.filter((p) => isPromotionActive(p));
  const expiredPromotions = promotions.filter((p) => isPromotionExpired(p));
  const inactivePromotions = promotions.filter((p) => !p.is_active);

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">
            Promotions Management
          </h1>
          <p className="mt-1 text-sm text-gray-500">
            Create and manage customer loyalty promotions and rewards
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button
            onClick={handleCreatePromotion}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Promotion
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Award className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Total Promotions
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {promotions.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Power className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Active Promotions
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {activePromotions.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <PowerOff className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Inactive Promotions
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {inactivePromotions.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Calendar className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">
                Expired Promotions
              </p>
              <p className="text-2xl font-bold text-gray-900">
                {expiredPromotions.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          {/* Search */}
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
                placeholder="Search promotions by name or description..."
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
                showFilters
                  ? "border-primary text-primary bg-primary-light"
                  : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
              }`}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
            {(filters.is_active !== undefined ||
              filters.rule_type ||
              filters.reward_type ||
              filters.applicable_customer_segments) && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Status Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <select
                  value={filters.is_active?.toString() || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "is_active",
                      e.target.value ? e.target.value === "true" : undefined
                    )
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Statuses</option>
                  <option value="true">Active Only</option>
                  <option value="false">Inactive Only</option>
                </select>
              </div>

              {/* Rule Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Rule Type
                </label>
                <select
                  value={filters.rule_type || ""}
                  onChange={(e) =>
                    handleFilterChange("rule_type", e.target.value || undefined)
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Rule Types</option>
                  {RULE_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Reward Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reward Type
                </label>
                <select
                  value={filters.reward_type || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "reward_type",
                      e.target.value || undefined
                    )
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Reward Types</option>
                  {REWARD_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Customer Segment Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Segment
                </label>
                <select
                  value={filters.applicable_customer_segments || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "applicable_customer_segments",
                      e.target.value || undefined
                    )
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Segments</option>
                  {CUSTOMER_SEGMENTS.map((segment) => (
                    <option key={segment.value} value={segment.value}>
                      {segment.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Promotions List */}
      <div className="bg-white rounded-lg border border-gray-200">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-sm text-gray-600">Loading promotions...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <TrendingUp className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Promotions
            </h3>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => loadPromotions()}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Try Again
            </button>
          </div>
        ) : promotions.length === 0 ? (
          <div className="p-8 text-center">
            <Award className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Promotions Found
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              {searchTerm || Object.keys(filters).length > 0
                ? "Try adjusting your search or filters."
                : "Get started by creating your first promotion."}
            </p>
            {!searchTerm && Object.keys(filters).length === 0 && (
              <button
                onClick={handleCreatePromotion}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create First Promotion
              </button>
            )}
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Promotion
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rule & Trigger
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reward
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Period
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {promotions.map((promotion) => (
                    <tr key={promotion.rule_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {formatPromotionName(promotion)}
                          </div>
                          {promotion.description && (
                            <div className="text-sm text-gray-500 truncate max-w-xs">
                              {promotion.description}
                            </div>
                          )}
                          <div className="text-xs text-gray-400 mt-1">
                            Priority: {promotion.priority}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRuleTypeColor(
                              promotion.rule_type
                            )}`}
                          >
                            {getRuleTypeLabel(promotion.rule_type)}
                          </span>
                          <div className="text-sm text-gray-600">
                            {formatTriggerValue(
                              promotion.rule_type,
                              promotion.trigger_value
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRewardTypeColor(
                              promotion.reward_type
                            )}`}
                          >
                            {getRewardTypeLabel(promotion.reward_type)}
                          </span>
                          <div className="text-sm text-gray-600">
                            {formatRewardValue(
                              promotion.reward_type,
                              promotion.reward_value
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPromotionStatusColor(
                              promotion.is_active
                            )}`}
                          >
                            {getPromotionStatusLabel(promotion.is_active)}
                          </span>
                          {isPromotionExpired(promotion) && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                              Expired
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatPromotionPeriod(promotion)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          <button
                            onClick={() => handleViewPromotion(promotion)}
                            className="text-gray-400 hover:text-gray-600"
                            title="View Details"
                          >
                            <Eye className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditPromotion(promotion)}
                            className="text-primary hover:text-primary-dark"
                            title="Edit"
                          >
                            <Edit className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() =>
                              handleDuplicatePromotion(
                                promotion.rule_id,
                                promotion.name
                              )
                            }
                            className="text-blue-600 hover:text-blue-800"
                            title="Duplicate"
                          >
                            <Copy className="h-4 w-4" />
                          </button>
                          {promotion.is_active ? (
                            <button
                              onClick={() =>
                                handleDeactivatePromotion(promotion.rule_id)
                              }
                              className="text-red-600 hover:text-red-800"
                              title="Deactivate"
                            >
                              <PowerOff className="h-4 w-4" />
                            </button>
                          ) : (
                            <button
                              onClick={() =>
                                handleActivatePromotion(promotion.rule_id)
                              }
                              className="text-green-600 hover:text-green-800"
                              title="Activate"
                            >
                              <Power className="h-4 w-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden">
              <div className="divide-y divide-gray-200">
                {promotions.map((promotion) => (
                  <div key={promotion.rule_id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {formatPromotionName(promotion)}
                        </div>
                        {promotion.description && (
                          <div className="text-sm text-gray-500 mt-1">
                            {promotion.description}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center space-x-2 ml-4">
                        <button
                          onClick={() => handleViewPromotion(promotion)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <Eye className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleEditPromotion(promotion)}
                          className="text-primary hover:text-primary-dark"
                        >
                          <Edit className="h-5 w-5" />
                        </button>
                      </div>
                    </div>

                    <div className="mt-3 flex flex-wrap gap-2">
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRuleTypeColor(
                          promotion.rule_type
                        )}`}
                      >
                        {getRuleTypeLabel(promotion.rule_type)}
                      </span>
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getRewardTypeColor(
                          promotion.reward_type
                        )}`}
                      >
                        {getRewardTypeLabel(promotion.reward_type)}
                      </span>
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getPromotionStatusColor(
                          promotion.is_active
                        )}`}
                      >
                        {getPromotionStatusLabel(promotion.is_active)}
                      </span>
                      {isPromotionExpired(promotion) && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Expired
                        </span>
                      )}
                    </div>

                    <div className="mt-2 text-xs text-gray-500">
                      <div>
                        Trigger:{" "}
                        {formatTriggerValue(
                          promotion.rule_type,
                          promotion.trigger_value
                        )}
                      </div>
                      <div>
                        Reward:{" "}
                        {formatRewardValue(
                          promotion.reward_type,
                          promotion.reward_value
                        )}
                      </div>
                      <div>Period: {formatPromotionPeriod(promotion)}</div>
                    </div>

                    <div className="mt-3 flex items-center space-x-3">
                      <button
                        onClick={() =>
                          handleDuplicatePromotion(
                            promotion.rule_id,
                            promotion.name
                          )
                        }
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Duplicate
                      </button>
                      {promotion.is_active ? (
                        <button
                          onClick={() =>
                            handleDeactivatePromotion(promotion.rule_id)
                          }
                          className="text-xs text-red-600 hover:text-red-800"
                        >
                          Deactivate
                        </button>
                      ) : (
                        <button
                          onClick={() =>
                            handleActivatePromotion(promotion.rule_id)
                          }
                          className="text-xs text-green-600 hover:text-green-800"
                        >
                          Activate
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && (
        <PromotionFormModal
          onSuccess={handlePromotionSaved}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedPromotion && (
        <PromotionFormModal
          promotion={selectedPromotion}
          onSuccess={handlePromotionSaved}
          onCancel={() => setShowEditModal(false)}
        />
      )}

      {showDetailsModal && selectedPromotion && (
        <PromotionDetailsModal
          promotion={selectedPromotion}
          onClose={() => setShowDetailsModal(false)}
          onEdit={() => {
            setShowDetailsModal(false);
            handleEditPromotion(selectedPromotion);
          }}
          onActivate={() => handleActivatePromotion(selectedPromotion.rule_id)}
          onDeactivate={() =>
            handleDeactivatePromotion(selectedPromotion.rule_id)
          }
          onDuplicate={() =>
            handleDuplicatePromotion(
              selectedPromotion.rule_id,
              selectedPromotion.name
            )
          }
        />
      )}
    </div>
  );
};

export default PromotionsContent;
