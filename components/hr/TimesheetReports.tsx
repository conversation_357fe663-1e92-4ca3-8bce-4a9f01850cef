"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  downloadTimesheetReport,
  PeriodType,
  ReportFormat,
  formatDateForTimesheetAPI,
  validateTimesheetParams,
} from "@/lib/timesheet-reports";
import { getEmployees } from "@/lib/employee";
import DashboardCard from "@/components/ui/DashboardCard";
import DatePicker from "react-datepicker";
import {
  FileText,
  Download,
  Calendar,
  AlertCircle,
  Users,
  Building2,
  Clock,
  FileSpreadsheet,
  Search,
  CheckSquare,
  Square,
  X
} from "lucide-react";
import toast from "react-hot-toast";
import "react-datepicker/dist/react-datepicker.css";

// Employee interface (simplified for timesheet reports)
interface Employee {
  employee_id: string;
  full_name: string;
}

// Department interface
interface Department {
  department_id: string;
  name: string;
  description: string;
}

const TimesheetReports: React.FC = () => {
  const { companies } = useAuth();
  
  // Form state
  const [periodType, setPeriodType] = useState<PeriodType>("monthly");
  const [reportFormat, setReportFormat] = useState<ReportFormat>("pdf");
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
  const [selectedDepartment, setSelectedDepartment] = useState<string>("");

  // Employee search state
  const [employeeSearchQuery, setEmployeeSearchQuery] = useState<string>("");

  // Data state
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(false);
  const [isLoadingDepartments, setIsLoadingDepartments] = useState(false);
  const [isDownloading, setIsDownloading] = useState(false);

  // Filtered employees based on search query
  const filteredEmployees = useMemo(() => {
    if (!employeeSearchQuery.trim()) {
      return employees;
    }

    const query = employeeSearchQuery.toLowerCase().trim();
    return employees.filter(employee =>
      employee.full_name.toLowerCase().includes(query)
    );
  }, [employees, employeeSearchQuery]);

  // Check if all filtered employees are selected
  const areAllFilteredEmployeesSelected = useMemo(() => {
    if (filteredEmployees.length === 0) return false;
    return filteredEmployees.every(employee =>
      selectedEmployees.includes(employee.employee_id)
    );
  }, [filteredEmployees, selectedEmployees]);

  // Fetch all employees (with pagination support)
  const fetchEmployees = async () => {
    if (!companies || companies.length === 0) return;

    try {
      setIsLoadingEmployees(true);
      const companyId = companies[0].company_id;

      // Use the existing getEmployees function that handles pagination
      const allEmployees = await getEmployees(companyId);
      setEmployees(allEmployees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      toast.error("Failed to load employees");
    } finally {
      setIsLoadingEmployees(false);
    }
  };

  // Fetch departments
  const fetchDepartments = async () => {
    if (!companies || companies.length === 0) return;
    
    try {
      setIsLoadingDepartments(true);
      const companyId = companies[0].company_id;
      const { apiGet } = await import("@/lib/api");
      const { getAccessToken } = await import("@/lib/auth");
      const token = getAccessToken();

      if (!token) return;

      const response = await apiGet<{
        departments: Department[];
        success: boolean;
      }>(`api/departments?company_id=${companyId}`, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (response.departments) {
        setDepartments(response.departments);
      }
    } catch (error) {
      console.error("Error fetching departments:", error);
    } finally {
      setIsLoadingDepartments(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchEmployees();
    fetchDepartments();
  }, [companies]);

  // Handle timesheet download
  const handleDownload = async () => {
    if (!companies || companies.length === 0) {
      toast.error("No company selected");
      return;
    }

    try {
      setIsDownloading(true);
      
      const companyId = companies[0].company_id;
      const params = {
        period_type: periodType,
        format: reportFormat,
        company_id: companyId,
        ...(selectedEmployees.length > 0 && { employee_ids: selectedEmployees }),
        ...(selectedDepartment && { department_id: selectedDepartment }),
        ...(periodType === "monthly" && { month: selectedMonth, year: selectedYear }),
        ...(selectedDate && { date: formatDateForTimesheetAPI(selectedDate) }),
      };

      // Validate parameters
      const validation = validateTimesheetParams(params);
      if (!validation.isValid) {
        toast.error(`Invalid parameters: ${validation.errors.join(", ")}`);
        return;
      }

      await downloadTimesheetReport(params);
      toast.success("Timesheet report downloaded successfully!");
    } catch (error: any) {
      console.error("Error downloading timesheet report:", error);
      toast.error(error.message || "Failed to download timesheet report");
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle employee selection
  const handleEmployeeToggle = (employeeId: string) => {
    setSelectedEmployees(prev =>
      prev.includes(employeeId)
        ? prev.filter(id => id !== employeeId)
        : [...prev, employeeId]
    );
  };

  // Select all filtered employees
  const handleSelectAllFiltered = () => {
    const filteredEmployeeIds = filteredEmployees.map(emp => emp.employee_id);
    setSelectedEmployees(prev => {
      const newSelection = new Set([...prev, ...filteredEmployeeIds]);
      return Array.from(newSelection);
    });
  };

  // Deselect all filtered employees
  const handleDeselectAllFiltered = () => {
    const filteredEmployeeIds = new Set(filteredEmployees.map(emp => emp.employee_id));
    setSelectedEmployees(prev =>
      prev.filter(id => !filteredEmployeeIds.has(id))
    );
  };

  // Clear all selections
  const clearSelections = () => {
    setSelectedEmployees([]);
    setSelectedDepartment("");
    setSelectedDate(null);
    setEmployeeSearchQuery("");
  };

  // Clear employee search
  const clearEmployeeSearch = () => {
    setEmployeeSearchQuery("");
  };

  return (
    <DashboardCard title="Timesheet Reports">
      <div className="space-y-6">
        {/* Description */}
        <div className="text-sm text-gray-600">
          <p>
            Generate comprehensive timesheet matrix reports for different periods and formats.
          </p>
          <p className="mt-1">
            <strong>Note:</strong> You can filter by employees, departments, or specific dates.
          </p>
        </div>

        {/* Report Configuration */}
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          {/* Period Type and Format Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Clock className="h-4 w-4 inline mr-1" />
                Period Type
              </label>
              <select
                value={periodType}
                onChange={(e) => setPeriodType(e.target.value as PeriodType)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="monthly">Monthly</option>
                <option value="weekly">Weekly</option>
                <option value="bi-weekly">Bi-Weekly</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <FileText className="h-4 w-4 inline mr-1" />
                Format
              </label>
              <select
                value={reportFormat}
                onChange={(e) => setReportFormat(e.target.value as ReportFormat)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              >
                <option value="pdf">PDF</option>
                <option value="excel">Excel</option>
              </select>
            </div>
          </div>

          {/* Month/Year Selection for Monthly Reports */}
          {periodType === "monthly" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Month
                </label>
                <select
                  value={selectedMonth}
                  onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {new Date(2024, i).toLocaleString('default', { month: 'long' })}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Year
                </label>
                <select
                  value={selectedYear}
                  onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {Array.from({ length: 5 }, (_, i) => {
                    const year = new Date().getFullYear() - 2 + i;
                    return (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    );
                  })}
                </select>
              </div>
            </div>
          )}

          {/* Specific Date Selection for Weekly/Bi-weekly */}
          {(periodType === "weekly" || periodType === "bi-weekly") && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="h-4 w-4 inline mr-1" />
                Specific Date (Optional)
              </label>
              <DatePicker
                selected={selectedDate}
                onChange={(date) => setSelectedDate(date)}
                dateFormat="yyyy-MM-dd"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholderText="Select a date for the period"
                isClearable
              />
            </div>
          )}
        </div>

        {/* Filtering Options */}
        <div className="bg-gray-50 p-4 rounded-lg space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900">
              Filtering Options (Optional)
            </h3>
            {(selectedEmployees.length > 0 || selectedDepartment || employeeSearchQuery) && (
              <button
                onClick={clearSelections}
                className="text-sm text-red-600 hover:text-red-800 flex items-center"
              >
                <X className="h-3 w-3 mr-1" />
                Clear All Filters
              </button>
            )}
          </div>

          {/* Department Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Building2 className="h-4 w-4 inline mr-1" />
              Department Filter
            </label>
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={isLoadingDepartments}
            >
              <option value="">All Departments</option>
              {departments.map((dept) => (
                <option key={dept.department_id} value={dept.department_id}>
                  {dept.name}
                </option>
              ))}
            </select>
          </div>

          {/* Employee Selection */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="text-sm font-medium text-gray-700">
                <Users className="h-4 w-4 inline mr-1" />
                Employee Filter ({selectedEmployees.length} selected)
              </label>
              {selectedEmployees.length > 0 && (
                <button
                  onClick={() => setSelectedEmployees([])}
                  className="text-xs text-red-600 hover:text-red-800 flex items-center"
                >
                  <X className="h-3 w-3 mr-1" />
                  Clear Selection
                </button>
              )}
            </div>

            {/* Employee Search */}
            <div className="relative mb-3">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search employees by name..."
                value={employeeSearchQuery}
                onChange={(e) => setEmployeeSearchQuery(e.target.value)}
                className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                aria-label="Search employees"
              />
              {employeeSearchQuery && (
                <button
                  onClick={clearEmployeeSearch}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  aria-label="Clear search"
                >
                  <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </button>
              )}
            </div>

            {/* Employee Count and Bulk Actions */}
            {!isLoadingEmployees && employees.length > 0 && (
              <div className="flex items-center justify-between mb-2 text-xs text-gray-600">
                <span>
                  {employeeSearchQuery ? (
                    <>Showing {filteredEmployees.length} of {employees.length} employees</>
                  ) : (
                    <>{employees.length} employees total</>
                  )}
                </span>
                {filteredEmployees.length > 0 && (
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={handleSelectAllFiltered}
                      disabled={areAllFilteredEmployeesSelected}
                      className="text-primary hover:text-primary-dark disabled:text-gray-400 disabled:cursor-not-allowed flex items-center"
                    >
                      <CheckSquare className="h-3 w-3 mr-1" />
                      Select All
                    </button>
                    <span className="text-gray-300">|</span>
                    <button
                      onClick={handleDeselectAllFiltered}
                      disabled={!filteredEmployees.some(emp => selectedEmployees.includes(emp.employee_id))}
                      className="text-primary hover:text-primary-dark disabled:text-gray-400 disabled:cursor-not-allowed flex items-center"
                    >
                      <Square className="h-3 w-3 mr-1" />
                      Deselect All
                    </button>
                  </div>
                )}
              </div>
            )}

            {/* Employee List */}
            <div className="max-h-48 overflow-y-auto border border-gray-300 rounded-md bg-white">
              {isLoadingEmployees ? (
                <div className="p-4 space-y-2">
                  {/* Loading skeleton */}
                  {[...Array(5)].map((_, index) => (
                    <div key={index} className="flex items-center p-2 animate-pulse">
                      <div className="w-4 h-4 bg-gray-200 rounded mr-2"></div>
                      <div className="h-4 bg-gray-200 rounded flex-1"></div>
                    </div>
                  ))}
                </div>
              ) : employees.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p>No employees found</p>
                </div>
              ) : filteredEmployees.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                  <p>No employees match your search</p>
                  <button
                    onClick={clearEmployeeSearch}
                    className="mt-2 text-primary hover:text-primary-dark text-sm"
                  >
                    Clear search
                  </button>
                </div>
              ) : (
                <div className="p-2 space-y-1">
                  {filteredEmployees.map((employee) => (
                    <label
                      key={employee.employee_id}
                      className="flex items-center p-2 hover:bg-gray-50 rounded cursor-pointer transition-colors"
                    >
                      <input
                        type="checkbox"
                        checked={selectedEmployees.includes(employee.employee_id)}
                        onChange={() => handleEmployeeToggle(employee.employee_id)}
                        className="mr-3 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                        aria-label={`Select ${employee.full_name}`}
                      />
                      <span className="text-sm text-gray-700 flex-1">
                        {employee.full_name}
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Download Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            onClick={handleDownload}
            disabled={isDownloading}
            className="flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isDownloading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating Report...
              </>
            ) : (
              <>
                {reportFormat === "pdf" ? (
                  <FileText className="h-5 w-5 mr-2" />
                ) : (
                  <FileSpreadsheet className="h-5 w-5 mr-2" />
                )}
                Download {reportFormat.toUpperCase()} Report
              </>
            )}
          </button>
        </div>

        {/* Information Panel */}
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">Timesheet Report Information:</p>
              <ul className="list-disc list-inside space-y-1 text-xs leading-relaxed">
                <li>
                  <strong>Monthly:</strong> Generates timesheet matrix for the selected month and year
                </li>
                <li>
                  <strong>Weekly:</strong> Generates timesheet for the current week or week containing the selected date
                </li>
                <li>
                  <strong>Bi-Weekly:</strong> Generates timesheet for the current bi-weekly period or period containing the selected date
                </li>
                <li>
                  <strong>Employee Filter:</strong> When selected, report includes only the chosen employees
                </li>
                <li>
                  <strong>Department Filter:</strong> When selected, report includes only employees from the chosen department
                </li>
                <li>
                  PDF format provides a formatted timesheet suitable for printing and review
                </li>
                <li>
                  Excel format allows for further data analysis and manipulation
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default TimesheetReports;
