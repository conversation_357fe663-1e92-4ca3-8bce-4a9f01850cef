"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Download,
  Award,
  TrendingUp,
  Users,
  Gift,
  ChevronDown,
  Eye,
  RotateCcw,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getLoyaltyBalances,
  getCurrentMonthLoyaltyAnalytics,
} from "@/lib/loyalty";
import {
  LoyaltyBalance,
  LoyaltyAnalytics,
  LoyaltyBalancesFilters,
  formatRewardValue,
  getActiveStatusColor,
  REWARD_TYPE_LABELS,
  calculateRewardsByType,
} from "@/types/loyalty";
import LoyaltyBalanceDetailsModal from "@/components/loyalty/LoyaltyBalanceDetailsModal";

interface LoyaltyContentProps {
  className?: string;
}

const LoyaltyContent: React.FC<LoyaltyContentProps> = ({ className = "" }) => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // State management
  const [balances, setBalances] = useState<LoyaltyBalance[]>([]);
  const [analytics, setAnalytics] = useState<LoyaltyAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBalance, setSelectedBalance] = useState<LoyaltyBalance | null>(
    null
  );
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Filters
  const [filters, setFilters] = useState<LoyaltyBalancesFilters>({
    page: 1,
    per_page: 20,
  });
  const [showFilters, setShowFilters] = useState(false);

  // Load data
  useEffect(() => {
    loadData();
  }, [filters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load balances and analytics in parallel
      const [balancesResponse, analyticsResponse] = await Promise.all([
        getLoyaltyBalances(filters),
        getCurrentMonthLoyaltyAnalytics(),
      ]);

      setBalances(balancesResponse.extend.balances);

      if (analyticsResponse.data) {
        setAnalytics(analyticsResponse.data);
      }
    } catch (err) {
      console.error("Error loading loyalty data:", err);
      setError("Failed to load loyalty data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Filter balances by search term
  const filteredBalances = balances.filter(
    (balance) =>
      balance.customer_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      balance.rule.reward_type
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      balance.rule.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle filter changes
  const handleFilterChange = (
    key: keyof LoyaltyBalancesFilters,
    value: any
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };

  // Handle balance details
  const handleViewBalance = (balance: LoyaltyBalance) => {
    setSelectedBalance(balance);
    setShowDetailsModal(true);
  };

  // Calculate summary statistics
  const totalActiveBalances = balances.filter((b) => b.is_active).length;
  const totalInactiveBalances = balances.filter((b) => !b.is_active).length;
  const rewardsByType = calculateRewardsByType(balances);

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
            <div className="space-y-3">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="text-red-600 mr-3">⚠️</div>
            <div>
              <h3 className="text-red-800 font-medium">Error Loading Data</h3>
              <p className="text-red-600 text-sm mt-1">{error}</p>
              <button
                onClick={loadData}
                className="mt-2 text-red-600 hover:text-red-800 text-sm font-medium flex items-center"
              >
                <RotateCcw className="w-4 h-4 mr-1" />
                Try Again
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbs} />

      <div className="p-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Loyalty Management
            </h1>
            <p className="text-gray-600">
              Manage customer loyalty balances and rewards
            </p>
          </div>
          <div className="flex items-center space-x-3 mt-4 sm:mt-0">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
              <ChevronDown
                className={`w-4 h-4 ml-2 transform transition-transform ${
                  showFilters ? "rotate-180" : ""
                }`}
              />
            </button>
            <button className="flex items-center px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark">
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
          </div>
        </div>

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Customers with Rewards
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics.customers_with_rewards}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 rounded-lg">
                  <Users className="w-6 h-6 text-blue-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Rewards Earned
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics.total_rewards_earned}
                  </p>
                </div>
                <div className="p-3 bg-green-100 rounded-lg">
                  <Award className="w-6 h-6 text-green-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Total Redeemed
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics.total_rewards_redeemed}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 rounded-lg">
                  <Gift className="w-6 h-6 text-purple-600" />
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">
                    Available Rewards
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {analytics.total_rewards_available}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 rounded-lg">
                  <TrendingUp className="w-6 h-6 text-orange-600" />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Reward Type
                </label>
                <select
                  value={filters.reward_type || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "reward_type",
                      e.target.value || undefined
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">All Types</option>
                  {Object.entries(REWARD_TYPE_LABELS).map(([value, label]) => (
                    <option key={value} value={value}>
                      {label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Status
                </label>
                <select
                  value={
                    filters.is_active === undefined
                      ? ""
                      : filters.is_active
                      ? "active"
                      : "inactive"
                  }
                  onChange={(e) =>
                    handleFilterChange(
                      "is_active",
                      e.target.value === ""
                        ? undefined
                        : e.target.value === "active"
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">All Statuses</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Customer ID
                </label>
                <input
                  type="text"
                  value={filters.customer_id || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "customer_id",
                      e.target.value || undefined
                    )
                  }
                  placeholder="Enter customer ID"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => setFilters({ page: 1, per_page: 20 })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Search */}
        <div className="bg-white p-6 rounded-lg shadow-sm border mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search by customer ID, reward type, or rule name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>
        </div>

        {/* Loyalty Balances Table */}
        <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Loyalty Balances
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              {filteredBalances.length} balance
              {filteredBalances.length !== 1 ? "s" : ""} found
            </p>
          </div>

          {filteredBalances.length === 0 ? (
            <div className="p-8 text-center">
              <Award className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No loyalty balances found
              </h3>
              <p className="text-gray-600">
                {searchTerm ||
                Object.keys(filters).some(
                  (key) => filters[key as keyof LoyaltyBalancesFilters]
                )
                  ? "Try adjusting your search or filters."
                  : "No loyalty balances have been created yet."}
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rule Name
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Reward Type
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Available
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Earned
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Redeemed
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Progress
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredBalances.map((balance) => (
                    <tr key={balance.balance_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {balance.customer_id.substring(0, 8)}...
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {balance.rule.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {REWARD_TYPE_LABELS[
                            balance.rule
                              .reward_type as keyof typeof REWARD_TYPE_LABELS
                          ] || balance.rule.reward_type}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-green-600">
                          {balance.rewards_available}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {balance.rewards_earned}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {balance.rewards_redeemed}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="text-sm text-gray-900 mr-2">
                            {balance.progress_percentage.toFixed(0)}%
                          </div>
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{
                                width: `${Math.min(
                                  balance.progress_percentage,
                                  100
                                )}%`,
                              }}
                            ></div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getActiveStatusColor(
                            balance.is_active
                          )}`}
                        >
                          {balance.is_active ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => handleViewBalance(balance)}
                          className="text-primary hover:text-primary-dark flex items-center"
                        >
                          <Eye className="w-4 h-4 mr-1" />
                          View
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Loyalty Balance Details Modal */}
        {/* {showDetailsModal && selectedBalance && (
          <LoyaltyBalanceDetailsModal
            balance={selectedBalance}
            onClose={() => {
              setShowDetailsModal(false);
              setSelectedBalance(null);
            }}
          />
        )} */}
      </div>
    </div>
  );
};

export default LoyaltyContent;
