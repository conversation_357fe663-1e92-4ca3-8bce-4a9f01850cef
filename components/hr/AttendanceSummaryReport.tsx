"use client";

import React, { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import {
  downloadAttendanceSummaryPDF,
  downloadAttendanceSummaryExcel,
  formatDateForAPI,
  validateDateRange,
} from "@/lib/attendance-reports";
import DashboardCard from "@/components/ui/DashboardCard";
import DatePicker from "react-datepicker";
import { FileText, Download, Calendar, AlertCircle } from "lucide-react";
import toast from "react-hot-toast";
import "react-datepicker/dist/react-datepicker.css";

const AttendanceSummaryReport: React.FC = () => {
  const { companies } = useAuth();
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [isDownloading, setIsDownloading] = useState<{
    pdf: boolean;
    excel: boolean;
  }>({
    pdf: false,
    excel: false,
  });
  const [error, setError] = useState<string>("");

  // Get company ID
  const companyId =
    companies && companies.length > 0 ? companies[0].company_id : null;

  // Handle PDF download
  const handlePDFDownload = async () => {
    if (!companyId) {
      toast.error("No company found");
      return;
    }

    // Validate date range
    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
      setError(validation.error || "Invalid date range");
      return;
    }

    setError("");
    setIsDownloading((prev) => ({ ...prev, pdf: true }));

    try {
      await downloadAttendanceSummaryPDF({
        company_id: companyId,
        start_date: formatDateForAPI(startDate),
        end_date: formatDateForAPI(endDate),
      });

      toast.success("PDF report downloaded successfully");
    } catch (err: any) {
      console.error("Error downloading PDF:", err);
      toast.error(err.message || "Failed to download PDF report");
      setError(err.message || "Failed to download PDF report");
    } finally {
      setIsDownloading((prev) => ({ ...prev, pdf: false }));
    }
  };

  // Handle Excel download
  const handleExcelDownload = async () => {
    if (!companyId) {
      toast.error("No company found");
      return;
    }

    // Validate date range
    const validation = validateDateRange(startDate, endDate);
    if (!validation.isValid) {
      setError(validation.error || "Invalid date range");
      return;
    }

    setError("");
    setIsDownloading((prev) => ({ ...prev, excel: true }));

    try {
      await downloadAttendanceSummaryExcel({
        company_id: companyId,
        start_date: formatDateForAPI(startDate),
        end_date: formatDateForAPI(endDate),
      });

      toast.success("Excel report downloaded successfully");
    } catch (err: any) {
      console.error("Error downloading Excel:", err);
      toast.error(err.message || "Failed to download Excel report");
      setError(err.message || "Failed to download Excel report");
    } finally {
      setIsDownloading((prev) => ({ ...prev, excel: false }));
    }
  };

  // Clear date range
  const clearDateRange = () => {
    setStartDate(null);
    setEndDate(null);
    setError("");
  };

  return (
    <DashboardCard title="Attendance Summary Reports">
      <div className="space-y-6">
        {/* Description */}
        <div className="text-sm text-gray-600">
          <p>
            Generate comprehensive attendance summary reports for your
            organization.
          </p>
          <p className="mt-1">
            <strong>Note:</strong> If no date range is selected, the report will
            include data for the current month.
          </p>
        </div>

        {/* Date Range Selection */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="flex items-center mb-3">
            <Calendar className="h-5 w-5 text-gray-500 mr-2" />
            <h3 className="text-sm font-medium text-gray-900">
              Date Range (Optional)
            </h3>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Start Date
              </label>
              <DatePicker
                selected={startDate}
                onChange={(date) => {
                  setStartDate(date);
                  setError("");
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                dateFormat="yyyy-MM-dd"
                maxDate={endDate || new Date()}
                placeholderText="Select start date"
                isClearable
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                End Date
              </label>
              <DatePicker
                selected={endDate}
                onChange={(date) => {
                  setEndDate(date);
                  setError("");
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                dateFormat="yyyy-MM-dd"
                minDate={startDate || undefined}
                maxDate={new Date()}
                placeholderText="Select end date"
                isClearable
              />
            </div>
          </div>

          {(startDate || endDate) && (
            <div className="mt-3 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                {startDate && endDate ? (
                  <span>
                    Report period: {startDate.toLocaleDateString()} -{" "}
                    {endDate.toLocaleDateString()}
                  </span>
                ) : startDate ? (
                  <span>Report from: {startDate.toLocaleDateString()}</span>
                ) : endDate ? (
                  <span>Report until: {endDate.toLocaleDateString()}</span>
                ) : null}
              </div>
              <button
                onClick={clearDateRange}
                className="text-sm text-gray-500 hover:text-gray-700 underline"
              >
                Clear dates
              </button>
            </div>
          )}
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-start">
            <AlertCircle className="h-5 w-5 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-red-700">{error}</div>
          </div>
        )}

        {/* Download Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* PDF Download */}
          <button
            onClick={handlePDFDownload}
            disabled={isDownloading.pdf || !companyId}
            className="flex items-center justify-center px-6 py-3 
      bg-blue-600 text-white rounded-lg 
      hover:bg-blue-700 
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 
      disabled:opacity-50 disabled:cursor-not-allowed 
      transition-colors"
          >
            {isDownloading.pdf ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Generating PDF...
              </>
            ) : (
              <>
                <FileText className="h-5 w-5 mr-2" />
                Download PDF Report
              </>
            )}
          </button>

          {/* Excel Download */}
          <button
            onClick={handleExcelDownload}
            disabled={isDownloading.excel || !companyId}
            className="flex items-center justify-center px-6 py-3 
      bg-white text-blue-600 border border-blue-600 rounded-lg 
      hover:bg-blue-600 hover:text-white 
      focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 
      disabled:opacity-50 disabled:cursor-not-allowed 
      transition-colors"
          >
            {isDownloading.excel ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                Generating Excel...
              </>
            ) : (
              <>
                <Download className="h-5 w-5 mr-2" />
                Download Excel Report
              </>
            )}
          </button>
        </div>

        {/* Additional Information */}
        <div className="max-w-2xl mx-auto mt-4 bg-blue-50 border border-blue-200 rounded-md p-4 shadow-sm">
          <div className="flex items-start">
            <AlertCircle className="h-5 w-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-700">
              <p className="font-medium mb-1">Report Information:</p>
              <ul className="list-disc list-inside space-y-1 text-xs leading-relaxed">
                <li>
                  Reports include comprehensive attendance data for all
                  employees
                </li>
                <li>
                  PDF format provides a formatted summary suitable for printing
                </li>
                <li>
                  Excel format allows for further data analysis and manipulation
                </li>
                <li>
                  Default period is the current month when no dates are
                  specified
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </DashboardCard>
  );
};

export default AttendanceSummaryReport;
