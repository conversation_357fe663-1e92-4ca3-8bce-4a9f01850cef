"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  Package,
  RefreshCw,
  Archive,
  CheckCircle,
  XCircle,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
  Trash2,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import ConfirmationModal from "@/components/ui/ConfirmationModal";
import {
  useDeleteConfirmation,
  useActionConfirmation,
} from "@/hooks/useConfirmation";
import {
  getCustomerStorages,
  deleteCustomerStorage,
  retrieveCustomerStorage,
  abandonCustomerStorage,
} from "@/lib/customer-storage";
import { getCustomers } from "@/lib/customer";
import { getStorageLocations } from "@/lib/storage-location";
import {
  CustomerStorage,
  CustomerStorageFilters,
  StorageStatus,
  getStorageStatusLabel,
  getStorageStatusColor,
  getStorageDuration,
  formatStorageDuration,
  isStorageActive,
  isStorageOverdue,
  CUSTOMER_STORAGE_SORT_OPTIONS,
  CustomerStorageStatistics,
  calculateStorageStatistics,
} from "@/types/customer-storage";
import { Customer } from "@/types/customer";
import { StorageLocation } from "@/types/storage-location";
import CustomerStorageFormModal from "@/components/customer-storage/CustomerStorageFormModal";
import CustomerStorageDetailsModal from "@/components/customer-storage/CustomerStorageDetailsModal";
import { getCurrentDateTimeString } from "@/utils/dateUtils";

interface CustomerStorageContentProps {}

const CustomerStorageContent: React.FC<CustomerStorageContentProps> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // Confirmation hooks
  const deleteConfirmation = useDeleteConfirmation();
  const actionConfirmation = useActionConfirmation();

  // State management
  const [customerStorages, setCustomerStorages] = useState<CustomerStorage[]>(
    []
  );
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [storageLocations, setStorageLocations] = useState<StorageLocation[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<CustomerStorageFilters>({});
  const [sortBy, setSortBy] = useState<string>("stored_date_desc");
  const [statistics, setStatistics] = useState<CustomerStorageStatistics>({
    total: 0,
    active: 0,
    retrieved: 0,
    abandoned: 0,
    overdue: 0,
    by_location: {},
    by_customer: {},
  });

  // Modal states
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedCustomerStorage, setSelectedCustomerStorage] =
    useState<CustomerStorage | null>(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(10);

  // Load data
  useEffect(() => {
    loadData();
  }, [filters, searchTerm]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      const [storagesResponse, customersResponse, locationsResponse] =
        await Promise.all([
          getCustomerStorages({
            ...filters,
            search: searchTerm || undefined,
          }),
          getCustomers({ status: "active" }),
          getStorageLocations({}), // Get all locations
        ]);

      const storages = storagesResponse.extend.customer_storages;
      setCustomerStorages(storages);
      setCustomers(customersResponse.extend.customers);
      setStorageLocations(locationsResponse.extend.storage_locations);

      // Calculate statistics from the loaded data
      const stats = calculateStorageStatistics(storages);
      setStatistics(stats);
    } catch (err) {
      console.error("Error loading data:", err);
      setError("Failed to load customer storage records. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  // Handle filter change
  const handleFilterChange = (newFilters: Partial<CustomerStorageFilters>) => {
    setFilters({ ...filters, ...newFilters });
    setCurrentPage(1);
  };

  // Handle retrieve storage
  const handleRetrieveStorage = async (customerStorage: CustomerStorage) => {
    const confirmed = await actionConfirmation.confirmAction(
      "Mark as Retrieved",
      customerStorage.items_description,
      "info"
    );

    if (!confirmed) {
      actionConfirmation.hideConfirmation();
      return;
    }

    try {
      actionConfirmation.setLoading(true);
      await retrieveCustomerStorage(customerStorage.storage_id, {
        retrieved_at: getCurrentDateTimeString(),
      });
      await loadData();
      actionConfirmation.hideConfirmation();
    } catch (error) {
      console.error("Error retrieving storage:", error);
      actionConfirmation.hideConfirmation();
      // Show error in a separate modal or toast - for now we'll use console.error
      // TODO: Implement error toast notification
    }
  };

  // Handle abandon storage
  const handleAbandonStorage = async (customerStorage: CustomerStorage) => {
    const confirmed = await actionConfirmation.confirmAction(
      "Mark as Abandoned",
      customerStorage.items_description,
      "warning"
    );

    if (!confirmed) {
      actionConfirmation.hideConfirmation();
      return;
    }

    try {
      actionConfirmation.setLoading(true);
      await abandonCustomerStorage(customerStorage.storage_id, {
        retrieved_at: new Date().toISOString(),
      });
      await loadData();
      actionConfirmation.hideConfirmation();
    } catch (error) {
      console.error("Error abandoning storage:", error);
      actionConfirmation.hideConfirmation();
      // TODO: Implement error toast notification
    }
  };

  // Handle delete storage
  const handleDeleteStorage = async (customerStorage: CustomerStorage) => {
    const confirmed = await deleteConfirmation.confirmDelete(
      customerStorage.items_description,
      "storage record"
    );

    if (!confirmed) {
      deleteConfirmation.hideConfirmation();
      return;
    }

    try {
      deleteConfirmation.setLoading(true);
      await deleteCustomerStorage(customerStorage.storage_id);
      await loadData();
      deleteConfirmation.hideConfirmation();
    } catch (error) {
      console.error("Error deleting storage:", error);
      deleteConfirmation.hideConfirmation();
      // TODO: Implement error toast notification
    }
  };

  // Handle view storage
  const handleViewStorage = (customerStorage: CustomerStorage) => {
    setSelectedCustomerStorage(customerStorage);
    setShowDetailsModal(true);
  };

  // Handle edit storage
  const handleEditStorage = (customerStorage: CustomerStorage) => {
    setSelectedCustomerStorage(customerStorage);
    setShowEditModal(true);
  };

  // Handle form success
  const handleFormSuccess = async () => {
    await loadData();
    setShowCreateModal(false);
    setShowEditModal(false);
    setSelectedCustomerStorage(null);
  };

  // Sort customer storages
  const sortedCustomerStorages = [...customerStorages].sort((a, b) => {
    switch (sortBy) {
      case "customer_name_asc":
        return `${a.customer.first_name} ${a.customer.last_name}`.localeCompare(
          `${b.customer.first_name} ${b.customer.last_name}`
        );
      case "customer_name_desc":
        return `${b.customer.first_name} ${b.customer.last_name}`.localeCompare(
          `${a.customer.first_name} ${a.customer.last_name}`
        );
      case "stored_date_desc":
        return (
          new Date(b.stored_at).getTime() - new Date(a.stored_at).getTime()
        );
      case "stored_date_asc":
        return (
          new Date(a.stored_at).getTime() - new Date(b.stored_at).getTime()
        );
      case "retrieved_date_desc":
        if (!a.retrieved_at && !b.retrieved_at) return 0;
        if (!a.retrieved_at) return 1;
        if (!b.retrieved_at) return -1;
        return (
          new Date(b.retrieved_at).getTime() -
          new Date(a.retrieved_at).getTime()
        );
      case "location_name_asc":
        return a.location.location_number.localeCompare(
          b.location.location_number
        );
      case "status_active":
        const aActive = isStorageActive(a);
        const bActive = isStorageActive(b);
        if (aActive === bActive) return 0;
        return aActive ? -1 : 1;
      case "duration_desc":
        return getStorageDuration(b) - getStorageDuration(a);
      default:
        return 0;
    }
  });

  // Paginate customer storages
  const totalPages = Math.ceil(sortedCustomerStorages.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedCustomerStorages = sortedCustomerStorages.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-secondary-dark">
            Customer Storage Management
          </h1>
          <p className="mt-1 text-sm text-secondary">
            Manage customer belongings storage, retrieval, and abandonment
            tracking
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex gap-3">
          <button
            onClick={() => loadData()}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Items</p>
              <p className="text-2xl font-bold text-gray-900">
                {statistics.total}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Package className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">
                Active Storage
              </p>
              <p className="text-2xl font-bold text-blue-600">
                {statistics.active}
              </p>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Archive className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Retrieved</p>
              <p className="text-2xl font-bold text-green-600">
                {statistics.retrieved}
              </p>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Abandoned</p>
              <p className="text-2xl font-bold text-red-600">
                {statistics.abandoned}
              </p>
            </div>
            <div className="p-3 bg-red-100 rounded-lg">
              <XCircle className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-orange-600">
                {statistics.overdue}
              </p>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search customers, items, locations..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Customer Filter */}
          <select
            value={filters.customer_id || ""}
            onChange={(e) =>
              handleFilterChange({ customer_id: e.target.value || undefined })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Customers</option>
            {customers.map((customer) => (
              <option key={customer.customer_id} value={customer.customer_id}>
                {customer.first_name} {customer.last_name}
              </option>
            ))}
          </select>

          {/* Location Filter */}
          <select
            value={filters.location_id || ""}
            onChange={(e) =>
              handleFilterChange({ location_id: e.target.value || undefined })
            }
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Locations</option>
            {storageLocations.map((location) => (
              <option key={location.location_id} value={location.location_id}>
                {location.location_number}
              </option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={filters.status || ""}
            onChange={(e) => {
              const value = e.target.value as StorageStatus | "";
              handleFilterChange({
                status: value || undefined,
              });
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            <option value="">All Status</option>
            <option value="stored">Active Storage</option>
            <option value="retrieved">Retrieved</option>
            <option value="abandoned">Abandoned</option>
          </select>
        </div>

        {/* Sort */}
        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-700">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value)}
              className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              {CUSTOMER_STORAGE_SORT_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
          <div className="text-sm text-gray-500">
            Showing {paginatedCustomerStorages.length} of{" "}
            {sortedCustomerStorages.length} records
          </div>
        </div>
      </div>

      {/* Customer Storage Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        {loading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">
              Loading customer storage records...
            </p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-600 mb-2">
              <XCircle className="h-8 w-8 mx-auto" />
            </div>
            <p className="text-sm text-red-600">{error}</p>
            <button
              onClick={() => loadData()}
              className="mt-2 text-sm text-primary hover:text-primary-dark"
            >
              Try again
            </button>
          </div>
        ) : paginatedCustomerStorages.length === 0 ? (
          <div className="p-8 text-center">
            <Package className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">
              {searchTerm || Object.keys(filters).length > 0
                ? "No customer storage records match your search criteria."
                : "No customer storage records found. Create your first storage record to get started."}
            </p>
            {!searchTerm && Object.keys(filters).length === 0 && (
              <button
                onClick={() => setShowCreateModal(true)}
                className="mt-2 text-sm text-primary hover:text-primary-dark"
              >
                Store First Item
              </button>
            )}
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Item Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Location
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stored Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedCustomerStorages.map((customerStorage) => {
                    const duration = getStorageDuration(customerStorage);
                    const isOverdue = isStorageOverdue(customerStorage);

                    return (
                      <tr
                        key={customerStorage.storage_id}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {customerStorage.customer.first_name}{" "}
                            {customerStorage.customer.last_name}
                          </div>
                          <div className="text-sm text-gray-500">
                            {customerStorage.customer.email}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 max-w-xs truncate">
                            {customerStorage.items_description}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {customerStorage.location.location_number}
                          </div>
                          {customerStorage.location.notes && (
                            <div className="text-sm text-gray-500">
                              {customerStorage.location.notes}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {new Date(
                            customerStorage.stored_at
                          ).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div
                            className={`text-sm ${
                              isOverdue
                                ? "text-red-600 font-medium"
                                : "text-gray-900"
                            }`}
                          >
                            {formatStorageDuration(duration)}
                          </div>
                          {isOverdue && (
                            <div className="text-xs text-red-500 flex items-center mt-1">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Overdue
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span
                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStorageStatusColor(
                              customerStorage.status
                            )}`}
                          >
                            {getStorageStatusLabel(customerStorage.status)}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex items-center justify-end space-x-2">
                            <button
                              onClick={() => handleViewStorage(customerStorage)}
                              className="text-gray-600 hover:text-gray-900"
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            {isStorageActive(customerStorage) && (
                              <>
                                <button
                                  onClick={() =>
                                    handleRetrieveStorage(customerStorage)
                                  }
                                  className="text-green-600 hover:text-green-900"
                                  title="Mark as Retrieved"
                                >
                                  <CheckCircle className="h-4 w-4" />
                                </button>
                                <button
                                  onClick={() =>
                                    handleAbandonStorage(customerStorage)
                                  }
                                  className="text-orange-600 hover:text-orange-900"
                                  title="Mark as Abandoned"
                                >
                                  <XCircle className="h-4 w-4" />
                                </button>
                              </>
                            )}
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                <div className="flex-1 flex justify-between sm:hidden">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() =>
                      setCurrentPage(Math.min(totalPages, currentPage + 1))
                    }
                    disabled={currentPage === totalPages}
                    className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Next
                  </button>
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-gray-700">
                      Showing{" "}
                      <span className="font-medium">{startIndex + 1}</span> to{" "}
                      <span className="font-medium">
                        {Math.min(
                          startIndex + itemsPerPage,
                          sortedCustomerStorages.length
                        )}
                      </span>{" "}
                      of{" "}
                      <span className="font-medium">
                        {sortedCustomerStorages.length}
                      </span>{" "}
                      results
                    </p>
                  </div>
                  <div>
                    <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                      <button
                        onClick={() =>
                          setCurrentPage(Math.max(1, currentPage - 1))
                        }
                        disabled={currentPage === 1}
                        className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </button>
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                        (page) => (
                          <button
                            key={page}
                            onClick={() => setCurrentPage(page)}
                            className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                              page === currentPage
                                ? "z-10 bg-primary border-primary text-white"
                                : "bg-white border-gray-300 text-gray-500 hover:bg-gray-50"
                            }`}
                          >
                            {page}
                          </button>
                        )
                      )}
                      <button
                        onClick={() =>
                          setCurrentPage(Math.min(totalPages, currentPage + 1))
                        }
                        disabled={currentPage === totalPages}
                        className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <ChevronRight className="h-5 w-5" />
                      </button>
                    </nav>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Modals */}
      {showCreateModal && (
        <CustomerStorageFormModal
          customers={customers}
          storageLocations={storageLocations}
          onSuccess={handleFormSuccess}
          onCancel={() => setShowCreateModal(false)}
        />
      )}

      {showEditModal && selectedCustomerStorage && (
        <CustomerStorageFormModal
          customerStorage={selectedCustomerStorage}
          customers={customers}
          storageLocations={storageLocations}
          onSuccess={handleFormSuccess}
          onCancel={() => {
            setShowEditModal(false);
            setSelectedCustomerStorage(null);
          }}
        />
      )}

      {showDetailsModal && selectedCustomerStorage && (
        <CustomerStorageDetailsModal
          customerStorage={selectedCustomerStorage}
          onClose={() => {
            setShowDetailsModal(false);
            setSelectedCustomerStorage(null);
          }}
        />
      )}

      {/* Confirmation Modals */}
      <ConfirmationModal
        isOpen={deleteConfirmation.confirmationState.isOpen}
        onClose={deleteConfirmation.confirmationState.onCancel}
        onConfirm={deleteConfirmation.confirmationState.onConfirm}
        title={deleteConfirmation.confirmationState.title}
        message={deleteConfirmation.confirmationState.message}
        confirmText={deleteConfirmation.confirmationState.confirmText}
        cancelText={deleteConfirmation.confirmationState.cancelText}
        type={deleteConfirmation.confirmationState.type}
        isLoading={deleteConfirmation.confirmationState.isLoading}
      />

      <ConfirmationModal
        isOpen={actionConfirmation.confirmationState.isOpen}
        onClose={actionConfirmation.confirmationState.onCancel}
        onConfirm={actionConfirmation.confirmationState.onConfirm}
        title={actionConfirmation.confirmationState.title}
        message={actionConfirmation.confirmationState.message}
        confirmText={actionConfirmation.confirmationState.confirmText}
        cancelText={actionConfirmation.confirmationState.cancelText}
        type={actionConfirmation.confirmationState.type}
        isLoading={actionConfirmation.confirmationState.isLoading}
      />
    </div>
  );
};

export default CustomerStorageContent;
