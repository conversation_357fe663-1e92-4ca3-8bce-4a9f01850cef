'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import DashboardCard from '@/components/ui/DashboardCard';
import DepartmentModal from '@/components/department/DepartmentModal';
import OrgUnitModal from '@/components/org-unit/OrgUnitModal';
import OrgUnitTree from '@/components/org-unit/OrgUnitTree';
import ConfirmationModal from '@/components/ui/ConfirmationModal';
import Link from 'next/link';
import { Building2, Users, ChevronDown, ChevronRight } from 'lucide-react';
import {
  OrgUnit,
  DepartmentStructure,
  OrgUnitModalState,
  ParentUnitOption,
  DepartmentOption,
} from '@/types/org-unit';
import { getDepartmentStructure, deleteOrgUnit } from '@/lib/org-units';

interface Department {
  department_id: string;
  name: string;
  description: string;
  manager_id: string | null;
  created_at: string;
  updated_at: string;
}

interface DepartmentResponse {
  departments: Department[];
  pagination: {
    has_next: boolean;
    has_prev: boolean;
    page: number;
    pages: number;
    per_page: number;
    total_count: number;
  };
  success: boolean;
}

const DepartmentsContent: React.FC = () => {
  const { companies } = useAuth();
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);

  // Organizational Units state
  const [expandedDepartments, setExpandedDepartments] = useState<Set<string>>(new Set());
  const [departmentStructures, setDepartmentStructures] = useState<Map<string, DepartmentStructure>>(new Map());
  const [loadingStructures, setLoadingStructures] = useState<Set<string>>(new Set());

  // Org Unit Modal state
  const [orgUnitModalState, setOrgUnitModalState] = useState<OrgUnitModalState>({
    isOpen: false,
    mode: 'create',
    unit: null,
    parentUnit: null,
    department: null,
  });

  // Org Unit Delete confirmation
  const [orgUnitDeleteConfirmation, setOrgUnitDeleteConfirmation] = useState<{
    isOpen: boolean;
    unit: OrgUnit | null;
  }>({
    isOpen: false,
    unit: null,
  });

  const [isDeletingOrgUnit, setIsDeletingOrgUnit] = useState(false);

  // Open create modal
  const openCreateModal = () => setIsCreateModalOpen(true);
  const closeCreateModal = () => setIsCreateModalOpen(false);

  // Open edit modal
  const openEditModal = (department: Department) => {
    setSelectedDepartment(department);
    setIsEditModalOpen(true);
  };
  const closeEditModal = () => {
    setSelectedDepartment(null);
    setIsEditModalOpen(false);
  };

  // Open delete confirmation
  const openDeleteConfirm = (department: Department) => {
    setDepartmentToDelete(department);
    setIsDeleteConfirmOpen(true);
  };
  const closeDeleteConfirm = () => {
    setDepartmentToDelete(null);
    setIsDeleteConfirmOpen(false);
  };

  // Fetch departments when component mounts or companies change
  useEffect(() => {
    fetchDepartments();
  }, [companies]);

  // Function to fetch departments from API
  const fetchDepartments = async () => {
    try {
      setIsLoading(true);
      setError('');

      const { apiGet } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      const response = await apiGet<DepartmentResponse>(`api/departments?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.departments) {
        setDepartments(response.departments);
      } else {
        // If API call succeeds but doesn't return the expected data
        console.warn('API returned unexpected data format');
        setDepartments([]);
      }
    } catch (error: any) {
      console.error('Error fetching departments:', error);
      setError(error.message || 'Failed to fetch departments');
      setDepartments([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to delete a department
  const handleDeleteDepartment = async () => {
    if (!departmentToDelete) return;

    try {
      setIsLoading(true);

      const { apiDelete } = await import('@/lib/api');
      const { getAccessToken } = await import('@/lib/auth');
      const token = getAccessToken();

      if (!token) {
        throw new Error('Authentication required');
      }

      const companyId = companies && companies.length > 0 ? companies[0].company_id : null;

      if (!companyId) {
        throw new Error('No company found');
      }

      await apiDelete(`api/departments/${departmentToDelete.department_id}?company_id=${companyId}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // Remove the deleted department from the state
      setDepartments(departments.filter(dept => dept.department_id !== departmentToDelete.department_id));

      // Close the confirmation dialog
      closeDeleteConfirm();
    } catch (error: any) {
      console.error('Error deleting department:', error);
      setError(error.message || 'Failed to delete department');
    } finally {
      setIsLoading(false);
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Organizational Units Functions
  const toggleDepartmentExpansion = async (department: Department) => {
    const isExpanded = expandedDepartments.has(department.department_id);

    if (isExpanded) {
      // Collapse
      const newExpanded = new Set(expandedDepartments);
      newExpanded.delete(department.department_id);
      setExpandedDepartments(newExpanded);
    } else {
      // Expand and fetch structure if not already loaded
      const newExpanded = new Set(expandedDepartments);
      newExpanded.add(department.department_id);
      setExpandedDepartments(newExpanded);

      if (!departmentStructures.has(department.department_id)) {
        await fetchDepartmentStructure(department.department_id);
      }
    }
  };

  const fetchDepartmentStructure = async (departmentId: string) => {
    try {
      const newLoading = new Set(loadingStructures);
      newLoading.add(departmentId);
      setLoadingStructures(newLoading);

      const structure = await getDepartmentStructure(departmentId);

      const newStructures = new Map(departmentStructures);
      newStructures.set(departmentId, structure);
      setDepartmentStructures(newStructures);
    } catch (error: any) {
      console.error('Error fetching department structure:', error);
      setError(error.message || 'Failed to fetch organizational structure');
    } finally {
      const newLoading = new Set(loadingStructures);
      newLoading.delete(departmentId);
      setLoadingStructures(newLoading);
    }
  };

  const handleAddOrgUnit = (departmentId: string, parentUnit?: OrgUnit) => {
    const department = departments.find(d => d.department_id === departmentId);

    setOrgUnitModalState({
      isOpen: true,
      mode: 'create',
      unit: null,
      parentUnit: parentUnit ? {
        org_unit_id: parentUnit.org_unit_id,
        name: parentUnit.name,
        level: parentUnit.level,
        full_path: `${department?.name} > ${parentUnit.name}`,
      } : null,
      department: department ? {
        department_id: department.department_id,
        name: department.name,
      } : null,
    });
  };

  const handleEditOrgUnit = (unit: OrgUnit) => {
    const department = departments.find(d => d.department_id === unit.department_id);

    setOrgUnitModalState({
      isOpen: true,
      mode: 'edit',
      unit,
      parentUnit: null,
      department: department ? {
        department_id: department.department_id,
        name: department.name,
      } : null,
    });
  };

  const handleDeleteOrgUnit = (unit: OrgUnit) => {
    setOrgUnitDeleteConfirmation({
      isOpen: true,
      unit,
    });
  };

  const confirmDeleteOrgUnit = async () => {
    if (!orgUnitDeleteConfirmation.unit) return;

    try {
      setIsDeletingOrgUnit(true);

      await deleteOrgUnit(orgUnitDeleteConfirmation.unit.org_unit_id);

      // Refresh the structure for the department
      const departmentId = orgUnitDeleteConfirmation.unit.department_id;
      await fetchDepartmentStructure(departmentId);

      // Close confirmation modal
      setOrgUnitDeleteConfirmation({ isOpen: false, unit: null });
    } catch (error: any) {
      console.error('Error deleting unit:', error);
      setError(error.message || 'Failed to delete organizational unit');
    } finally {
      setIsDeletingOrgUnit(false);
    }
  };

  const handleOrgUnitModalSuccess = async () => {
    setOrgUnitModalState({ isOpen: false, mode: 'create', unit: null, parentUnit: null, department: null });

    // Refresh the structure for the relevant department
    const departmentId = orgUnitModalState.department?.department_id || orgUnitModalState.unit?.department_id;
    if (departmentId) {
      await fetchDepartmentStructure(departmentId);
    }
  };

  const handleOrgUnitModalClose = () => {
    setOrgUnitModalState({ isOpen: false, mode: 'create', unit: null, parentUnit: null, department: null });
  };

  return (
    <div className="space-y-6">
      {/* Create Department Modal */}
      <DepartmentModal
        isOpen={isCreateModalOpen}
        onClose={closeCreateModal}
        onSuccess={() => {
          closeCreateModal();
          fetchDepartments();
        }}
      />

      {/* Edit Department Modal */}
      <DepartmentModal
        isOpen={isEditModalOpen}
        onClose={closeEditModal}
        onSuccess={() => {
          closeEditModal();
          fetchDepartments();
        }}
        department={selectedDepartment}
        isEditing={true}
      />

      {/* Organizational Unit Modal */}
      <OrgUnitModal
        isOpen={orgUnitModalState.isOpen}
        onClose={handleOrgUnitModalClose}
        onSuccess={handleOrgUnitModalSuccess}
        orgUnit={orgUnitModalState.unit}
        isEditing={orgUnitModalState.mode === 'edit'}
        selectedDepartment={orgUnitModalState.department}
        selectedParentUnit={orgUnitModalState.parentUnit}
      />

      {/* Organizational Unit Delete Confirmation */}
      <ConfirmationModal
        isOpen={orgUnitDeleteConfirmation.isOpen}
        onClose={() => setOrgUnitDeleteConfirmation({ isOpen: false, unit: null })}
        onConfirm={confirmDeleteOrgUnit}
        title="Delete Organizational Unit"
        message={
          orgUnitDeleteConfirmation.unit
            ? `Are you sure you want to delete "${orgUnitDeleteConfirmation.unit.name}"? This action cannot be undone and will also delete all sub-units within this unit.`
            : ''
        }
        confirmText="Delete Unit"
        cancelText="Cancel"
        type="danger"
        isLoading={isDeletingOrgUnit}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteConfirmOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            {/* Background overlay */}
            <div
              className="fixed inset-0 transition-opacity"
              aria-hidden="true"
              onClick={closeDeleteConfirm}
            >
              <div className="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            {/* Modal panel */}
            <div
              className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
              onClick={e => e.stopPropagation()}
            >
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg className="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                  <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                    <h3 className="text-lg leading-6 font-medium text-gray-900">Delete Department</h3>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete the department "{departmentToDelete?.name}"? This action cannot be undone.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={handleDeleteDepartment}
                  disabled={isLoading}
                >
                  {isLoading ? 'Deleting...' : 'Delete'}
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
                  onClick={closeDeleteConfirm}
                  disabled={isLoading}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-secondary-dark">Department Management</h1>
        <div>
          <button
            className="btn-primary py-2 px-4 text-sm font-medium rounded-md transition-all flex items-center"
            onClick={openCreateModal}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Department
          </button>
        </div>
      </div>

      {/* Breadcrumbs */}
      <div className="text-sm text-secondary">
        <Link href="/dashboard/hr" className="hover:text-primary">Dashboard</Link>
        <span className="mx-2">/</span>
        <span className="text-secondary-dark">Departments</span>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
          {error}
        </div>
      )}

      <DashboardCard title="Department List">
        {isLoading ? (
          <div className="py-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-secondary">Loading departments...</p>
          </div>
        ) : departments.length === 0 ? (
          <div className="py-8 text-center">
            <p className="text-secondary">No departments found.</p>
            <button
              onClick={openCreateModal}
              className="mt-2 text-primary hover:text-primary-dark"
            >
              Add your first department
            </button>
          </div>
        ) : (
          <div className="space-y-4">
            {departments.map((department) => {
              const isExpanded = expandedDepartments.has(department.department_id);
              const structure = departmentStructures.get(department.department_id);
              const isLoadingStructure = loadingStructures.has(department.department_id);

              return (
                <div key={department.department_id} className="border border-gray-200 rounded-lg">
                  {/* Department Header */}
                  <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => toggleDepartmentExpansion(department)}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-5 w-5" />
                        ) : (
                          <ChevronRight className="h-5 w-5" />
                        )}
                      </button>
                      <Building2 className="h-5 w-5 text-primary" />
                      <div>
                        <h3 className="text-lg font-medium text-secondary-dark">{department.name}</h3>
                        <p className="text-sm text-secondary">{department.description || 'No description'}</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-secondary">
                        Created: {formatDate(department.created_at)}
                      </span>
                      <button
                        className="text-primary hover:text-primary-dark px-3 py-1 text-sm"
                        onClick={() => openEditModal(department)}
                      >
                        Edit
                      </button>
                      <button
                        className="text-red-600 hover:text-red-800 px-3 py-1 text-sm"
                        onClick={() => openDeleteConfirm(department)}
                      >
                        Delete
                      </button>
                    </div>
                  </div>

                  {/* Organizational Units Section */}
                  {isExpanded && (
                    <div className="px-6 py-4 bg-white">
                      {isLoadingStructure ? (
                        <div className="py-8 text-center">
                          <div className="inline-block animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                          <p className="mt-2 text-sm text-secondary">Loading organizational structure...</p>
                        </div>
                      ) : structure ? (
                        <OrgUnitTree
                          departmentStructure={structure}
                          onAddUnit={handleAddOrgUnit}
                          onEditUnit={handleEditOrgUnit}
                          onDeleteUnit={handleDeleteOrgUnit}
                          isLoading={false}
                          showDepartmentHeader={false}
                        />
                      ) : (
                        <div className="py-8 text-center">
                          <Users className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                          <p className="text-secondary mb-4">No organizational units found for this department.</p>
                          <button
                            onClick={() => handleAddOrgUnit(department.department_id)}
                            className="bg-primary text-white px-4 py-2 rounded-md hover:bg-primary-dark transition-colors"
                          >
                            Add First Unit
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </DashboardCard>
    </div>
  );
};

export default DepartmentsContent;
