"use client";

import React, { useState, useEffect } from "react";
import {
  Search,
  Filter,
  Calendar,
  Users,
  Eye,
  Clock,
  MapPin,
  Award,
  Gift,
  ChevronLeft,
  ChevronRight,
  RefreshCw,
  Download,
  TrendingUp,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import Breadcrumb from "@/components/ui/Breadcrumb";
import { generateBreadcrumbs } from "@/lib/breadcrumb-utils";
import {
  getCustomerVisits,
  getDailySummary,
  searchVisits,
} from "@/lib/customer-visit";
import {
  CustomerVisit,
  CustomerVisitFilters,
  CustomerVisitsListResponse,
  CustomerVisitsFilteredResponse,
  DailySummary,
  VisitType,
  VisitSource,
  getVisitTypeLabel,
  getVisitSourceLabel,
  getVisitTypeColor,
  getVisitSourceColor,
  formatVisitTime,
  formatVisitDate,
  getDurationDisplay,
  isRecentVisit,
  VISIT_TYPES,
  VISIT_SOURCES,
} from "@/types/customer-visit";
import { formatCustomerName } from "@/types/customer";
import CustomerVisitDetailsModal from "@/components/customer/CustomerVisitDetailsModal";
import VisitStorageFormModal from "@/components/customer-visit/VisitStorageFormModal";
import VisitServiceConsumptionFormModal from "@/components/customer-visit/VisitServiceConsumptionFormModal";

interface CustomerVisitsContentProps {}

const CustomerVisitsContent: React.FC<CustomerVisitsContentProps> = () => {
  const { user } = useAuth();
  const pathname = usePathname();
  const breadcrumbs = generateBreadcrumbs(pathname, user?.role);

  // State management
  const [visits, setVisits] = useState<CustomerVisit[]>([]);
  const [dailySummary, setDailySummary] = useState<DailySummary | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingSummary, setIsLoadingSummary] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [summaryError, setSummaryError] = useState<string | null>(null);

  // Filter and search state
  const [searchTerm, setSearchTerm] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<CustomerVisitFilters>({
    page: 1,
    per_page: 20,
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [hasNext, setHasNext] = useState(false);
  const [hasPrev, setHasPrev] = useState(false);

  // Modal state
  const [selectedVisit, setSelectedVisit] = useState<CustomerVisit | null>(
    null
  );
  const [showVisitDetails, setShowVisitDetails] = useState(false);
  const [showStorageForm, setShowStorageForm] = useState(false);
  const [showConsumptionForm, setShowConsumptionForm] = useState(false);

  // Load visits
  const loadVisits = async (newFilters?: CustomerVisitFilters) => {
    setIsLoading(true);
    setError(null);

    try {
      const filtersToUse = newFilters || filters;
      let response: CustomerVisitsListResponse | CustomerVisitsFilteredResponse;

      if (searchTerm.trim()) {
        response = await searchVisits(searchTerm, filtersToUse);
      } else {
        response = await getCustomerVisits(filtersToUse);
      }

      if ("extend" in response) {
        // No filters response format
        setVisits(response.extend.visits);
        setTotalRecords(response.extend.count);
        setTotalPages(
          Math.ceil(response.extend.count / (filtersToUse.per_page || 20))
        );
        setHasNext(false);
        setHasPrev(false);
      } else {
        // Filtered response format
        setVisits(response.visits);
        setCurrentPage(response.pagination.page);
        setTotalPages(response.pagination.total_pages);
        setTotalRecords(response.pagination.total_records);
        setHasNext(response.pagination.has_next);
        setHasPrev(response.pagination.has_prev);
      }
    } catch (err) {
      console.error("Error loading visits:", err);
      setError(err instanceof Error ? err.message : "Failed to load visits");
      setVisits([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Load daily summary
  const loadDailySummary = async (date?: string) => {
    setIsLoadingSummary(true);
    setSummaryError(null);

    try {
      const response = await getDailySummary(date);
      setDailySummary(response.extend.summary);
    } catch (err) {
      console.error("Error loading daily summary:", err);
      setSummaryError(
        err instanceof Error ? err.message : "Failed to load summary"
      );
    } finally {
      setIsLoadingSummary(false);
    }
  };

  // Initial load
  useEffect(() => {
    loadVisits();
    loadDailySummary();
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      loadVisits({ ...filters, page: 1 });
    } else {
      loadVisits({ ...filters, page: 1 });
    }
  };

  // Handle filter change
  const handleFilterChange = (key: keyof CustomerVisitFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    loadVisits(newFilters);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    setCurrentPage(page);
    loadVisits(newFilters);
  };

  // Clear filters
  const clearFilters = () => {
    const clearedFilters: CustomerVisitFilters = {
      page: 1,
      per_page: 20,
    };
    setFilters(clearedFilters);
    setSearchTerm("");
    loadVisits(clearedFilters);
  };

  // Handle visit details
  const handleViewVisit = (visit: CustomerVisit) => {
    setSelectedVisit(visit);
    setShowVisitDetails(true);
  };

  // Handle store items for visit
  const handleStoreItems = () => {
    setShowVisitDetails(false);
    setShowStorageForm(true);
  };

  // Handle record consumption for visit
  const handleRecordConsumption = () => {
    setShowVisitDetails(false);
    setShowConsumptionForm(true);
  };

  // Handle form success
  const handleFormSuccess = () => {
    setShowStorageForm(false);
    setShowConsumptionForm(false);
    setSelectedVisit(null);
    loadVisits(); // Refresh the visits list
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setShowStorageForm(false);
    setShowConsumptionForm(false);
    // Return to visit details if we came from there
    if (selectedVisit) {
      setShowVisitDetails(true);
    }
  };

  // Refresh data
  const handleRefresh = () => {
    loadVisits();
    loadDailySummary();
  };

  // Get today's date for default filter
  const today = new Date().toISOString().split("T")[0];

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <Breadcrumb items={breadcrumbs} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customer Visits</h1>
          <p className="mt-1 text-sm text-gray-500">
            Track and manage customer visits and loyalty activities
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          <button
            onClick={handleRefresh}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          <button className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <Download className="h-4 w-4 mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Daily Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {isLoadingSummary ? (
          Array.from({ length: 4 }).map((_, index) => (
            <div
              key={index}
              className="bg-white rounded-lg border border-gray-200 p-6"
            >
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))
        ) : summaryError ? (
          <div className="col-span-full bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-sm text-red-600">
              Error loading summary: {summaryError}
            </p>
          </div>
        ) : dailySummary ? (
          <>
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Total Visits
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dailySummary.total_visits}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Unique Customers
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dailySummary.unique_customers}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Award className="h-8 w-8 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Loyalty Visits
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dailySummary.loyalty_visits}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <Gift className="h-8 w-8 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-500">
                    Reward Redemptions
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {dailySummary.reward_redemptions}
                  </p>
                </div>
              </div>
            </div>
          </>
        ) : null}
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          {/* Search */}
          <div className="flex-1 max-w-lg">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary focus:border-primary"
                placeholder="Search by customer name, email, or membership..."
              />
            </div>
          </div>

          {/* Filter Toggle */}
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`inline-flex items-center px-3 py-2 border rounded-md text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary ${
                showFilters
                  ? "border-primary text-primary bg-primary-light"
                  : "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
              }`}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
            {(filters.date ||
              filters.start_date ||
              filters.visit_type ||
              filters.source) && (
              <button
                onClick={clearFilters}
                className="text-sm text-gray-500 hover:text-gray-700"
              >
                Clear Filters
              </button>
            )}
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Date Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Date
                </label>
                <input
                  type="date"
                  value={filters.date || ""}
                  onChange={(e) =>
                    handleFilterChange("date", e.target.value || undefined)
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                />
              </div>

              {/* Visit Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Visit Type
                </label>
                <select
                  value={filters.visit_type || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "visit_type",
                      e.target.value || undefined
                    )
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Types</option>
                  {VISIT_TYPES.map((type) => (
                    <option key={type.value} value={type.value}>
                      {type.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Source Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Source
                </label>
                <select
                  value={filters.source || ""}
                  onChange={(e) =>
                    handleFilterChange("source", e.target.value || undefined)
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Sources</option>
                  {VISIT_SOURCES.map((source) => (
                    <option key={source.value} value={source.value}>
                      {source.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Loyalty Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Loyalty Visit
                </label>
                <select
                  value={filters.is_loyalty_visit?.toString() || ""}
                  onChange={(e) =>
                    handleFilterChange(
                      "is_loyalty_visit",
                      e.target.value ? e.target.value === "true" : undefined
                    )
                  }
                  className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary"
                >
                  <option value="">All Visits</option>
                  <option value="true">Loyalty Visits Only</option>
                  <option value="false">Non-Loyalty Visits</option>
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Visits List */}
      <div className="bg-white rounded-lg border border-gray-200">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <p className="mt-2 text-sm text-gray-600">Loading visits...</p>
          </div>
        ) : error ? (
          <div className="p-8 text-center">
            <div className="text-red-500 mb-4">
              <TrendingUp className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Error Loading Visits
            </h3>
            <p className="text-sm text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => loadVisits()}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Try Again
            </button>
          </div>
        ) : visits.length === 0 ? (
          <div className="p-8 text-center">
            <Users className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No Visits Found
            </h3>
            <p className="text-sm text-gray-600">
              {searchTerm ||
              Object.keys(filters).some(
                (key) =>
                  key !== "page" &&
                  key !== "per_page" &&
                  filters[key as keyof CustomerVisitFilters]
              )
                ? "Try adjusting your search or filters."
                : "No customer visits recorded yet."}
            </p>
          </div>
        ) : (
          <>
            {/* Desktop Table View */}
            <div className="hidden lg:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Visit Details
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type & Source
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Loyalty & Rewards
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Duration
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {visits.map((visit) => (
                    <tr key={visit.visit_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center">
                              <span className="text-sm font-medium text-primary">
                                {formatCustomerName(visit.customer).charAt(0)}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {formatCustomerName(visit.customer)}
                            </div>
                            <div className="text-sm text-gray-500">
                              {visit.customer.membership_number &&
                                `#${visit.customer.membership_number}`}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatVisitDate(visit.visit_date)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatVisitTime(visit.visit_time)}
                          {isRecentVisit(visit.visit_time) && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                              Recent
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitTypeColor(
                              visit.visit_type
                            )}`}
                          >
                            {getVisitTypeLabel(visit.visit_type)}
                          </span>
                          <span
                            className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitSourceColor(
                              visit.source
                            )}`}
                          >
                            {getVisitSourceLabel(visit.source)}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex flex-col space-y-1">
                          {visit.is_loyalty_visit && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                              <Award className="h-3 w-3 mr-1" />
                              Loyalty
                            </span>
                          )}
                          {visit.reward_redeemed && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                              <Gift className="h-3 w-3 mr-1" />
                              Reward
                            </span>
                          )}
                          {visit.loyalty_points_earned > 0 && (
                            <span className="text-xs text-gray-500">
                              +{visit.loyalty_points_earned} pts
                            </span>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getDurationDisplay(visit.duration_minutes)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewVisit(visit)}
                          className="text-primary hover:text-primary-dark"
                        >
                          <Eye className="h-4 w-4" />
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View */}
            <div className="lg:hidden">
              <div className="divide-y divide-gray-200">
                {visits.map((visit) => (
                  <div key={visit.visit_id} className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-primary-light flex items-center justify-center">
                            <span className="text-sm font-medium text-primary">
                              {formatCustomerName(visit.customer).charAt(0)}
                            </span>
                          </div>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {formatCustomerName(visit.customer)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatVisitDate(visit.visit_date)} at{" "}
                            {formatVisitTime(visit.visit_time)}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={() => handleViewVisit(visit)}
                        className="text-primary hover:text-primary-dark"
                      >
                        <Eye className="h-5 w-5" />
                      </button>
                    </div>

                    <div className="mt-3 flex flex-wrap gap-2">
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitTypeColor(
                          visit.visit_type
                        )}`}
                      >
                        {getVisitTypeLabel(visit.visit_type)}
                      </span>
                      <span
                        className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getVisitSourceColor(
                          visit.source
                        )}`}
                      >
                        {getVisitSourceLabel(visit.source)}
                      </span>
                      {visit.is_loyalty_visit && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                          <Award className="h-3 w-3 mr-1" />
                          Loyalty
                        </span>
                      )}
                      {visit.reward_redeemed && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          <Gift className="h-3 w-3 mr-1" />
                          Reward
                        </span>
                      )}
                      {isRecentVisit(visit.visit_time) && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Recent
                        </span>
                      )}
                    </div>

                    {(visit.duration_minutes ||
                      visit.loyalty_points_earned > 0) && (
                      <div className="mt-2 text-xs text-gray-500">
                        {visit.duration_minutes && (
                          <span>
                            Duration:{" "}
                            {getDurationDisplay(visit.duration_minutes)}
                          </span>
                        )}
                        {visit.loyalty_points_earned > 0 && (
                          <span className="ml-4">
                            +{visit.loyalty_points_earned} points
                          </span>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
                <div className="text-sm text-gray-700">
                  Showing {(currentPage - 1) * (filters.per_page || 20) + 1} to{" "}
                  {Math.min(
                    currentPage * (filters.per_page || 20),
                    totalRecords
                  )}{" "}
                  of {totalRecords} results
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={!hasPrev}
                    className="p-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </button>
                  <span className="px-3 py-1 text-sm">
                    Page {currentPage} of {totalPages}
                  </span>
                  <button
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={!hasNext}
                    className="p-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* Visit Details Modal */}
      {showVisitDetails && selectedVisit && (
        <CustomerVisitDetailsModal
          visit={selectedVisit}
          onClose={() => {
            setShowVisitDetails(false);
            setSelectedVisit(null);
          }}
          onStoreItems={handleStoreItems}
          onRecordConsumption={handleRecordConsumption}
        />
      )}

      {/* Visit Storage Form Modal */}
      {showStorageForm && selectedVisit && (
        <VisitStorageFormModal
          visit={selectedVisit}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}

      {/* Visit Service Consumption Form Modal */}
      {showConsumptionForm && selectedVisit && (
        <VisitServiceConsumptionFormModal
          visit={selectedVisit}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      )}
    </div>
  );
};

export default CustomerVisitsContent;
