"use client";

import React from "react";
import {
  X,
  MapPin,
  Calendar,
  CheckCircle,
  XCircle,
  FileText,
  Clock,
} from "lucide-react";
import {
  StorageLocation,
  getAvailabilityLabel,
  getAvailabilityColor,
  isLocationActive,
} from "@/types/storage-location";

interface StorageLocationDetailsModalProps {
  storageLocation: StorageLocation;
  onClose: () => void;
}

const StorageLocationDetailsModal: React.FC<
  StorageLocationDetailsModalProps
> = ({ storageLocation, onClose }) => {
  const isActive = isLocationActive(storageLocation);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="p-2 bg-primary-light rounded-lg mr-3">
              <MapPin className="w-6 h-6 text-primary" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Storage Location Details
              </h2>
              <p className="text-sm text-gray-600">
                Location {storageLocation.location_number}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Status Banner */}
          <div
            className={`p-4 rounded-lg border ${
              isActive
                ? "bg-green-50 border-green-200"
                : "bg-red-50 border-red-200"
            }`}
          >
            <div className="flex items-center">
              {isActive ? (
                <CheckCircle className="w-5 h-5 text-green-600 mr-3" />
              ) : (
                <XCircle className="w-5 h-5 text-red-600 mr-3" />
              )}
              <div>
                <h3
                  className={`font-medium ${
                    isActive ? "text-green-800" : "text-red-800"
                  }`}
                >
                  {getAvailabilityLabel(storageLocation.is_available)}
                </h3>
                <p
                  className={`text-sm mt-1 ${
                    isActive ? "text-green-700" : "text-red-700"
                  }`}
                >
                  {isActive
                    ? "This storage location is currently available for use."
                    : "This storage location is currently unavailable."}
                </p>
              </div>
            </div>
          </div>

          {/* Basic Information */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Basic Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Location Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location Number
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <MapPin className="w-4 h-4 text-gray-500 mr-2" />
                  <span className="text-sm text-gray-900">
                    {storageLocation.location_number}
                  </span>
                </div>
              </div>

              {/* Location ID */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location ID
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span className="text-sm text-gray-900 font-mono">
                    {storageLocation.location_id}
                  </span>
                </div>
              </div>

              {/* Status */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Status
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <span
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAvailabilityColor(
                      storageLocation.is_available
                    )}`}
                  >
                    {getAvailabilityLabel(storageLocation.is_available)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Notes */}
          {storageLocation.notes && (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notes</h3>
              <div className="p-4 bg-gray-50 rounded-lg">
                <div className="flex items-start">
                  <FileText className="w-4 h-4 text-gray-500 mr-2 mt-0.5" />
                  <p className="text-sm text-gray-900 leading-relaxed">
                    {storageLocation.notes}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Timestamps */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">
              Record Information
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Created At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Created At
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Calendar className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(
                        storageLocation.created_at
                      ).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(
                        storageLocation.created_at
                      ).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Updated At */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Updated
                </label>
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <Clock className="w-4 h-4 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm text-gray-900">
                      {new Date(
                        storageLocation.updated_at
                      ).toLocaleDateString()}
                    </div>
                    <div className="text-xs text-gray-500">
                      {new Date(
                        storageLocation.updated_at
                      ).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <MapPin className="w-4 h-4 text-blue-600" />
                </div>
              </div>
              <div className="ml-3">
                <h4 className="text-sm font-medium text-blue-900">
                  Storage Management
                </h4>
                <p className="text-sm text-blue-700 mt-1">
                  This storage location can be used to store customer
                  belongings, equipment, or other items. Track usage and manage
                  availability through the customer storage management section.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default StorageLocationDetailsModal;
