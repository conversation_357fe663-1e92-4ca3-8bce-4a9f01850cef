"use client";

import React, { useState, useMemo } from "react";
import { Filter } from "lucide-react";
import DashboardCard from "@/components/ui/DashboardCard";

interface Employee {
  employee_id: string;
  first_name: string;
  last_name: string;
  full_name: string;
  email: string | null;
  phone_number: string | null;
  position: string | null;
  department_id: string | null;
  department_name: string | null;
  status: string;
  attendance: {
    status: string;
    attendance_id?: string;
    check_in_time?: string | null;
    check_out_time?: string | null;
    total_hours?: number | null;
    reason?: string;
  };
}

interface AttendanceEmployeeListProps {
  presentEmployees: Employee[];
  absentEmployees: Employee[];
  onLeaveEmployees: Employee[];
  date: string;
}

// Sorting options for different tabs
const PRESENT_SORT_OPTIONS = [
  { value: "name_asc", label: "Name (A-Z)" },
  { value: "name_desc", label: "Name (Z-A)" },
  { value: "department_asc", label: "Department (A-Z)" },
  { value: "department_desc", label: "Department (Z-A)" },
  { value: "checkin_asc", label: "Check-in (Earliest First)" },
  { value: "checkin_desc", label: "Check-in (Latest First)" },
  { value: "checkout_asc", label: "Check-out (Earliest First)" },
  { value: "checkout_desc", label: "Check-out (Latest First)" },
  { value: "hours_asc", label: "Hours (Least First)" },
  { value: "hours_desc", label: "Hours (Most First)" },
] as const;

const ABSENT_LEAVE_SORT_OPTIONS = [
  { value: "name_asc", label: "Name (A-Z)" },
  { value: "name_desc", label: "Name (Z-A)" },
  { value: "department_asc", label: "Department (A-Z)" },
  { value: "department_desc", label: "Department (Z-A)" },
  { value: "position_asc", label: "Position (A-Z)" },
  { value: "position_desc", label: "Position (Z-A)" },
] as const;

type PresentSortOption = (typeof PRESENT_SORT_OPTIONS)[number]["value"];
type AbsentLeaveSortOption = (typeof ABSENT_LEAVE_SORT_OPTIONS)[number]["value"];

const AttendanceEmployeeList: React.FC<AttendanceEmployeeListProps> = ({
  presentEmployees,
  absentEmployees,
  onLeaveEmployees,
  date,
}) => {
  const [activeTab, setActiveTab] = useState<"present" | "absent" | "leave">(
    "present"
  );

  // Sorting states for each tab
  const [presentSort, setPresentSort] = useState<PresentSortOption>("name_asc");
  const [absentSort, setAbsentSort] = useState<AbsentLeaveSortOption>("name_asc");
  const [leaveSort, setLeaveSort] = useState<AbsentLeaveSortOption>("name_asc");

  // Format time for display (HH:MM:SS -> HH:MM AM/PM)
  const formatTime = (timeString: string | null | undefined) => {
    if (!timeString) return "N/A";

    try {
      const date = new Date(timeString);
      return date.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch (error) {
      return timeString;
    }
  };

  // Get sorted employees for each tab
  const sortedPresentEmployees = useMemo(() => {
    // Helper function to compare time strings
    const compareTime = (timeA: string | null | undefined, timeB: string | null | undefined) => {
      if (!timeA && !timeB) return 0;
      if (!timeA) return 1; // null values go to end
      if (!timeB) return -1;

      try {
        const dateA = new Date(timeA).getTime();
        const dateB = new Date(timeB).getTime();
        return dateA - dateB;
      } catch (error) {
        return timeA.localeCompare(timeB);
      }
    };

    return [...presentEmployees].sort((a, b) => {
      switch (presentSort) {
        case "name_asc":
          return a.full_name.localeCompare(b.full_name);
        case "name_desc":
          return b.full_name.localeCompare(a.full_name);
        case "department_asc":
          return (a.department_name || "").localeCompare(b.department_name || "");
        case "department_desc":
          return (b.department_name || "").localeCompare(a.department_name || "");
        case "checkin_asc":
          return compareTime(a.attendance.check_in_time, b.attendance.check_in_time);
        case "checkin_desc":
          return compareTime(b.attendance.check_in_time, a.attendance.check_in_time);
        case "checkout_asc":
          return compareTime(a.attendance.check_out_time, b.attendance.check_out_time);
        case "checkout_desc":
          return compareTime(b.attendance.check_out_time, a.attendance.check_out_time);
        case "hours_asc":
          return (a.attendance.total_hours || 0) - (b.attendance.total_hours || 0);
        case "hours_desc":
          return (b.attendance.total_hours || 0) - (a.attendance.total_hours || 0);
        default:
          return 0;
      }
    });
  }, [presentEmployees, presentSort]);

  const sortedAbsentEmployees = useMemo(() => {
    return [...absentEmployees].sort((a, b) => {
      switch (absentSort) {
        case "name_asc":
          return a.full_name.localeCompare(b.full_name);
        case "name_desc":
          return b.full_name.localeCompare(a.full_name);
        case "department_asc":
          return (a.department_name || "").localeCompare(b.department_name || "");
        case "department_desc":
          return (b.department_name || "").localeCompare(a.department_name || "");
        case "position_asc":
          return (a.position || "").localeCompare(b.position || "");
        case "position_desc":
          return (b.position || "").localeCompare(a.position || "");
        default:
          return 0;
      }
    });
  }, [absentEmployees, absentSort]);

  const sortedLeaveEmployees = useMemo(() => {
    return [...onLeaveEmployees].sort((a, b) => {
      switch (leaveSort) {
        case "name_asc":
          return a.full_name.localeCompare(b.full_name);
        case "name_desc":
          return b.full_name.localeCompare(a.full_name);
        case "department_asc":
          return (a.department_name || "").localeCompare(b.department_name || "");
        case "department_desc":
          return (b.department_name || "").localeCompare(a.department_name || "");
        case "position_asc":
          return (a.position || "").localeCompare(b.position || "");
        case "position_desc":
          return (b.position || "").localeCompare(a.position || "");
        default:
          return 0;
      }
    });
  }, [onLeaveEmployees, leaveSort]);

  return (
    <DashboardCard title={`Employee Attendance - ${date}`}>
      <div className="mb-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("present")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "present"
                  ? "border-primary text-primary"
                  : "border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300"
              }`}
            >
              Present ({presentEmployees.length})
            </button>
            <button
              onClick={() => setActiveTab("absent")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "absent"
                  ? "border-primary text-primary"
                  : "border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300"
              }`}
            >
              Absent ({absentEmployees.length})
            </button>
            <button
              onClick={() => setActiveTab("leave")}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === "leave"
                  ? "border-primary text-primary"
                  : "border-transparent text-secondary hover:text-secondary-dark hover:border-gray-300"
              }`}
            >
              On Leave ({onLeaveEmployees.length})
            </button>
          </nav>
        </div>
      </div>

      {/* Sorting Controls */}
      <div className="mb-4 flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Filter className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-700">Sort by:</span>
          <select
            value={
              activeTab === "present"
                ? presentSort
                : activeTab === "absent"
                ? absentSort
                : leaveSort
            }
            onChange={(e) => {
              if (activeTab === "present") {
                setPresentSort(e.target.value as PresentSortOption);
              } else if (activeTab === "absent") {
                setAbsentSort(e.target.value as AbsentLeaveSortOption);
              } else {
                setLeaveSort(e.target.value as AbsentLeaveSortOption);
              }
            }}
            className="px-3 py-1 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          >
            {(activeTab === "present" ? PRESENT_SORT_OPTIONS : ABSENT_LEAVE_SORT_OPTIONS).map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
        <div className="text-sm text-gray-500">
          {activeTab === "present" && `${sortedPresentEmployees.length} employees`}
          {activeTab === "absent" && `${sortedAbsentEmployees.length} employees`}
          {activeTab === "leave" && `${sortedLeaveEmployees.length} employees`}
        </div>
      </div>

      <div className="overflow-x-auto">
        {activeTab === "present" && (
          <>
            {sortedPresentEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">
                  No employees present on this date.
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check In
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Check Out
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Hours
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedPresentEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-primary text-white flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.position || "No position"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || "No Department"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {formatTime(employee.attendance.check_in_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {formatTime(employee.attendance.check_out_time)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.attendance.total_hours !== null &&
                          employee.attendance.total_hours !== undefined
                            ? `${employee.attendance.total_hours.toFixed(
                                2
                              )} hrs`
                            : "N/A"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}

        {activeTab === "absent" && (
          <>
            {sortedAbsentEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">
                  No employees absent on this date.
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Reason
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedAbsentEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-red-100 text-red-600 flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.email || "No email"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || "No Department"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.position || "No position"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-red-600">
                          {employee.attendance.reason || "No reason provided"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}

        {activeTab === "leave" && (
          <>
            {sortedLeaveEmployees.length === 0 ? (
              <div className="py-8 text-center">
                <p className="text-secondary">
                  No employees on leave on this date.
                </p>
              </div>
            ) : (
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Employee
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Department
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Position
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-secondary-dark uppercase tracking-wider">
                      Reason
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {sortedLeaveEmployees.map((employee) => (
                    <tr key={employee.employee_id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="h-10 w-10 rounded-full bg-yellow-100 text-yellow-600 flex items-center justify-center">
                            <span className="text-sm font-medium">
                              {employee.first_name.charAt(0)}
                              {employee.last_name.charAt(0)}
                            </span>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-secondary-dark">
                              {employee.full_name}
                            </div>
                            <div className="text-xs text-secondary">
                              {employee.email || "No email"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.department_name || "No Department"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-secondary-dark">
                          {employee.position || "No position"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-yellow-600">
                          {employee.attendance.reason || "No reason provided"}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </>
        )}
      </div>
    </DashboardCard>
  );
};

export default AttendanceEmployeeList;
