/**
 * Customer Integration Tests
 * Tests to verify customer components work together correctly
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { AuthContext } from '@/contexts/AuthContext';
import CustomersContent from '@/components/hr/CustomersContent';
import CustomerRegistrationForm from '@/components/customer/CustomerRegistrationForm';
import {
  Customer,
  CUSTOMER_SEGMENTS,
  CONTACT_METHODS,
  formatCustomerName,
  getCustomerSegmentLabel,
  getCustomerStatusLabel,
} from '@/types/customer';

// Mock the customer API
jest.mock('@/lib/customer', () => ({
  getCustomers: jest.fn(),
  createCustomer: jest.fn(),
  updateCustomer: jest.fn(),
  deleteCustomer: jest.fn(),
  getCustomerStatistics: jest.fn(),
}));

// Mock auth context
const mockAuthContext = {
  user: {
    user_id: '1',
    username: 'testuser',
    role: 'hr',
    employee_id: '1',
  },
  companies: [
    {
      company_id: '1',
      company_name: 'Test Company',
      company_tin: '*********',
    },
  ],
  login: jest.fn(),
  logout: jest.fn(),
  isLoading: false,
};

// Mock customer data
const mockCustomer: Customer = {
  customer_id: '123',
  first_name: 'John',
  last_name: 'Doe',
  full_name: 'John Doe',
  email: '<EMAIL>',
  phone_number: '+**********',
  membership_number: '12345',
  customer_segment: 'vip',
  preferred_contact_method: 'email',
  notes: 'VIP customer',
  date_of_birth: '1990-01-01',
  marketing_consent: true,
  status: 'active',
  registration_date: '2025-01-01',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  available_rewards: 100,
  visit_count: 5,
};

const mockCustomersResponse = {
  code: 100,
  extend: {
    customers: [mockCustomer],
    pagination: {
      has_next: false,
      has_prev: false,
      page: 1,
      pages: 1,
      per_page: 10,
      total_count: 1,
    },
  },
  msg: 'Success!',
};

describe('Customer Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('CustomersContent Component', () => {
    it('should render customer list correctly', async () => {
      const { getCustomers } = require('@/lib/customer');
      getCustomers.mockResolvedValue(mockCustomersResponse);

      render(
        <AuthContext.Provider value={mockAuthContext}>
          <CustomersContent />
        </AuthContext.Provider>
      );

      // Check if the component renders
      expect(screen.getByText('Customer Management')).toBeInTheDocument();
      expect(screen.getByText('Add Customer')).toBeInTheDocument();

      // Wait for customers to load
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Check if customer details are displayed
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('+**********')).toBeInTheDocument();
      expect(screen.getByText('VIP')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });

    it('should handle empty customer list', async () => {
      const { getCustomers } = require('@/lib/customer');
      getCustomers.mockResolvedValue({
        code: 100,
        extend: {
          customers: [],
          pagination: {
            has_next: false,
            has_prev: false,
            page: 1,
            pages: 1,
            per_page: 10,
            total_count: 0,
          },
        },
        msg: 'Success!',
      });

      render(
        <AuthContext.Provider value={mockAuthContext}>
          <CustomersContent />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(screen.getByText('No Customers Found')).toBeInTheDocument();
        expect(screen.getByText('Get started by adding your first customer.')).toBeInTheDocument();
      });
    });

    it('should handle API errors', async () => {
      const { getCustomers } = require('@/lib/customer');
      getCustomers.mockRejectedValue(new Error('API Error'));

      render(
        <AuthContext.Provider value={mockAuthContext}>
          <CustomersContent />
        </AuthContext.Provider>
      );

      await waitFor(() => {
        expect(screen.getByText('Error Loading Customers')).toBeInTheDocument();
        expect(screen.getByText('API Error')).toBeInTheDocument();
      });
    });
  });

  describe('CustomerRegistrationForm Component', () => {
    it('should render registration form correctly', () => {
      const mockOnSuccess = jest.fn();
      const mockOnCancel = jest.fn();

      render(
        <CustomerRegistrationForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Check if form elements are present
      expect(screen.getByText('Add New Customer')).toBeInTheDocument();
      expect(screen.getByLabelText('First Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Last Name *')).toBeInTheDocument();
      expect(screen.getByLabelText('Email Address')).toBeInTheDocument();
      expect(screen.getByLabelText('Phone Number')).toBeInTheDocument();
      expect(screen.getByLabelText('Customer Segment')).toBeInTheDocument();
      expect(screen.getByLabelText('Preferred Contact Method')).toBeInTheDocument();
      expect(screen.getByText('Create Customer')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('should validate required fields', async () => {
      const mockOnSuccess = jest.fn();
      const mockOnCancel = jest.fn();

      render(
        <CustomerRegistrationForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Try to submit without filling required fields
      fireEvent.click(screen.getByText('Create Customer'));

      await waitFor(() => {
        expect(screen.getByText('First name is required')).toBeInTheDocument();
        expect(screen.getByText('Last name is required')).toBeInTheDocument();
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('should validate email format', async () => {
      const mockOnSuccess = jest.fn();
      const mockOnCancel = jest.fn();

      render(
        <CustomerRegistrationForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      // Fill required fields
      fireEvent.change(screen.getByLabelText('First Name *'), {
        target: { value: 'John' },
      });
      fireEvent.change(screen.getByLabelText('Last Name *'), {
        target: { value: 'Doe' },
      });

      // Enter invalid email
      fireEvent.change(screen.getByLabelText('Email Address'), {
        target: { value: 'invalid-email' },
      });

      fireEvent.click(screen.getByText('Create Customer'));

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument();
      });

      expect(mockOnSuccess).not.toHaveBeenCalled();
    });

    it('should call onCancel when cancel button is clicked', () => {
      const mockOnSuccess = jest.fn();
      const mockOnCancel = jest.fn();

      render(
        <CustomerRegistrationForm
          onSuccess={mockOnSuccess}
          onCancel={mockOnCancel}
        />
      );

      fireEvent.click(screen.getByText('Cancel'));
      expect(mockOnCancel).toHaveBeenCalled();
    });
  });

  describe('Customer Type Helper Functions', () => {
    it('should format customer name correctly', () => {
      expect(formatCustomerName(mockCustomer)).toBe('John Doe');
      
      const customerWithoutFullName = { ...mockCustomer, full_name: '' };
      expect(formatCustomerName(customerWithoutFullName)).toBe('John Doe');
    });

    it('should get customer segment label correctly', () => {
      expect(getCustomerSegmentLabel('vip')).toBe('VIP');
      expect(getCustomerSegmentLabel('regular')).toBe('Regular');
      expect(getCustomerSegmentLabel('premium')).toBe('Premium');
    });

    it('should get customer status label correctly', () => {
      expect(getCustomerStatusLabel('active')).toBe('Active');
      expect(getCustomerStatusLabel('inactive')).toBe('Inactive');
      expect(getCustomerStatusLabel('suspended')).toBe('Suspended');
      expect(getCustomerStatusLabel('blocked')).toBe('Blocked');
    });
  });

  describe('Customer Constants', () => {
    it('should have correct customer segments', () => {
      expect(CUSTOMER_SEGMENTS).toHaveLength(6);
      expect(CUSTOMER_SEGMENTS.map(s => s.value)).toEqual([
        'regular', 'vip', 'premium', 'gold', 'silver', 'bronze'
      ]);
    });

    it('should have correct contact methods', () => {
      expect(CONTACT_METHODS).toHaveLength(4);
      expect(CONTACT_METHODS.map(m => m.value)).toEqual([
        'email', 'phone', 'sms', 'whatsapp'
      ]);
    });
  });
});
