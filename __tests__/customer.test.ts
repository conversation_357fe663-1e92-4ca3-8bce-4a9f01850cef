/**
 * Customer API Tests
 * Basic tests to verify customer API functions work correctly
 */

import {
  createCustomer,
  getCustomerById,
  updateCustomer,
  deleteCustomer,
  getCustomers,
  getCustomerStatistics,
  searchCustomers,
  getCustomersBySegment,
  getAllCustomers,
} from '@/lib/customer';

import {
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CustomerFilters,
} from '@/types/customer';

// Mock the API functions
jest.mock('@/lib/api', () => ({
  apiGet: jest.fn(),
  apiPost: jest.fn(),
  apiPatch: jest.fn(),
  apiDelete: jest.fn(),
}));

// Mock the auth functions
jest.mock('@/lib/auth', () => ({
  getAccessToken: jest.fn(() => 'mock-token'),
}));

describe('Customer API Functions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createCustomer', () => {
    it('should create a customer with required fields', async () => {
      const mockResponse = {
        extend: {
          customer: {
            customer_id: '123',
            first_name: '<PERSON>',
            last_name: 'Doe',
            full_name: '<PERSON> Doe',
            customer_segment: 'regular',
            marketing_consent: false,
            status: 'active',
            registration_date: '2025-01-01',
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T00:00:00Z',
          },
        },
        msg: 'Success!',
      };

      const { apiPost } = require('@/lib/api');
      apiPost.mockResolvedValue(mockResponse);

      const customerData: CreateCustomerRequest = {
        first_name: 'John',
        last_name: 'Doe',
      };

      const result = await createCustomer(customerData);
      
      expect(apiPost).toHaveBeenCalledWith('api/customers', customerData, {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should throw error when no token is available', async () => {
      const { getAccessToken } = require('@/lib/auth');
      getAccessToken.mockReturnValue(null);

      const customerData: CreateCustomerRequest = {
        first_name: 'John',
        last_name: 'Doe',
      };

      await expect(createCustomer(customerData)).rejects.toThrow('Authentication required');
    });
  });

  describe('getCustomers', () => {
    it('should fetch customers with filters', async () => {
      const mockResponse = {
        code: 100,
        extend: {
          customers: [
            {
              customer_id: '123',
              first_name: 'John',
              last_name: 'Doe',
              full_name: 'John Doe',
              customer_segment: 'vip',
              marketing_consent: true,
              status: 'active',
              registration_date: '2025-01-01',
              created_at: '2025-01-01T00:00:00Z',
              updated_at: '2025-01-01T00:00:00Z',
            },
          ],
          pagination: {
            has_next: false,
            has_prev: false,
            page: 1,
            pages: 1,
            per_page: 10,
            total_count: 1,
          },
        },
        msg: 'Success!',
      };

      const { apiGet } = require('@/lib/api');
      apiGet.mockResolvedValue(mockResponse);

      const filters: CustomerFilters = {
        page: 1,
        per_page: 10,
        segment: 'vip',
        search: 'John',
      };

      const result = await getCustomers(filters);
      
      expect(apiGet).toHaveBeenCalledWith('api/customers?page=1&per_page=10&segment=vip&search=John', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should fetch customers without filters', async () => {
      const mockResponse = {
        code: 100,
        extend: {
          customers: [],
          pagination: {
            has_next: false,
            has_prev: false,
            page: 1,
            pages: 1,
            per_page: 10,
            total_count: 0,
          },
        },
        msg: 'Success!',
      };

      const { apiGet } = require('@/lib/api');
      apiGet.mockResolvedValue(mockResponse);

      const result = await getCustomers();
      
      expect(apiGet).toHaveBeenCalledWith('api/customers', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('updateCustomer', () => {
    it('should update a customer', async () => {
      const mockResponse = {
        extend: {
          customer: {
            customer_id: '123',
            first_name: 'Jane',
            last_name: 'Doe',
            full_name: 'Jane Doe',
            email: '<EMAIL>',
            customer_segment: 'premium',
            marketing_consent: true,
            status: 'active',
            registration_date: '2025-01-01',
            created_at: '2025-01-01T00:00:00Z',
            updated_at: '2025-01-01T01:00:00Z',
          },
        },
        msg: 'Success!',
      };

      const { apiPatch } = require('@/lib/api');
      apiPatch.mockResolvedValue(mockResponse);

      const updateData: UpdateCustomerRequest = {
        first_name: 'Jane',
        email: '<EMAIL>',
        customer_segment: 'premium',
      };

      const result = await updateCustomer('123', updateData);
      
      expect(apiPatch).toHaveBeenCalledWith('api/customers/123', updateData, {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('deleteCustomer', () => {
    it('should delete a customer', async () => {
      const mockResponse = {
        msg: 'Customer deleted successfully',
        success: true,
      };

      const { apiDelete } = require('@/lib/api');
      apiDelete.mockResolvedValue(mockResponse);

      const result = await deleteCustomer('123');
      
      expect(apiDelete).toHaveBeenCalledWith('api/customers/123', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getCustomerStatistics', () => {
    it('should fetch customer statistics', async () => {
      const mockResponse = {
        extend: {
          statistics: {
            customer_id: '123',
            customer: {
              customer_id: '123',
              first_name: 'John',
              last_name: 'Doe',
              full_name: 'John Doe',
              customer_segment: 'vip',
              marketing_consent: true,
              status: 'active',
              registration_date: '2025-01-01',
              created_at: '2025-01-01T00:00:00Z',
              updated_at: '2025-01-01T00:00:00Z',
            },
            total_visits: 10,
            regular_visits: 8,
            loyalty_visits: 2,
            reward_visits: 0,
            complimentary_visits: 0,
            biometric_visits: 5,
            manual_visits: 5,
            start_date: null,
            end_date: null,
          },
        },
        msg: 'Success!',
      };

      const { apiGet } = require('@/lib/api');
      apiGet.mockResolvedValue(mockResponse);

      const result = await getCustomerStatistics('123');
      
      expect(apiGet).toHaveBeenCalledWith('api/customers/123/statistics', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });

    it('should fetch customer statistics with date range', async () => {
      const mockResponse = {
        extend: {
          statistics: {
            customer_id: '123',
            total_visits: 5,
            regular_visits: 4,
            loyalty_visits: 1,
            reward_visits: 0,
            complimentary_visits: 0,
            biometric_visits: 3,
            manual_visits: 2,
            start_date: '2025-01-01',
            end_date: '2025-01-31',
          },
        },
        msg: 'Success!',
      };

      const { apiGet } = require('@/lib/api');
      apiGet.mockResolvedValue(mockResponse);

      const result = await getCustomerStatistics('123', '2025-01-01', '2025-01-31');
      
      expect(apiGet).toHaveBeenCalledWith('api/customers/123/statistics?start_date=2025-01-01&end_date=2025-01-31', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('searchCustomers', () => {
    it('should search customers', async () => {
      const mockResponse = {
        code: 100,
        extend: {
          customers: [
            {
              customer_id: '123',
              first_name: 'John',
              last_name: 'Doe',
              full_name: 'John Doe',
              email: '<EMAIL>',
              customer_segment: 'regular',
              marketing_consent: false,
              status: 'active',
              registration_date: '2025-01-01',
              created_at: '2025-01-01T00:00:00Z',
              updated_at: '2025-01-01T00:00:00Z',
            },
          ],
          pagination: {
            has_next: false,
            has_prev: false,
            page: 1,
            pages: 1,
            per_page: 10,
            total_count: 1,
          },
        },
        msg: 'Success!',
      };

      const { apiGet } = require('@/lib/api');
      apiGet.mockResolvedValue(mockResponse);

      const result = await searchCustomers('John');
      
      expect(apiGet).toHaveBeenCalledWith('api/customers?search=John&page=1&per_page=10', {
        headers: {
          'Authorization': 'Bearer mock-token',
        },
      });
      expect(result).toEqual(mockResponse);
    });
  });
});
