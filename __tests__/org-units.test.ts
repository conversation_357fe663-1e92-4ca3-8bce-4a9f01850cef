/**
 * Test suite for organizational units functionality
 * Tests the API service layer and type definitions
 */

import { describe, it, expect, beforeEach, jest } from '@jest/globals';

// Mock the API module
jest.mock('@/lib/api', () => ({
  apiGet: jest.fn(),
  apiPost: jest.fn(),
  apiPut: jest.fn(),
  apiDelete: jest.fn(),
}));

// Mock the auth module
jest.mock('@/lib/auth', () => ({
  getAccessToken: jest.fn(() => 'mock-token'),
}));

import {
  createOrgUnit,
  getOrgUnits,
  getOrgUnitById,
  getDepartmentUnits,
  getDepartmentStructure,
  updateOrgUnit,
  deleteOrgUnit,
  getAvailableParentUnits,
} from '@/lib/org-units';

import {
  OrgUnit,
  CreateOrgUnitRequest,
  UpdateOrgUnitRequest,
  DepartmentStructure,
} from '@/types/org-unit';

describe('Organizational Units API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Type Definitions', () => {
    it('should have correct OrgUnit interface structure', () => {
      const mockOrgUnit: OrgUnit = {
        org_unit_id: 'unit-123',
        name: 'Test Unit',
        description: 'Test Description',
        department_id: 'dept-123',
        parent_id: null,
        level: 1,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        children: [],
      };

      expect(mockOrgUnit.org_unit_id).toBe('unit-123');
      expect(mockOrgUnit.name).toBe('Test Unit');
      expect(mockOrgUnit.level).toBe(1);
      expect(Array.isArray(mockOrgUnit.children)).toBe(true);
    });

    it('should have correct CreateOrgUnitRequest interface structure', () => {
      const mockRequest: CreateOrgUnitRequest = {
        name: 'New Unit',
        description: 'New Description',
        department_id: 'dept-123',
        parent_id: 'parent-123',
        level: 2,
      };

      expect(mockRequest.name).toBe('New Unit');
      expect(mockRequest.level).toBe(2);
    });

    it('should have correct DepartmentStructure interface structure', () => {
      const mockStructure: DepartmentStructure = {
        department_id: 'dept-123',
        department_name: 'Test Department',
        units: [],
        total_units: 0,
      };

      expect(mockStructure.department_id).toBe('dept-123');
      expect(Array.isArray(mockStructure.units)).toBe(true);
    });
  });

  describe('API Functions', () => {
    it('should call createOrgUnit with correct parameters', async () => {
      const { apiPost } = require('@/lib/api');
      const mockResponse = { success: true, org_unit: { org_unit_id: 'unit-123' } };
      apiPost.mockResolvedValue(mockResponse);

      const request: CreateOrgUnitRequest = {
        name: 'Test Unit',
        description: 'Test Description',
        department_id: 'dept-123',
        parent_id: null,
        level: 1,
      };

      const result = await createOrgUnit(request);

      expect(apiPost).toHaveBeenCalledWith('org_units', request, {
        headers: { 'Authorization': 'Bearer mock-token' }
      });
      expect(result).toEqual(mockResponse);
    });

    it('should call getDepartmentStructure with correct parameters', async () => {
      const { apiGet } = require('@/lib/api');
      const mockStructure: DepartmentStructure = {
        department_id: 'dept-123',
        department_name: 'Test Department',
        units: [],
        total_units: 0,
      };
      apiGet.mockResolvedValue(mockStructure);

      const result = await getDepartmentStructure('dept-123');

      expect(apiGet).toHaveBeenCalledWith('departments/dept-123/structure', {
        headers: { 'Authorization': 'Bearer mock-token' }
      });
      expect(result).toEqual(mockStructure);
    });

    it('should call deleteOrgUnit with correct parameters', async () => {
      const { apiDelete } = require('@/lib/api');
      const mockResponse = { success: true, message: 'Unit deleted' };
      apiDelete.mockResolvedValue(mockResponse);

      const result = await deleteOrgUnit('unit-123');

      expect(apiDelete).toHaveBeenCalledWith('org_units/unit-123', {
        headers: { 'Authorization': 'Bearer mock-token' }
      });
      expect(result).toEqual(mockResponse);
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', async () => {
      const { apiGet } = require('@/lib/api');
      const mockError = new Error('API Error');
      apiGet.mockRejectedValue(mockError);

      await expect(getDepartmentStructure('dept-123')).rejects.toThrow('API Error');
    });
  });
});
