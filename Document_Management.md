# Document Management API Documentation

Base URL: `{{APIUrl}}`

All endpoints require Bear<PERSON> token authentication unless otherwise specified.

---

## 📄 Documents

### 1. Upload Document
**Endpoint:** `POST /documents/upload`

**Request Type:** `multipart/form-data`

**Required Fields:**
- `file` (file) - The document file
- `document_name` (text) - Name of the document
- `document_category` (text) - Category (e.g., resume, contract, certificate, id_document)

**Optional Fields:**
- `document_description` (text) - Description of the document
- `expiry_date` (text) - Format: YYYY-MM-DD
- `employee_id` (text) - UUID of employee (for employee-specific documents)
- `folder_id` (text) - UUID of folder to upload to

**Success Response (200):**
```json
{
  "success": true,
  "message": "Document uploaded successfully",
  "document": {
    "document_id": "uuid",
    "document_name": "string",
    "document_description": "string",
    "document_category": "string",
    "original_filename": "string",
    "file_type": "string",
    "mime_type": "string",
    "file_size_bytes": 285048,
    "file_size_mb": 0.27,
    "storage_provider": "S3",
    "storage_url": "string",
    "download_url": "/api/documents/{id}/download",
    "expiry_date": "2025-10-29",
    "days_until_expiry": 46,
    "is_expired": false,
    "employee_id": "uuid or null",
    "employee_name": "string or null",
    "folder_id": "uuid or null",
    "folder_name": "string or null",
    "folder_path": "string or null",
    "access_level": "STANDARD",
    "is_confidential": false,
    "status": "ACTIVE",
    "uploaded_by": "uuid",
    "uploaded_at": "2025-09-13 23:19:34",
    "updated_at": "2025-09-13 23:19:34"
  }
}
```

---

### 2. Get All Documents
**Endpoint:** `GET /documents`

**Query Parameters:** (all optional)
- `page` (int) - Page number (default: 1)
- `per_page` (int) - Items per page (default: 20)

**Success Response (200):**
```json
{
  "success": true,
  "documents": [
    {
      "document_id": "uuid",
      "document_name": "string",
      "document_description": "string",
      "document_category": "string",
      "file_size_mb": 0.27,
      "expiry_date": "2025-10-29",
      "days_until_expiry": 46,
      "is_expired": false,
      "employee_name": "string or null",
      "folder_name": "string or null",
      "folder_path": "string or null",
      "uploaded_at": "2025-09-13 23:19:34"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total_count": 3,
    "total_pages": 1
  }
}
```

---

### 3. Get Document by ID
**Endpoint:** `GET /documents/{document_id}`

**Success Response (200):**
```json
{
  "success": true,
  "document": {
    // Same structure as upload response
  }
}
```

---

### 4. Download Document
**Endpoint:** `GET /documents/{document_id}/download`

**Success Response (200):**
```json
{
  "success": true,
  "filename": "remmittance rdb.pdf",
  "download_url": "https://signed-s3-url..."
}
```

---

### 5. Delete Document
**Endpoint:** `DELETE /documents/{document_id}`

**Success Response (200):**
```json
{
  "success": true,
  "message": "Document deleted successfully",
  "storage_deleted": true
}
```

---

### 6. Get Expiring Documents
**Endpoint:** `GET /documents/expiring`

**Query Parameters:**
- `days_ahead` (int) - Number of days to look ahead (e.g., 30, 90)

**Success Response (200):**
```json
{
  "success": true,
  "count": 3,
  "documents": [
    // Array of document objects
  ]
}
```

---

### 7. Get Storage Usage
**Endpoint:** `GET /documents/storage/usage`

**Success Response (200):**
```json
{
  "success": true,
  "company_id": "uuid",
  "usage": {
    "total_bytes": 552153,
    "total_mb": 0.53,
    "object_count": 2
  }
}
```

---

### 8. Get Document Statistics
**Endpoint:** `GET /documents/statistics`

**Query Parameters:** (optional)
- `date_from` (string) - Format: YYYY-MM-DD
- `date_to` (string) - Format: YYYY-MM-DD

**Success Response (200):**
```json
{
  "success": true,
  "filters": {
    "date_from": "2024-01-01 or null",
    "date_to": "2024-01-31 or null"
  },
  "statistics": {
    "total_documents": 6,
    "total_size_mb": 0.29,
    "avg_document_size_mb": 0.05,
    "expiring_soon": 5,
    "category_breakdown": {
      "certificate": 1,
      "contract": 3,
      "id_document": 1,
      "resume": 1
    }
  }
}
```

---

### 9. Move Document to Folder
**Endpoint:** `PUT /documents/{document_id}/move-to-folder`

**Request Body:**
```json
{
  "folder_id": "uuid"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Document moved to folder successfully",
  "document": {
    // Full document object with updated folder_id
  }
}
```

---

## 📁 Folders

### 1. Create Folder
**Endpoint:** `POST /folders`

**Request Body:**
```json
{
  "folder_name": "string", // Required
  "description": "string", // Optional
  "parent_folder_id": "uuid", // Optional (null for root folder)
  "color": "string", // Optional (e.g., "#4CAF50", "Yellow")
  "icon": "string", // Optional (e.g., "file-contract")
  "is_private": boolean, // Optional (default: false)
  "allowed_roles": ["hr", "admin", "employee"] // Optional
}
```

**Success Response (201):**
```json
{
  "success": true,
  "message": "Folder created successfully",
  "folder": {
    "folder_id": "uuid",
    "folder_name": "string",
    "description": "string",
    "parent_folder_id": "uuid or null",
    "full_path": "/Parent/Child",
    "depth": 0,
    "color": "string or null",
    "icon": "string or null",
    "is_private": false,
    "allowed_roles": ["hr", "admin", "employee"],
    "document_count": 0,
    "has_subfolders": false,
    "created_by": "uuid",
    "created_at": "2025-09-29 20:11:57",
    "updated_at": "2025-09-29 20:11:57"
  }
}
```

---

### 2. Get All Folders
**Endpoint:** `GET /folders`

**Query Parameters:** (all optional)
- `parent_folder_id` (string) - Filter by parent folder (use "root" for top-level)
- `is_private` (boolean) - Filter by privacy
- `search` (string) - Search by folder name
- `sort_by` (string) - Sort field: "name", "created_at", "updated_at" (default: "name")
- `sort_order` (string) - "asc" or "desc" (default: "asc")
- `page` (int) - Page number
- `per_page` (int) - Items per page

**Success Response (200):**
```json
{
  "success": true,
  "filters": {
    "parent_folder_id": "string or null",
    "is_private": "boolean or null",
    "search": "string or null",
    "sort_by": "name",
    "sort_order": "asc"
  },
  "folders": [
    {
      "folder_id": "uuid",
      "folder_name": "string",
      "description": "string",
      "full_path": "/Parent/Child",
      "depth": 0,
      "document_count": 0,
      "has_subfolders": false,
      "is_private": false,
      "color": "string or null",
      "icon": "string or null",
      "created_at": "timestamp",
      "updated_at": "timestamp"
    }
  ],
  "pagination": {
    "page": 1,
    "per_page": 20,
    "total_count": 3,
    "total_pages": 1,
    "has_next": false,
    "has_prev": false
  }
}
```

---

### 3. Get Single Folder
**Endpoint:** `GET /folders/{folder_id}`

**Success Response (200):**
```json
{
  "success": true,
  "folder": {
    // Full folder object
  }
}
```

---

### 4. Update Folder
**Endpoint:** `PUT /folders/{folder_id}`

**Request Body:** (all optional)
```json
{
  "folder_name": "string",
  "description": "string",
  "color": "string",
  "icon": "string"
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Folder updated successfully",
  "folder": {
    // Updated folder object
  }
}
```

---

### 5. Get Folder Tree (Hierarchical)
**Endpoint:** `GET /folders/tree`

**Query Parameters:**
- `include_documents` (boolean) - Include documents in response (default: false)

**Success Response (200):**
```json
{
  "success": true,
  "tree": [
    {
      "folder_id": "uuid",
      "folder_name": "string",
      "full_path": "/Parent",
      "depth": 0,
      "document_count": 0,
      "has_subfolders": true,
      "documents": [], // Only if include_documents=true
      "subfolders": [
        {
          "folder_id": "uuid",
          "folder_name": "string",
          "full_path": "/Parent/Child",
          "depth": 1,
          "subfolders": []
        }
      ]
    }
  ]
}
```

---

### 6. Get Folder Path (Breadcrumb)
**Endpoint:** `GET /folders/{folder_id}/path`

**Success Response (200):**
```json
{
  "success": true,
  "path": [
    {
      "folder_id": "uuid",
      "folder_name": "Parent"
    },
    {
      "folder_id": "uuid",
      "folder_name": "Child"
    }
  ]
}
```

---

### 7. Get Documents in Folder
**Endpoint:** `GET /folders/{folder_id}/documents`

**Query Parameters:**
- `include_subfolders` (boolean) - Include documents from subfolders (default: false)

**Success Response (200):**
```json
{
  "success": true,
  "count": 3,
  "documents": [
    // Array of document objects
  ]
}
```

---

### 8. Move Multiple Documents to Folder
**Endpoint:** `PUT /folders/{folder_id}/documents/move`

**Request Body:**
```json
{
  "document_ids": [
    "uuid1",
    "uuid2",
    "uuid3"
  ]
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Moved 2 documents to folder",
  "updated_count": 2
}
```

---

### 9. Get Folder Statistics
**Endpoint:** `GET /folders/{folder_id}/statistics`

**Success Response (200):**
```json
{
  "success": true,
  "statistics": {
    "folder_id": "uuid",
    "folder_name": "string",
    "full_path": "/Parent/Child",
    "depth": 0,
    "direct_document_count": 3,
    "direct_size_bytes": 98991,
    "direct_size_mb": 0.09,
    "subfolder_count": 1,
    "subfolder_document_count": 0,
    "subfolder_size_bytes": 0,
    "subfolder_size_mb": 0,
    "total_document_count": 3,
    "total_size_bytes": 98991,
    "total_size_mb": 0.09,
    "category_breakdown": {
      "contract": 2,
      "id_document": 1
    }
  }
}
```

---

## 🔑 Authentication

All requests require a Bearer token in the Authorization header:

```
Authorization: Bearer {{token}}
```

---

## 📝 Common Data Types

### Document Categories
- `resume`
- `contract`
- `certificate`
- `id_document`
- (custom categories allowed)

### File Types Supported
- PDF: `application/pdf`
- Word: `application/vnd.openxmlformats-officedocument.wordprocessingml.document`
- Images: `image/jpeg`, `image/png`, etc.

### Folder Allowed Roles
- `hr`
- `admin`
- `employee`

---

## ⚠️ Error Responses

All error responses follow this format:

```json
{
  "success": false,
  "error": "Error message description",
  "code": "ERROR_CODE" // Optional
}
```

Common HTTP status codes:
- `400` - Bad Request (invalid input)
- `401` - Unauthorized (missing/invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (resource doesn't exist)
- `500` - Internal Server Error