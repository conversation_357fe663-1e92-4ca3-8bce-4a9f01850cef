/**
 * Customer Loyalty API Service
 * Handles all loyalty-related API operations
 */

import { apiGet, apiPost } from "@/lib/api";
import { getAccessToken } from "@/lib/auth";
import {
  CustomerLoyaltyResponse,
  CustomerRewardsResponse,
  LoyaltyBalancesResponse,
  CustomerRedemptionsResponse,
  RedeemRewardRequest,
  RedeemRewardResponse,
  LoyaltyAnalyticsResponse,
  LoyaltyAnalyticsFilters,
  CustomerRedemptionsFilters,
  LoyaltyBalancesFilters,
} from "@/types/loyalty";

// ============================================================================
// CUSTOMER LOYALTY OPERATIONS
// ============================================================================

/**
 * Get customer loyalty summary
 */
export const getCustomerLoyalty = async (
  customerId: string
): Promise<CustomerLoyaltyResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiGet(`api/customers/${customerId}/loyalty`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerLoyaltyResponse;
  } catch (error) {
    console.error("Error fetching customer loyalty:", error);
    throw error;
  }
};

/**
 * Get customer rewards
 */
export const getCustomerRewards = async (
  customerId: string
): Promise<CustomerRewardsResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiGet(`api/customers/${customerId}/rewards`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerRewardsResponse;
  } catch (error) {
    console.error("Error fetching customer rewards:", error);
    throw error;
  }
};

/**
 * Redeem customer reward
 */
export const redeemCustomerReward = async (
  customerId: string,
  redeemData: RedeemRewardRequest
): Promise<RedeemRewardResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost(
      `api/customers/${customerId}/rewards/redeem`,
      redeemData,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response as RedeemRewardResponse;
  } catch (error) {
    console.error("Error redeeming customer reward:", error);
    throw error;
  }
};

/**
 * Get customer redemptions
 */
export const getCustomerRedemptions = async (
  customerId: string,
  filters?: CustomerRedemptionsFilters
): Promise<CustomerRedemptionsResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (filters) {
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);
    if (filters.reward_type) params.append("reward_type", filters.reward_type);
    if (filters.status) params.append("status", filters.status);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page)
      params.append("per_page", filters.per_page.toString());
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/customers/${customerId}/redemptions?${queryString}`
    : `api/customers/${customerId}/redemptions`;

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerRedemptionsResponse;
  } catch (error) {
    console.error("Error fetching customer redemptions:", error);
    throw error;
  }
};

// ============================================================================
// LOYALTY SYSTEM OPERATIONS
// ============================================================================

/**
 * Get loyalty balances
 */
export const getLoyaltyBalances = async (
  filters?: LoyaltyBalancesFilters
): Promise<LoyaltyBalancesResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (filters) {
    if (filters.customer_id) params.append("customer_id", filters.customer_id);
    if (filters.reward_type) params.append("reward_type", filters.reward_type);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page)
      params.append("per_page", filters.per_page.toString());
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/loyalty/balances?${queryString}`
    : "api/loyalty/balances";

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as LoyaltyBalancesResponse;
  } catch (error) {
    console.error("Error fetching loyalty balances:", error);
    throw error;
  }
};

/**
 * Get loyalty analytics
 */
export const getLoyaltyAnalytics = async (
  filters?: LoyaltyAnalyticsFilters
): Promise<LoyaltyAnalyticsResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (filters) {
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/loyalty/analytics?${queryString}`
    : "api/loyalty/analytics";

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as LoyaltyAnalyticsResponse;
  } catch (error) {
    console.error("Error fetching loyalty analytics:", error);
    throw error;
  }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get customer loyalty summary with error handling
 */
export const getCustomerLoyaltySafe = async (customerId: string) => {
  try {
    const response = await getCustomerLoyalty(customerId);
    return { data: response.extend.loyalty, error: null };
  } catch (error) {
    console.error("Error fetching customer loyalty:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Get customer rewards with error handling
 */
export const getCustomerRewardsSafe = async (customerId: string) => {
  try {
    const response = await getCustomerRewards(customerId);
    return { data: response.extend.rewards, error: null };
  } catch (error) {
    console.error("Error fetching customer rewards:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Redeem reward with error handling
 */
export const redeemRewardSafe = async (customerId: string, ruleId: string) => {
  try {
    const response = await redeemCustomerReward(customerId, {
      rule_id: ruleId,
    });
    return { data: response.extend.redemption, error: null };
  } catch (error) {
    console.error("Error redeeming reward:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Get loyalty analytics for current month
 */
export const getCurrentMonthLoyaltyAnalytics = async () => {
  const now = new Date();
  const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
  const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);

  const filters: LoyaltyAnalyticsFilters = {
    start_date: startOfMonth.toISOString().split("T")[0],
    end_date: endOfMonth.toISOString().split("T")[0],
  };

  try {
    const response = await getLoyaltyAnalytics(filters);
    return { data: response.extend.analytics, error: null };
  } catch (error) {
    console.error("Error fetching current month loyalty analytics:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Get loyalty analytics for date range
 */
export const getLoyaltyAnalyticsForRange = async (
  startDate: string,
  endDate: string
) => {
  const filters: LoyaltyAnalyticsFilters = {
    start_date: startDate,
    end_date: endDate,
  };

  try {
    const response = await getLoyaltyAnalytics(filters);
    return { data: response.extend.analytics, error: null };
  } catch (error) {
    console.error("Error fetching loyalty analytics for range:", error);
    return { data: null, error: error as Error };
  }
};
