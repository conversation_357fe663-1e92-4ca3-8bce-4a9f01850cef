/**
 * Folder Management API Service
 * Handles all API calls for folder management functionality
 */

import { apiGet, createApiUrl } from "./api";
import { getAccessToken } from "./auth";
import {
  Folder,
  FolderResponse,
  FolderListResponse,
  FolderTreeResponse,
  FolderPathResponse,
  FolderDocumentsResponse,
  FolderStatisticsResponse,
  MoveDocumentsResponse,
  CreateFolderRequest,
  UpdateFolderRequest,
  MoveFolderRequest,
  MoveMultipleDocumentsRequest,
  FolderFilters,
} from "@/types/document";

/**
 * Create a new folder
 */
export async function createFolder(
  data: CreateFolderRequest
): Promise<FolderResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const url = createApiUrl("api/folders");
  const response = await fetch(url, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to create folder");
  }

  return response.json();
}

/**
 * Get all folders with optional filters
 */
export async function getFolders(
  filters?: FolderFilters
): Promise<FolderListResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  let endpoint = "api/folders";
  const params = new URLSearchParams();

  if (filters) {
    if (filters.parent_folder_id) {
      params.append("parent_folder_id", filters.parent_folder_id);
    }
    if (filters.is_private !== undefined) {
      params.append("is_private", filters.is_private.toString());
    }
    if (filters.search) {
      params.append("search", filters.search);
    }
    if (filters.sort_by) {
      params.append("sort_by", filters.sort_by);
    }
    if (filters.sort_order) {
      params.append("sort_order", filters.sort_order);
    }
    if (filters.page) {
      params.append("page", filters.page.toString());
    }
    if (filters.per_page) {
      params.append("per_page", filters.per_page.toString());
    }
  }

  if (params.toString()) {
    endpoint += `?${params.toString()}`;
  }

  return await apiGet<FolderListResponse>(endpoint, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Get single folder by ID
 */
export async function getFolderById(folderId: string): Promise<FolderResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return await apiGet<FolderResponse>(`api/folders/${folderId}`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Update folder
 */
export async function updateFolder(
  folderId: string,
  data: UpdateFolderRequest
): Promise<FolderResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const url = createApiUrl(`api/folders/${folderId}`);
  const response = await fetch(url, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to update folder");
  }

  return response.json();
}

/**
 * Get folder tree (hierarchical structure)
 */
export async function getFolderTree(
  includeDocuments: boolean = false,
  companyId?: string
): Promise<FolderTreeResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (includeDocuments) {
    params.append("include_documents", "true");
  }
  if (companyId) {
    params.append("company_id", companyId);
  }

  const endpoint = `api/folders/tree${params.toString() ? `?${params.toString()}` : ""}`;

  return await apiGet<FolderTreeResponse>(endpoint, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Get folder path (breadcrumb)
 */
export async function getFolderPath(
  folderId: string
): Promise<FolderPathResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return await apiGet<FolderPathResponse>(`api/folders/${folderId}/path`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Get documents in folder
 */
export async function getFolderDocuments(
  folderId: string,
  includeSubfolders: boolean = false
): Promise<FolderDocumentsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (includeSubfolders) {
    params.append("include_subfolders", "true");
  }

  const endpoint = `api/folders/${folderId}/documents${params.toString() ? `?${params.toString()}` : ""}`;

  return await apiGet<FolderDocumentsResponse>(endpoint, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Move multiple documents to folder
 */
export async function moveDocumentsToFolder(
  folderId: string,
  data: MoveMultipleDocumentsRequest
): Promise<MoveDocumentsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const url = createApiUrl(`api/folders/${folderId}/documents/move`);
  const response = await fetch(url, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to move documents to folder");
  }

  return response.json();
}

/**
 * Get folder statistics
 */
export async function getFolderStatistics(
  folderId: string
): Promise<FolderStatisticsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  return await apiGet<FolderStatisticsResponse>(`api/folders/${folderId}/statistics`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });
}

/**
 * Delete folder
 */
export async function deleteFolder(
  folderId: string
): Promise<{ success: boolean; message: string }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const url = createApiUrl(`api/folders/${folderId}`);
  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(errorText || "Failed to delete folder");
  }

  return response.json();
}

/**
 * Get folder icon based on folder properties
 */
export function getFolderIcon(folder: Folder): string {
  if (folder.icon) {
    return folder.icon;
  }
  
  // Default icons based on folder properties
  if (folder.is_private) {
    return "lock";
  }
  
  return "folder";
}

/**
 * Get folder color class for UI
 */
export function getFolderColorClass(folder: Folder): string {
  if (folder.color) {
    // Convert hex colors to Tailwind classes
    const colorMap: Record<string, string> = {
      "#4CAF50": "bg-green-100 text-green-800 border-green-200",
      "#2196F3": "bg-blue-100 text-blue-800 border-blue-200",
      "#FF9800": "bg-orange-100 text-orange-800 border-orange-200",
      "#9C27B0": "bg-purple-100 text-purple-800 border-purple-200",
      "#F44336": "bg-red-100 text-red-800 border-red-200",
      "#607D8B": "bg-gray-100 text-gray-800 border-gray-200",
    };
    
    return colorMap[folder.color] || "bg-blue-100 text-blue-800 border-blue-200";
  }
  
  return "bg-blue-100 text-blue-800 border-blue-200";
}
