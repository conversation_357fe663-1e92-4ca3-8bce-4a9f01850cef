/**
 * Device Sync Management API Functions
 * API service functions for device sync operations in HR dashboard
 */

import { apiGet, apiPost } from './api';
import { getAccessToken } from './auth';
import {
  CustomerSyncStatusResponse,
  EmployeeSyncStatusResponse,
  SyncFailuresResponse,
  RetrySyncRequest,
  RetrySyncResponse,
  PersonType
} from '@/types/device-sync';

/**
 * Get sync status for a specific customer
 * @param customerId The customer ID to get sync status for
 * @returns Customer sync status response
 */
export async function getCustomerSyncStatus(customerId: string): Promise<CustomerSyncStatusResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CustomerSyncStatusResponse>(
    `api/hr/sync-status/customer/${customerId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

/**
 * Get sync status for a specific employee
 * @param employeeId The employee ID to get sync status for
 * @returns Employee sync status response
 */
export async function getEmployeeSyncStatus(employeeId: string): Promise<EmployeeSyncStatusResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<EmployeeSyncStatusResponse>(
    `api/hr/sync-status/employee/${employeeId}`,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

/**
 * Get all sync failures for the company
 * @returns Sync failures response
 */
export async function getSyncFailures(): Promise<SyncFailuresResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<SyncFailuresResponse>(
    'api/hr/sync-failures',
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

/**
 * Retry failed sync for a person (customer or employee)
 * @param personId The person ID to retry sync for
 * @param personType The type of person ('customer' or 'employee')
 * @returns Retry sync response
 */
export async function retrySync(personId: string, personType: PersonType): Promise<RetrySyncResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const requestData: RetrySyncRequest = {
    person_type: personType,
    person_id: personId
  };

  const response = await apiPost<RetrySyncResponse>(
    'api/hr/retry-sync',
    requestData,
    {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    }
  );

  return response;
}

/**
 * Get sync status for a person (automatically determines if customer or employee)
 * @param personId The person ID to get sync status for
 * @param personType The type of person ('customer' or 'employee')
 * @returns Sync status response
 */
export async function getPersonSyncStatus(
  personId: string, 
  personType: PersonType
): Promise<CustomerSyncStatusResponse | EmployeeSyncStatusResponse> {
  if (personType === 'customer') {
    return getCustomerSyncStatus(personId);
  } else {
    return getEmployeeSyncStatus(personId);
  }
}

/**
 * Helper function to format sync status for display
 * @param syncStatus The sync status response
 * @returns Formatted sync status information
 */
export function formatSyncStatus(syncStatus: CustomerSyncStatusResponse | EmployeeSyncStatusResponse) {
  const { sync_summary, device_sync_details } = syncStatus;
  
  return {
    totalDevices: sync_summary.total_devices,
    syncedDevices: sync_summary.synced_devices,
    pendingDevices: sync_summary.pending_devices,
    failedDevices: sync_summary.failed_devices,
    syncPercentage: sync_summary.total_devices > 0 
      ? Math.round((sync_summary.synced_devices / sync_summary.total_devices) * 100)
      : 0,
    hasFailures: sync_summary.failed_devices > 0,
    hasPending: sync_summary.pending_devices > 0,
    devices: device_sync_details.map(device => ({
      serialNumber: device.device_sn,
      connected: device.device_connected,
      commandCount: device.commands.length,
      lastCommand: device.commands.length > 0 
        ? device.commands[device.commands.length - 1]
        : null,
      hasFailures: device.commands.some(cmd => cmd.status === 'failed' || cmd.status === 'error'),
      hasPending: device.commands.some(cmd => cmd.status === 'pending')
    }))
  };
}

/**
 * Helper function to get sync status color based on status
 * @param status The sync status
 * @returns CSS color class
 */
export function getSyncStatusColor(status: string): string {
  switch (status) {
    case 'completed':
      return 'text-green-600 bg-green-100';
    case 'pending':
      return 'text-yellow-600 bg-yellow-100';
    case 'failed':
    case 'error':
      return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
}

/**
 * Helper function to get sync status icon
 * @param status The sync status
 * @returns Icon name or emoji
 */
export function getSyncStatusIcon(status: string): string {
  switch (status) {
    case 'completed':
      return '✅';
    case 'pending':
      return '⏳';
    case 'failed':
    case 'error':
      return '❌';
    default:
      return '❓';
  }
}

/**
 * Helper function to format command type for display
 * @param commandType The command type
 * @returns Formatted command type
 */
export function formatCommandType(commandType: string): string {
  switch (commandType) {
    case 'setusername':
      return 'Set Username';
    case 'deleteuser':
      return 'Delete User';
    case 'adduser':
      return 'Add User';
    case 'sync':
      return 'Sync';
    case 'init':
      return 'Initialize';
    default:
      return commandType.charAt(0).toUpperCase() + commandType.slice(1);
  }
}

/**
 * Helper function to check if a sync can be retried
 * @param syncStatus The sync status response
 * @returns Whether retry is possible
 */
export function canRetrySync(syncStatus: CustomerSyncStatusResponse | EmployeeSyncStatusResponse): boolean {
  return syncStatus.device_sync_details.some(device =>
    device.commands.some(command => 
      (command.status === 'failed' || command.status === 'error') && command.can_retry
    )
  );
}
