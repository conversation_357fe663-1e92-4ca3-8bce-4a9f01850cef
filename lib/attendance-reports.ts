/**
 * Attendance Summary Reports API utilities
 * Handles PDF and Excel report generation for attendance summaries
 */

import { createApiUrl } from "./api";
import { getAccessToken } from "./auth";

export interface AttendanceReportParams {
  company_id: string;
  start_date?: string; // Format: YYYY-MM-DD
  end_date?: string; // Format: YYYY-MM-DD
}

/**
 * Download attendance summary report as PDF
 * @param params Report parameters including company_id and optional date range
 * @returns Promise that resolves when download is complete
 */
export async function downloadAttendanceSummaryPDF(
  params: AttendanceReportParams
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    // Build query parameters
    const queryParams = new URLSearchParams({
      company_id: params.company_id,
    });

    // Add optional date parameters
    if (params.start_date) {
      queryParams.append("start_date", params.start_date);
    }
    if (params.end_date) {
      queryParams.append("end_date", params.end_date);
    }

    // Create the API URL
    const url = createApiUrl(
      `api/attendance/summary/pdf?${queryParams.toString()}`
    );

    // Make the request
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to download PDF report: ${response.status} ${response.statusText}`
      );
    }

    // Get the file blob
    const blob = await response.blob();

    // Create download link
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;

    // Generate filename with date range if provided, if not use current date
    const dateStr = new Date().toISOString().split("T")[0];
    let filename;

    if (params.start_date && params.end_date) {
      filename = `attendance-summary-${params.start_date}-to-${params.end_date}.pdf`;
    } else if (params.start_date) {
      filename = `attendance-summary-from-${params.start_date}.pdf`;
    } else if (params.end_date) {
      filename = `attendance-summary-until-${params.end_date}.pdf`;
    } else {
      filename = `attendance-summary-${dateStr}.pdf`;
    }

    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error) {
    console.error("Error downloading PDF report:", error);
    throw error;
  }
}

/**
 * Download attendance summary report as Excel
 * @param params Report parameters including company_id and optional date range
 * @returns Promise that resolves when download is complete
 */
export async function downloadAttendanceSummaryExcel(
  params: AttendanceReportParams
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    // Build query parameters
    const queryParams = new URLSearchParams({
      company_id: params.company_id,
    });

    // Add optional date parameters
    if (params.start_date) {
      queryParams.append("start_date", params.start_date);
    }
    if (params.end_date) {
      queryParams.append("end_date", params.end_date);
    }

    // Create the API URL
    const url = createApiUrl(
      `api/attendance/summary/excel?${queryParams.toString()}`
    );

    // Make the request
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to download Excel report: ${response.status} ${response.statusText}`
      );
    }

    // Get the file blob
    const blob = await response.blob();

    // Create download link
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;

    // Generate filename with date range if provided, if not use current date
    const dateStr = new Date().toISOString().split("T")[0];
    let filename;

    if (params.start_date && params.end_date) {
      filename = `attendance-summary-${params.start_date}-to-${params.end_date}.xlsx`;
    } else if (params.start_date) {
      filename = `attendance-summary-from-${params.start_date}.xlsx`;
    } else if (params.end_date) {
      filename = `attendance-summary-until-${params.end_date}.xlsx`;
    } else {
      filename = `attendance-summary-${dateStr}.xlsx`;
    }

    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error) {
    console.error("Error downloading Excel report:", error);
    throw error;
  }
}

/**
 * Format date for API (YYYY-MM-DD format)
 * @param date Date object or null
 * @returns Formatted date string or undefined
 */
export function formatDateForAPI(date: Date | null): string | undefined {
  if (!date) return undefined;
  return date.toISOString().split("T")[0];
}

/**
 * Validate date range
 * @param startDate Start date
 * @param endDate End date
 * @returns Validation result with error message if invalid
 */
export function validateDateRange(
  startDate: Date | null,
  endDate: Date | null
): { isValid: boolean; error?: string } {
  if (startDate && endDate && startDate > endDate) {
    return { isValid: false, error: "Start date cannot be after end date" };
  }

  const today = new Date();
  today.setHours(23, 59, 59, 999); // Set to end of today

  if (startDate && startDate > today) {
    return { isValid: false, error: "Start date cannot be in the future" };
  }

  if (endDate && endDate > today) {
    return { isValid: false, error: "End date cannot be in the future" };
  }

  return { isValid: true };
}
