/**
 * Service API Service
 * Handles all API calls for service management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  Service,
  CreateServiceRequest,
  UpdateServiceRequest,
  ServiceResponse,
  ServicesListResponse,
  SingleServiceResponse,
  DeleteServiceResponse,
  ServiceFilters,
} from '@/types/service';

/**
 * Create a new service
 */
export const createService = async (
  serviceData: CreateServiceRequest
): Promise<ServiceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPost<ServiceResponse>('api/services', serviceData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Get service by ID
 */
export const getServiceById = async (serviceId: string): Promise<SingleServiceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<SingleServiceResponse>(`api/services/${serviceId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get all services
 */
export const getServices = async (filters?: ServiceFilters): Promise<ServicesListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Build query parameters
  const params = new URLSearchParams();
  
  if (filters) {
    if (filters.is_active !== undefined) {
      params.append('is_active', filters.is_active.toString());
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.page) {
      params.append('page', filters.page.toString());
    }
    if (filters.limit) {
      params.append('limit', filters.limit.toString());
    }
  }

  const queryString = params.toString();
  const url = queryString ? `api/services?${queryString}` : 'api/services';

  return apiGet<ServicesListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Update service
 */
export const updateService = async (
  serviceId: string,
  serviceData: UpdateServiceRequest
): Promise<ServiceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPatch<ServiceResponse>(`api/services/${serviceId}`, serviceData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Delete (deactivate) service
 */
export const deleteService = async (serviceId: string): Promise<DeleteServiceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiDelete<DeleteServiceResponse>(`api/services/${serviceId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get active services only
 */
export const getActiveServices = async (): Promise<ServicesListResponse> => {
  return getServices({ is_active: true });
};

/**
 * Get inactive services only
 */
export const getInactiveServices = async (): Promise<ServicesListResponse> => {
  return getServices({ is_active: false });
};

/**
 * Search services by name or description
 */
export const searchServices = async (
  searchTerm: string,
  filters?: Omit<ServiceFilters, 'search'>
): Promise<ServicesListResponse> => {
  return getServices({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get services with pagination
 */
export const getServicesPaginated = async (
  page: number = 1,
  limit: number = 10,
  filters?: Omit<ServiceFilters, 'page' | 'limit'>
): Promise<ServicesListResponse> => {
  return getServices({
    ...filters,
    page,
    limit,
  });
};

/**
 * Activate service (helper function)
 */
export const activateService = async (serviceId: string): Promise<ServiceResponse> => {
  // Note: The API doesn't have a specific activate endpoint, 
  // so we use the update endpoint to set is_active to true
  // This is a logical assumption based on typical REST patterns
  return updateService(serviceId, { name: undefined, description: undefined });
};

/**
 * Deactivate service (helper function)
 */
export const deactivateService = async (serviceId: string): Promise<DeleteServiceResponse> => {
  // Use the delete endpoint which deactivates the service
  return deleteService(serviceId);
};

/**
 * Duplicate service (create a copy)
 */
export const duplicateService = async (
  serviceId: string,
  newName?: string
): Promise<ServiceResponse> => {
  // First get the original service
  const originalResponse = await getServiceById(serviceId);
  const originalService = originalResponse.extend.service;

  // Create a new service based on the original
  const newServiceData: CreateServiceRequest = {
    name: newName || `${originalService.name} (Copy)`,
    description: originalService.description || undefined,
  };

  return createService(newServiceData);
};

/**
 * Get service statistics (helper function)
 */
export const getServiceStatistics = async (): Promise<{
  total: number;
  active: number;
  inactive: number;
}> => {
  const [allServices, activeServices, inactiveServices] = await Promise.all([
    getServices(),
    getActiveServices(),
    getInactiveServices(),
  ]);

  return {
    total: allServices.extend.count,
    active: activeServices.extend.count,
    inactive: inactiveServices.extend.count,
  };
};
