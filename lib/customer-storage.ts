/**
 * Customer Storage API Service
 * Handles all customer storage-related API operations
 */

import { apiGet, apiPost, apiPut, apiDelete } from "@/lib/api";
import { getAccessToken } from "@/lib/auth";
import {
  CustomerStoragesListResponse,
  SingleCustomerStorageResponse,
  CustomerStorageResponse,
  DeleteCustomerStorageResponse,
  RetrieveStorageResponse,
  AbandonStorageResponse,
  CreateCustomerStorageRequest,
  UpdateCustomerStorageRequest,
  RetrieveCustomerStorageRequest,
  AbandonCustomerStorageRequest,
  CustomerStorageFilters,
} from "@/types/customer-storage";

const BASE_URL = "api/customer-storages";

// ============================================================================
// CUSTOMER STORAGE CRUD OPERATIONS
// ============================================================================

/**
 * Get all customer storages with optional filters
 */
export const getCustomerStorages = async (
  filters?: CustomerStorageFilters
): Promise<CustomerStoragesListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const params = new URLSearchParams();
  if (filters) {
    if (filters.customer_id) params.append("customer_id", filters.customer_id);
    if (filters.location_id) params.append("location_id", filters.location_id);
    if (filters.visit_id) params.append("visit_id", filters.visit_id);
    if (filters.status) params.append("status", filters.status);
    if (filters.search) params.append("search", filters.search);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
  }

  const queryString = params.toString();
  const endpoint = queryString ? `${BASE_URL}?${queryString}` : BASE_URL;

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerStoragesListResponse;
  } catch (error) {
    console.error("Error fetching customer storages:", error);
    throw error;
  }
};

/**
 * Get a single customer storage by ID
 */
export const getCustomerStorage = async (
  storageId: string
): Promise<SingleCustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiGet(`${BASE_URL}/${storageId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as SingleCustomerStorageResponse;
  } catch (error) {
    console.error("Error fetching customer storage:", error);
    throw error;
  }
};

/**
 * Create a new customer storage record
 */
export const createCustomerStorage = async (
  data: CreateCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost(BASE_URL, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response as CustomerStorageResponse;
  } catch (error) {
    console.error("Error creating customer storage:", error);
    throw error;
  }
};

/**
 * Update an existing customer storage record
 */
export const updateCustomerStorage = async (
  storageId: string,
  data: UpdateCustomerStorageRequest
): Promise<CustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPut(`${BASE_URL}/${storageId}`, data, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response as CustomerStorageResponse;
  } catch (error) {
    console.error("Error updating customer storage:", error);
    throw error;
  }
};

/**
 * Delete a customer storage record
 */
export const deleteCustomerStorage = async (
  storageId: string
): Promise<DeleteCustomerStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiDelete(`${BASE_URL}/${storageId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as DeleteCustomerStorageResponse;
  } catch (error) {
    console.error("Error deleting customer storage:", error);
    throw error;
  }
};

// ============================================================================
// STORAGE STATUS OPERATIONS
// ============================================================================

/**
 * Mark customer storage as retrieved
 */
export const retrieveCustomerStorage = async (
  storageId: string,
  data?: RetrieveCustomerStorageRequest
): Promise<RetrieveStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost(
      `${BASE_URL}/${storageId}/retrieve`,
      data || {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response as RetrieveStorageResponse;
  } catch (error) {
    console.error("Error retrieving customer storage:", error);
    throw error;
  }
};

/**
 * Mark customer storage as abandoned
 */
export const abandonCustomerStorage = async (
  storageId: string,
  data?: AbandonCustomerStorageRequest
): Promise<AbandonStorageResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost(
      `${BASE_URL}/${storageId}/abandon`,
      data || {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response as AbandonStorageResponse;
  } catch (error) {
    console.error("Error abandoning customer storage:", error);
    throw error;
  }
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get customer storages with error handling
 */
export const getCustomerStoragesSafe = async (
  filters?: CustomerStorageFilters
) => {
  try {
    const response = await getCustomerStorages(filters);
    return { data: response.extend.customer_storages, error: null };
  } catch (error) {
    console.error("Error fetching customer storages:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Get customer storage by ID with error handling
 */
export const getCustomerStorageSafe = async (storageId: string) => {
  try {
    const response = await getCustomerStorage(storageId);
    return { data: response.extend.customer_storage, error: null };
  } catch (error) {
    console.error("Error fetching customer storage:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Create customer storage with error handling
 */
export const createCustomerStorageSafe = async (
  data: CreateCustomerStorageRequest
) => {
  try {
    const response = await createCustomerStorage(data);
    return { data: response.extend.customer_storage, error: null };
  } catch (error) {
    console.error("Error creating customer storage:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Update customer storage with error handling
 */
export const updateCustomerStorageSafe = async (
  storageId: string,
  data: UpdateCustomerStorageRequest
) => {
  try {
    const response = await updateCustomerStorage(storageId, data);
    return { data: response.extend.customer_storage, error: null };
  } catch (error) {
    console.error("Error updating customer storage:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Retrieve customer storage with error handling
 */
export const retrieveCustomerStorageSafe = async (
  storageId: string,
  data?: RetrieveCustomerStorageRequest
) => {
  try {
    const response = await retrieveCustomerStorage(storageId, data);
    return { data: response.extend.message, error: null };
  } catch (error) {
    console.error("Error retrieving customer storage:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Abandon customer storage with error handling
 */
export const abandonCustomerStorageSafe = async (
  storageId: string,
  data?: AbandonCustomerStorageRequest
) => {
  try {
    const response = await abandonCustomerStorage(storageId, data);
    return { data: response.extend.message, error: null };
  } catch (error) {
    console.error("Error abandoning customer storage:", error);
    return { data: null, error: error as Error };
  }
};

/**
 * Get active customer storages (status = 'stored')
 */
export const getActiveCustomerStorages = async () => {
  return getCustomerStorages({ status: "stored" });
};

/**
 * Get retrieved customer storages
 */
export const getRetrievedCustomerStorages = async () => {
  return getCustomerStorages({ status: "retrieved" });
};

/**
 * Get abandoned customer storages
 */
export const getAbandonedCustomerStorages = async () => {
  return getCustomerStorages({ status: "abandoned" });
};

/**
 * Get customer storages by customer ID
 */
export const getCustomerStoragesByCustomer = async (customerId: string) => {
  return getCustomerStorages({ customer_id: customerId });
};

/**
 * Get customer storages by location ID
 */
export const getCustomerStoragesByLocation = async (locationId: string) => {
  return getCustomerStorages({ location_id: locationId });
};

/**
 * Get customer storages by visit ID
 */
export const getCustomerStoragesByVisit = async (visitId: string) => {
  return getCustomerStorages({ visit_id: visitId });
};
