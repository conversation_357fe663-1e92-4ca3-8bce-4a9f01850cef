/**
 * Customer API Service
 * Handles all API calls for customer management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CustomerResponse,
  CustomersListResponse,
  CustomerStatisticsResponse,
  CustomerFilters,
} from '@/types/customer';

/**
 * Create a new customer
 * @param customerData The customer data to create
 * @returns Promise with the created customer response
 */
export async function createCustomer(customerData: CreateCustomerRequest): Promise<CustomerResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPost<CustomerResponse>('api/customers', customerData, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error creating customer:', error);
    throw error;
  }
}

/**
 * Get a customer by ID
 * @param customerId The customer ID
 * @returns Promise with the customer data
 */
export async function getCustomerById(customerId: string): Promise<CustomerResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiGet<CustomerResponse>(`api/customers/${customerId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error fetching customer:', error);
    throw error;
  }
}

/**
 * Update a customer
 * @param customerId The customer ID
 * @param customerData The customer data to update
 * @returns Promise with the updated customer response
 */
export async function updateCustomer(
  customerId: string,
  customerData: UpdateCustomerRequest
): Promise<CustomerResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPatch<CustomerResponse>(`api/customers/${customerId}`, customerData, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error updating customer:', error);
    throw error;
  }
}

/**
 * Delete a customer
 * @param customerId The customer ID
 * @returns Promise with the deletion response
 */
export async function deleteCustomer(customerId: string): Promise<{ msg: string; success: boolean }> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiDelete<{ msg: string; success: boolean }>(`api/customers/${customerId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error deleting customer:', error);
    throw error;
  }
}

/**
 * Get customers list with pagination and filtering
 * @param filters The filters to apply (page, per_page, segment, status, search)
 * @returns Promise with the customers list response
 */
export async function getCustomers(filters: CustomerFilters = {}): Promise<CustomersListResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    
    if (filters.page) {
      queryParams.append('page', filters.page.toString());
    }
    
    if (filters.per_page) {
      queryParams.append('per_page', filters.per_page.toString());
    }
    
    if (filters.segment) {
      queryParams.append('segment', filters.segment);
    }
    
    if (filters.status) {
      queryParams.append('status', filters.status);
    }
    
    if (filters.search) {
      queryParams.append('search', filters.search);
    }

    const queryString = queryParams.toString();
    const endpoint = queryString ? `api/customers?${queryString}` : 'api/customers';

    const response = await apiGet<CustomersListResponse>(endpoint, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error fetching customers:', error);
    throw error;
  }
}

/**
 * Get customer statistics
 * @param customerId The customer ID
 * @param startDate Optional start date for statistics (ISO string)
 * @param endDate Optional end date for statistics (ISO string)
 * @returns Promise with the customer statistics response
 */
export async function getCustomerStatistics(
  customerId: string,
  startDate?: string,
  endDate?: string
): Promise<CustomerStatisticsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    // Build query parameters for date range if provided
    const queryParams = new URLSearchParams();
    
    if (startDate) {
      queryParams.append('start_date', startDate);
    }
    
    if (endDate) {
      queryParams.append('end_date', endDate);
    }

    const queryString = queryParams.toString();
    const endpoint = queryString 
      ? `api/customers/${customerId}/statistics?${queryString}`
      : `api/customers/${customerId}/statistics`;

    const response = await apiGet<CustomerStatisticsResponse>(endpoint, {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });

    return response;
  } catch (error) {
    console.error('Error fetching customer statistics:', error);
    throw error;
  }
}

/**
 * Search customers by name, email, phone, or membership number
 * @param searchTerm The search term
 * @param page The page number (default: 1)
 * @param perPage The number of items per page (default: 10)
 * @returns Promise with the search results
 */
export async function searchCustomers(
  searchTerm: string,
  page: number = 1,
  perPage: number = 10
): Promise<CustomersListResponse> {
  return getCustomers({
    search: searchTerm,
    page,
    per_page: perPage,
  });
}

/**
 * Get customers by segment
 * @param segment The customer segment
 * @param page The page number (default: 1)
 * @param perPage The number of items per page (default: 10)
 * @returns Promise with the filtered customers
 */
export async function getCustomersBySegment(
  segment: string,
  page: number = 1,
  perPage: number = 10
): Promise<CustomersListResponse> {
  return getCustomers({
    segment: segment as any,
    page,
    per_page: perPage,
  });
}

/**
 * Get all customers (for dropdowns, etc.) - fetches all pages
 * @returns Promise with all customers
 */
export async function getAllCustomers(): Promise<Customer[]> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    let allCustomers: Customer[] = [];
    let currentPage = 1;
    let hasNextPage = true;

    // Fetch all pages of customers
    while (hasNextPage) {
      const response = await getCustomers({
        page: currentPage,
        per_page: 100, // Use larger page size for efficiency
      });

      if (response.extend && response.extend.customers) {
        allCustomers = [...allCustomers, ...response.extend.customers];

        // Check if there are more pages
        if (response.extend.pagination) {
          hasNextPage = response.extend.pagination.has_next;
          currentPage++;
        } else {
          hasNextPage = false;
        }
      } else {
        hasNextPage = false;
      }
    }

    return allCustomers;
  } catch (error) {
    console.error('Error fetching all customers:', error);
    throw error;
  }
}
