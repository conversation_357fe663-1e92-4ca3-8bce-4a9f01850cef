/**
 * API Integration Service Functions
 * Handles all API calls for API client management functionality
 */

import { apiGet, apiPost, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  CreateApiClientRequest,
  CreateApiClientResponse,
  ResetSecretResponse,
  GetApiClientsResponse,
  DeleteApiClientResponse,
} from '@/types/api-integration';

/**
 * Create a new API client
 * @param data The API client creation data
 * @returns The created API client with credentials
 */
export async function createApiClient(data: CreateApiClientRequest): Promise<CreateApiClientResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPost<CreateApiClientResponse>('api/auth/api_clients', data, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response;
  } catch (error: any) {
    throw new Error(`Failed to create API client: ${error.message}`);
  }
}

/**
 * Reset the secret for an existing API client
 * @param clientId The client ID to reset the secret for
 * @returns The reset response with new secret
 */
export async function resetClientSecret(clientId: string): Promise<ResetSecretResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiPost<ResetSecretResponse>(
      `api/auth/api_clients/${clientId}/reset-secret`,
      {}, // Empty body for reset endpoint
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );

    return response;
  } catch (error: any) {
    throw new Error(`Failed to reset client secret: ${error.message}`);
  }
}

/**
 * Get all API clients for the current user
 * @returns List of API clients
 */
export async function getAllApiClients(): Promise<GetApiClientsResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiGet<GetApiClientsResponse>('api/auth/api_clients', {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response;
  } catch (error: any) {
    throw new Error(`Failed to fetch API clients: ${error.message}`);
  }
}

/**
 * Delete an API client
 * @param clientId The client ID to delete
 * @returns The deletion response
 */
export async function deleteApiClient(clientId: string): Promise<DeleteApiClientResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  try {
    const response = await apiDelete<DeleteApiClientResponse>(`api/auth/api_clients/${clientId}`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    return response;
  } catch (error: any) {
    throw new Error(`Failed to delete API client: ${error.message}`);
  }
}

/**
 * Format date for display in the UI
 * @param dateString The date string from the API
 * @returns Formatted date string
 */
export function formatApiClientDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (error) {
    return dateString; // Return original string if parsing fails
  }
}

/**
 * Extract the app name from the full name returned by the API
 * The API returns names like "developer (Created by user: uuid)"
 * @param fullName The full name from the API
 * @returns The clean app name
 */
export function extractAppName(fullName: string): string {
  const match = fullName.match(/^(.+?)\s*\(Created by user:/);
  return match ? match[1].trim() : fullName;
}

/**
 * Validate API client name
 * @param name The name to validate
 * @returns Validation result
 */
export function validateApiClientName(name: string): { isValid: boolean; error?: string } {
  if (!name || name.trim().length === 0) {
    return { isValid: false, error: 'App name is required' };
  }

  if (name.trim().length < 2) {
    return { isValid: false, error: 'App name must be at least 2 characters long' };
  }

  if (name.trim().length > 100) {
    return { isValid: false, error: 'App name must be less than 100 characters' };
  }

  // Check for invalid characters (basic validation)
  const invalidChars = /[<>\"'&]/;
  if (invalidChars.test(name)) {
    return { isValid: false, error: 'App name contains invalid characters' };
  }

  return { isValid: true };
}

/**
 * Copy text to clipboard with error handling
 * @param text The text to copy
 * @returns Promise that resolves when copy is successful
 */
export async function copyToClipboard(text: string): Promise<void> {
  try {
    if (navigator.clipboard && window.isSecureContext) {
      // Use the modern clipboard API if available
      await navigator.clipboard.writeText(text);
    } else {
      // Fallback for older browsers or non-secure contexts
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      
      const successful = document.execCommand('copy');
      document.body.removeChild(textArea);
      
      if (!successful) {
        throw new Error('Copy command failed');
      }
    }
  } catch (error) {
    throw new Error('Failed to copy to clipboard. Please copy manually.');
  }
}
