/**
 * Timesheet Reports API utilities
 * Handles PDF and Excel report generation for timesheet matrix reports
 */

import { createApiUrl } from "./api";
import { getAccessToken } from "./auth";

export type PeriodType = "monthly" | "weekly" | "bi-weekly";
export type ReportFormat = "pdf" | "excel";

export interface TimesheetReportParams {
  period_type: PeriodType;
  format: ReportFormat;
  company_id?: string;
  employee_ids?: string[]; // Array of employee IDs
  department_id?: string;
  month?: number; // 1-12
  year?: number;
  date?: string; // Format: YYYY-MM-DD (for specific date periods)
}

/**
 * Download timesheet matrix report
 * @param params Report parameters including period type, format, and optional filters
 * @returns Promise that resolves when download is complete
 */
export async function downloadTimesheetReport(
  params: TimesheetReportParams
): Promise<void> {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    // Build query parameters
    const queryParams = new URLSearchParams({
      period_type: params.period_type,
      format: params.format,
    });

    // Add optional parameters
    if (params.company_id) {
      queryParams.append("company_id", params.company_id);
    }
    
    if (params.employee_ids && params.employee_ids.length > 0) {
      queryParams.append("employee_ids", params.employee_ids.join(","));
    }
    
    if (params.department_id) {
      queryParams.append("department_id", params.department_id);
    }
    
    if (params.month) {
      queryParams.append("month", params.month.toString());
    }
    
    if (params.year) {
      queryParams.append("year", params.year.toString());
    }
    
    if (params.date) {
      queryParams.append("date", params.date);
    }

    // Create the API URL
    const url = createApiUrl(
      `api/attendance/timesheets/matrix?${queryParams.toString()}`
    );

    // Make the request
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(
        `Failed to download timesheet report: ${response.status} ${response.statusText}`
      );
    }

    // Get the file blob
    const blob = await response.blob();

    // Create download link
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;

    // Generate filename based on parameters
    const filename = generateTimesheetFilename(params);
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Clean up
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error) {
    console.error("Error downloading timesheet report:", error);
    throw error;
  }
}

/**
 * Generate appropriate filename for timesheet report
 * @param params Report parameters
 * @returns Generated filename
 */
function generateTimesheetFilename(params: TimesheetReportParams): string {
  const dateStr = new Date().toISOString().split("T")[0];
  const extension = params.format === "pdf" ? "pdf" : "xlsx";
  
  let filename = `timesheet-${params.period_type}`;
  
  // Add specific date/period info
  if (params.month && params.year) {
    filename += `-${params.year}-${params.month.toString().padStart(2, "0")}`;
  } else if (params.year) {
    filename += `-${params.year}`;
  } else if (params.date) {
    filename += `-${params.date}`;
  } else {
    filename += `-${dateStr}`;
  }
  
  // Add filter info
  if (params.employee_ids && params.employee_ids.length > 0) {
    filename += `-employees`;
  } else if (params.department_id) {
    filename += `-department`;
  }
  
  return `${filename}.${extension}`;
}

/**
 * Download monthly timesheet report (current month)
 * @param format Report format (pdf or excel)
 * @param companyId Optional company ID
 * @returns Promise that resolves when download is complete
 */
export async function downloadMonthlyTimesheet(
  format: ReportFormat = "pdf",
  companyId?: string
): Promise<void> {
  return downloadTimesheetReport({
    period_type: "monthly",
    format,
    company_id: companyId,
  });
}

/**
 * Download monthly timesheet report for specific month/year
 * @param month Month (1-12)
 * @param year Year
 * @param format Report format (pdf or excel)
 * @param companyId Optional company ID
 * @returns Promise that resolves when download is complete
 */
export async function downloadMonthlyTimesheetForPeriod(
  month: number,
  year: number,
  format: ReportFormat = "pdf",
  companyId?: string
): Promise<void> {
  return downloadTimesheetReport({
    period_type: "monthly",
    format,
    month,
    year,
    company_id: companyId,
  });
}

/**
 * Download timesheet report for specific employees
 * @param employeeIds Array of employee IDs
 * @param periodType Period type
 * @param format Report format (pdf or excel)
 * @param companyId Optional company ID
 * @returns Promise that resolves when download is complete
 */
export async function downloadEmployeeTimesheet(
  employeeIds: string[],
  periodType: PeriodType = "monthly",
  format: ReportFormat = "pdf",
  companyId?: string
): Promise<void> {
  return downloadTimesheetReport({
    period_type: periodType,
    format,
    employee_ids: employeeIds,
    company_id: companyId,
  });
}

/**
 * Download timesheet report for specific department
 * @param departmentId Department ID
 * @param periodType Period type
 * @param format Report format (pdf or excel)
 * @param companyId Optional company ID
 * @returns Promise that resolves when download is complete
 */
export async function downloadDepartmentTimesheet(
  departmentId: string,
  periodType: PeriodType = "weekly",
  format: ReportFormat = "pdf",
  companyId?: string
): Promise<void> {
  return downloadTimesheetReport({
    period_type: periodType,
    format,
    department_id: departmentId,
    company_id: companyId,
  });
}

/**
 * Download timesheet report for specific date period
 * @param date Specific date (YYYY-MM-DD)
 * @param periodType Period type
 * @param format Report format (pdf or excel)
 * @param departmentId Optional department ID
 * @param companyId Optional company ID
 * @returns Promise that resolves when download is complete
 */
export async function downloadTimesheetForDate(
  date: string,
  periodType: PeriodType = "bi-weekly",
  format: ReportFormat = "pdf",
  departmentId?: string,
  companyId?: string
): Promise<void> {
  return downloadTimesheetReport({
    period_type: periodType,
    format,
    date,
    department_id: departmentId,
    company_id: companyId,
  });
}

/**
 * Format date for API (YYYY-MM-DD)
 * @param date Date object
 * @returns Formatted date string
 */
export function formatDateForTimesheetAPI(date: Date): string {
  return date.toISOString().split("T")[0];
}

/**
 * Validate timesheet report parameters
 * @param params Report parameters
 * @returns Validation result
 */
export function validateTimesheetParams(params: TimesheetReportParams): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate period type
  if (!["monthly", "weekly", "bi-weekly"].includes(params.period_type)) {
    errors.push("Invalid period type. Must be 'monthly', 'weekly', or 'bi-weekly'");
  }

  // Validate format
  if (!["pdf", "excel"].includes(params.format)) {
    errors.push("Invalid format. Must be 'pdf' or 'excel'");
  }

  // Validate month if provided
  if (params.month && (params.month < 1 || params.month > 12)) {
    errors.push("Month must be between 1 and 12");
  }

  // Validate year if provided
  if (params.year && (params.year < 2000 || params.year > 2100)) {
    errors.push("Year must be between 2000 and 2100");
  }

  // Validate date format if provided
  if (params.date && !/^\d{4}-\d{2}-\d{2}$/.test(params.date)) {
    errors.push("Date must be in YYYY-MM-DD format");
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
