/**
 * Promotion API Service
 * Handles all promotion-related API operations
 */

import { apiGet, apiPost, apiPatch } from "./api";
import { getAccessToken } from "./auth";
import {
  CreatePromotionRequest,
  UpdatePromotionRequest,
  PromotionResponse,
  PromotionsListResponse,
  SinglePromotionResponse,
  DeactivatePromotionResponse,
  PromotionFilters,
} from "@/types/promotion";

/**
 * Create a new promotion
 */
export const createPromotion = async (
  promotionData: CreatePromotionRequest
): Promise<PromotionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost("api/promotions", promotionData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response as PromotionResponse;
  } catch (error) {
    console.error("Error creating promotion:", error);
    throw error;
  }
};

/**
 * Get all promotions
 */
export const getPromotions = async (
  filters?: PromotionFilters
): Promise<PromotionsListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.is_active !== undefined) {
      params.append("is_active", filters.is_active.toString());
    }
    if (filters.rule_type) params.append("rule_type", filters.rule_type);
    if (filters.reward_type) params.append("reward_type", filters.reward_type);
    if (filters.applicable_customer_segments) {
      params.append(
        "applicable_customer_segments",
        filters.applicable_customer_segments
      );
    }
    if (filters.search) params.append("search", filters.search);
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/promotions?${queryString}`
    : "api/promotions";

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as PromotionsListResponse;
  } catch (error) {
    console.error("Error fetching promotions:", error);
    throw error;
  }
};

/**
 * Get promotion by rule ID
 */
export const getPromotionById = async (
  ruleId: string
): Promise<SinglePromotionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiGet(`api/promotions/${ruleId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as PromotionResponse;
  } catch (error) {
    console.error("Error fetching promotion:", error);
    throw error;
  }
};

/**
 * Update promotion
 */
export const updatePromotion = async (
  ruleId: string,
  updateData: UpdatePromotionRequest
): Promise<PromotionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPatch(`api/promotions/${ruleId}`, updateData, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    });

    return response as PromotionResponse;
  } catch (error) {
    console.error("Error updating promotion:", error);
    throw error;
  }
};

/**
 * Deactivate promotion
 */
export const deactivatePromotion = async (
  ruleId: string
): Promise<DeactivatePromotionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiPost(
      `api/promotions/${ruleId}/deactivate`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      }
    );

    return response as PromotionResponse;
  } catch (error) {
    console.error("Error deactivating promotion:", error);
    throw error;
  }
};

/**
 * Activate promotion (by updating is_active to true)
 */
export const activatePromotion = async (
  ruleId: string
): Promise<PromotionResponse> => {
  return updatePromotion(ruleId, { is_active: true });
};

/**
 * Get active promotions only
 */
export const getActivePromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotions({ is_active: true });
  };

/**
 * Get inactive promotions only
 */
export const getInactivePromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotions({ is_active: false });
  };

/**
 * Get promotions by rule type
 */
export const getPromotionsByRuleType = async (
  ruleType: string,
  filters?: Omit<PromotionFilters, "rule_type">
): Promise<PromotionsListResponse> => {
  return getPromotions({
    ...filters,
    rule_type: ruleType as any,
  });
};

/**
 * Get promotions by reward type
 */
export const getPromotionsByRewardType = async (
  rewardType: string,
  filters?: Omit<PromotionFilters, "reward_type">
): Promise<PromotionsListResponse> => {
  return getPromotions({
    ...filters,
    reward_type: rewardType as any,
  });
};

/**
 * Get promotions by customer segment
 */
export const getPromotionsByCustomerSegment = async (
  customerSegment: string,
  filters?: Omit<PromotionFilters, "applicable_customer_segments">
): Promise<PromotionsListResponse> => {
  return getPromotions({
    ...filters,
    applicable_customer_segments: customerSegment as any,
  });
};

/**
 * Search promotions by name or description
 */
export const searchPromotions = async (
  searchTerm: string,
  filters?: Omit<PromotionFilters, "search">
): Promise<PromotionsListResponse> => {
  return getPromotions({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get visit count promotions
 */
export const getVisitCountPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRuleType("visit_count");
  };

/**
 * Get spending amount promotions
 */
export const getSpendingPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRuleType("spending_amount");
  };

/**
 * Get birthday promotions
 */
export const getBirthdayPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRuleType("birthday");
  };

/**
 * Get first visit promotions
 */
export const getFirstVisitPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRuleType("first_visit");
  };

/**
 * Get free visit promotions
 */
export const getFreeVisitPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRewardType("free_visit");
  };

/**
 * Get discount promotions
 */
export const getDiscountPromotions =
  async (): Promise<PromotionsListResponse> => {
    const percentagePromotions = await getPromotionsByRewardType(
      "discount_percentage"
    );
    const amountPromotions = await getPromotionsByRewardType("discount_amount");

    // Combine both types of discount promotions
    return {
      extend: {
        count:
          percentagePromotions.extend.count + amountPromotions.extend.count,
        promotions: [
          ...percentagePromotions.extend.promotions,
          ...amountPromotions.extend.promotions,
        ],
      },
      msg: "Success!",
    };
  };

/**
 * Get loyalty points promotions
 */
export const getLoyaltyPointsPromotions =
  async (): Promise<PromotionsListResponse> => {
    return getPromotionsByRewardType("loyalty_points");
  };

/**
 * Duplicate promotion (create a copy with modified name)
 */
export const duplicatePromotion = async (
  ruleId: string,
  newName?: string
): Promise<PromotionResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    // First, get the existing promotion
    const existingPromotion = await getPromotionById(ruleId);
    const promotion = existingPromotion.extend.promotion;

    // Create a new promotion based on the existing one
    const newPromotionData: CreatePromotionRequest = {
      name: newName || `${promotion.name} (Copy)`,
      description: promotion.description || undefined,
      rule_type: promotion.rule_type,
      trigger_value: promotion.trigger_value,
      trigger_period_days: promotion.trigger_period_days || undefined,
      reward_type: promotion.reward_type,
      reward_value: promotion.reward_value,
      reward_description: promotion.reward_description || undefined,
      reward_expiry_days: promotion.reward_expiry_days || undefined,
      applicable_customer_segments:
        promotion.applicable_customer_segments || undefined,
      applicable_visit_types: promotion.applicable_visit_types || undefined,
      max_redemptions_per_customer:
        promotion.max_redemptions_per_customer || undefined,
      priority: promotion.priority,
      valid_from: promotion.valid_from || undefined,
      valid_until: promotion.valid_until || undefined,
    };

    return createPromotion(newPromotionData);
  } catch (error) {
    console.error("Error duplicating promotion:", error);
    throw error;
  }
};

/**
 * Bulk activate promotions
 */
export const bulkActivatePromotions = async (
  ruleIds: string[]
): Promise<PromotionResponse[]> => {
  const results = await Promise.allSettled(
    ruleIds.map((ruleId) => activatePromotion(ruleId))
  );

  const successful = results
    .filter(
      (result): result is PromiseFulfilledResult<PromotionResponse> =>
        result.status === "fulfilled"
    )
    .map((result) => result.value);

  const failed = results
    .filter(
      (result): result is PromiseRejectedResult => result.status === "rejected"
    )
    .map((result) => result.reason);

  if (failed.length > 0) {
    console.warn("Some promotions failed to activate:", failed);
  }

  return successful;
};

/**
 * Bulk deactivate promotions
 */
export const bulkDeactivatePromotions = async (
  ruleIds: string[]
): Promise<DeactivatePromotionResponse[]> => {
  const results = await Promise.allSettled(
    ruleIds.map((ruleId) => deactivatePromotion(ruleId))
  );

  const successful = results
    .filter(
      (result): result is PromiseFulfilledResult<DeactivatePromotionResponse> =>
        result.status === "fulfilled"
    )
    .map((result) => result.value);

  const failed = results
    .filter(
      (result): result is PromiseRejectedResult => result.status === "rejected"
    )
    .map((result) => result.reason);

  if (failed.length > 0) {
    console.warn("Some promotions failed to deactivate:", failed);
  }

  return successful;
};
