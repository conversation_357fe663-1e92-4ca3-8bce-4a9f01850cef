/**
 * Organizational Units API Service
 * Handles all API calls for organizational unit management functionality
 */

import { apiGet, apiPost, apiPut, apiDelete } from './api';
import { getAccessToken } from './auth';
import {
  OrgUnit,
  CreateOrgUnitRequest,
  UpdateOrgUnitRequest,
  OrgUnitResponse,
  OrgUnitsListResponse,
  SingleOrgUnitResponse,
  DeleteOrgUnitResponse,
  DepartmentStructure,
  OrgUnitFilters,
} from '@/types/org-unit';

/**
 * Create a new organizational unit
 */
export const createOrgUnit = async (
  unitData: CreateOrgUnitRequest
): Promise<OrgUnitResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPost<OrgUnitResponse>('org_units', unitData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Get all organizational units
 */
export const getOrgUnits = async (
  filters?: OrgUnitFilters
): Promise<OrgUnitsListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Build query parameters
  const params = new URLSearchParams();
  if (filters?.department_id) params.append('department_id', filters.department_id);
  if (filters?.parent_id) params.append('parent_id', filters.parent_id);
  if (filters?.level !== undefined) params.append('level', filters.level.toString());
  if (filters?.search) params.append('search', filters.search);
  if (filters?.page) params.append('page', filters.page.toString());
  if (filters?.per_page) params.append('per_page', filters.per_page.toString());

  const queryString = params.toString();
  const url = queryString ? `org_units?${queryString}` : 'org_units';

  return apiGet<OrgUnitsListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get organizational unit by ID (with children)
 */
export const getOrgUnitById = async (
  unitId: string
): Promise<SingleOrgUnitResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<SingleOrgUnitResponse>(`org_units/${unitId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get all organizational units for a specific department
 */
export const getDepartmentUnits = async (
  departmentId: string
): Promise<OrgUnitsListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<OrgUnitsListResponse>(`departments/${departmentId}/org_units`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get full hierarchical structure for a department
 */
export const getDepartmentStructure = async (
  departmentId: string
): Promise<DepartmentStructure> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<DepartmentStructure>(`departments/${departmentId}/structure`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Update organizational unit
 */
export const updateOrgUnit = async (
  unitId: string,
  unitData: UpdateOrgUnitRequest
): Promise<OrgUnitResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPut<OrgUnitResponse>(`org_units/${unitId}`, unitData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Delete organizational unit
 */
export const deleteOrgUnit = async (
  unitId: string
): Promise<DeleteOrgUnitResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiDelete<DeleteOrgUnitResponse>(`org_units/${unitId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get organizational units that can be parent units (for creating sub-units)
 * Returns units that are not at the maximum level
 */
export const getAvailableParentUnits = async (
  departmentId: string,
  maxLevel: number = 5
): Promise<OrgUnit[]> => {
  const response = await getDepartmentUnits(departmentId);
  
  if (!response.success || !response.org_units) {
    return [];
  }

  // Filter units that can have children (not at max level)
  return response.org_units.filter(unit => unit.level < maxLevel);
};
