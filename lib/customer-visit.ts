/**
 * Customer Visit API Service
 * Handles all customer visit-related API operations
 */

import { apiGet } from "./api";
import { getAccessToken } from "./auth";
import {
  CustomerVisitFilters,
  CustomerVisitsListResponse,
  CustomerVisitsFilteredResponse,
  CustomerVisitResponse,
  DailySummaryResponse,
  CustomerVisitHistoryResponse,
} from "@/types/customer-visit";

/**
 * Get all customer visits with optional filtering
 */
export const getCustomerVisits = async (
  filters?: CustomerVisitFilters
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.customer_id) params.append("customer_id", filters.customer_id);
    if (filters.date) params.append("date", filters.date);
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);
    if (filters.visit_type) params.append("visit_type", filters.visit_type);
    if (filters.source) params.append("source", filters.source);
    if (filters.is_loyalty_visit !== undefined) {
      params.append("is_loyalty_visit", filters.is_loyalty_visit.toString());
    }
    if (filters.reward_redeemed !== undefined) {
      params.append("reward_redeemed", filters.reward_redeemed.toString());
    }
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page)
      params.append("per_page", filters.per_page.toString());
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/customer-visits?${queryString}`
    : "api/customer-visits";

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as
      | CustomerVisitsListResponse
      | CustomerVisitsFilteredResponse;
  } catch (error) {
    console.error("Error fetching customer visits:", error);
    throw error;
  }
};

/**
 * Get customer visit by ID
 */
export const getCustomerVisitById = async (
  visitId: string
): Promise<CustomerVisitResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  try {
    const response = await apiGet(`api/customer-visits/${visitId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerVisitResponse;
  } catch (error) {
    console.error("Error fetching customer visit:", error);
    throw error;
  }
};

/**
 * Get daily summary of customer visits
 */
export const getDailySummary = async (
  date?: string
): Promise<DailySummaryResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  const endpoint = date
    ? `api/customer-visits/daily-summary?date=${date}`
    : "api/customer-visits/daily-summary";

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as DailySummaryResponse;
  } catch (error) {
    console.error("Error fetching daily summary:", error);
    throw error;
  }
};

/**
 * Get customer visit history for a specific customer
 */
export const getCustomerVisitHistory = async (
  customerId: string,
  filters?: {
    start_date?: string;
    end_date?: string;
    page?: number;
    per_page?: number;
  }
): Promise<CustomerVisitHistoryResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error("Authentication required");
  }

  // Build query parameters
  const params = new URLSearchParams();

  if (filters) {
    if (filters.start_date) params.append("start_date", filters.start_date);
    if (filters.end_date) params.append("end_date", filters.end_date);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.per_page)
      params.append("per_page", filters.per_page.toString());
  }

  const queryString = params.toString();
  const endpoint = queryString
    ? `api/customers/${customerId}/visits?${queryString}`
    : `api/customers/${customerId}/visits`;

  try {
    const response = await apiGet(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response as CustomerVisitHistoryResponse;
  } catch (error) {
    console.error("Error fetching customer visit history:", error);
    throw error;
  }
};

/**
 * Get customer visits for today
 */
export const getTodayVisits = async (): Promise<
  CustomerVisitsListResponse | CustomerVisitsFilteredResponse
> => {
  const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD format
  return getCustomerVisits({ date: today });
};

/**
 * Get customer visits for a specific date range
 */
export const getVisitsByDateRange = async (
  startDate: string,
  endDate: string,
  page: number = 1,
  perPage: number = 20
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  return getCustomerVisits({
    start_date: startDate,
    end_date: endDate,
    page,
    per_page: perPage,
  });
};

/**
 * Get loyalty visits only
 */
export const getLoyaltyVisits = async (
  filters?: Omit<CustomerVisitFilters, "is_loyalty_visit">
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  return getCustomerVisits({
    ...filters,
    is_loyalty_visit: true,
  });
};

/**
 * Get visits with rewards redeemed
 */
export const getRewardVisits = async (
  filters?: Omit<CustomerVisitFilters, "reward_redeemed">
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  return getCustomerVisits({
    ...filters,
    reward_redeemed: true,
  });
};

/**
 * Get biometric visits only
 */
export const getBiometricVisits = async (
  filters?: Omit<CustomerVisitFilters, "source">
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  return getCustomerVisits({
    ...filters,
    source: "biometric",
  });
};

/**
 * Get manual visits only
 */
export const getManualVisits = async (
  filters?: Omit<CustomerVisitFilters, "source">
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  return getCustomerVisits({
    ...filters,
    source: "manual",
  });
};

/**
 * Get recent visits (last 24 hours)
 */
export const getRecentVisits = async (): Promise<
  CustomerVisitsListResponse | CustomerVisitsFilteredResponse
> => {
  const now = new Date();
  const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  const startDate = yesterday.toISOString().split("T")[0];
  const endDate = now.toISOString().split("T")[0];

  return getCustomerVisits({
    start_date: startDate,
    end_date: endDate,
  });
};

/**
 * Search visits by customer name or membership number
 * This function gets all visits and filters them client-side
 * In a real implementation, this should be done server-side
 */
export const searchVisits = async (
  searchTerm: string,
  filters?: CustomerVisitFilters
): Promise<CustomerVisitsListResponse | CustomerVisitsFilteredResponse> => {
  // For now, we'll get all visits and filter client-side
  // In production, this should be handled by the API
  const response = await getCustomerVisits(filters);

  if ("extend" in response) {
    // No filters response format
    const filteredVisits = response.extend.visits.filter((visit) => {
      const customer = visit.customer;
      const searchLower = searchTerm.toLowerCase();

      return (
        customer.full_name.toLowerCase().includes(searchLower) ||
        customer.first_name.toLowerCase().includes(searchLower) ||
        customer.last_name.toLowerCase().includes(searchLower) ||
        (customer.email &&
          customer.email.toLowerCase().includes(searchLower)) ||
        (customer.membership_number &&
          customer.membership_number.toLowerCase().includes(searchLower))
      );
    });

    return {
      extend: {
        count: filteredVisits.length,
        visits: filteredVisits,
      },
      msg: response.msg,
    };
  } else {
    // Filtered response format
    const filteredVisits = response.visits.filter((visit) => {
      const customer = visit.customer;
      const searchLower = searchTerm.toLowerCase();

      return (
        customer.full_name.toLowerCase().includes(searchLower) ||
        customer.first_name.toLowerCase().includes(searchLower) ||
        customer.last_name.toLowerCase().includes(searchLower) ||
        (customer.email &&
          customer.email.toLowerCase().includes(searchLower)) ||
        (customer.membership_number &&
          customer.membership_number.toLowerCase().includes(searchLower))
      );
    });

    return {
      ...response,
      visits: filteredVisits,
      pagination: {
        ...response.pagination,
        count: filteredVisits.length,
        total_records: filteredVisits.length,
      },
    };
  }
};
