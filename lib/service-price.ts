/**
 * Service Price API Service
 * Handles all API calls for service price management functionality
 */

import { apiGet, apiPost, apiPatch } from './api';
import { getAccessToken } from './auth';
import {
  ServicePrice,
  CreateServicePriceRequest,
  UpdateServicePriceRequest,
  ServicePriceResponse,
  ServicePricesListResponse,
  SingleServicePriceResponse,
  CurrentPriceResponse,
  ServicePriceFilters,
} from '@/types/service-price';

/**
 * Create a new service price
 */
export const createServicePrice = async (
  priceData: CreateServicePriceRequest
): Promise<ServicePriceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPost<ServicePriceResponse>('api/service-prices', priceData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Get service price by ID
 */
export const getServicePriceById = async (priceId: string): Promise<SingleServicePriceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<SingleServicePriceResponse>(`api/service-prices/${priceId}`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get all service prices
 */
export const getServicePrices = async (filters?: ServicePriceFilters): Promise<ServicePricesListResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  // Build query parameters
  const params = new URLSearchParams();
  
  if (filters) {
    if (filters.service_id) {
      params.append('service_id', filters.service_id);
    }
    if (filters.effective_date) {
      params.append('effective_date', filters.effective_date);
    }
    if (filters.is_current !== undefined) {
      params.append('is_current', filters.is_current.toString());
    }
    if (filters.search) {
      params.append('search', filters.search);
    }
    if (filters.page) {
      params.append('page', filters.page.toString());
    }
    if (filters.limit) {
      params.append('limit', filters.limit.toString());
    }
  }

  const queryString = params.toString();
  const url = queryString ? `api/service-prices?${queryString}` : 'api/service-prices';

  return apiGet<ServicePricesListResponse>(url, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Update service price
 */
export const updateServicePrice = async (
  priceId: string,
  priceData: UpdateServicePriceRequest
): Promise<ServicePriceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiPatch<ServicePriceResponse>(`api/service-prices/${priceId}`, priceData, {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });
};

/**
 * Get current price for a service
 */
export const getCurrentServicePrice = async (serviceId: string): Promise<CurrentPriceResponse> => {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  return apiGet<CurrentPriceResponse>(`api/services/${serviceId}/current-price`, {
    headers: {
      'Authorization': `Bearer ${token}`,
    },
  });
};

/**
 * Get service prices by service ID
 */
export const getServicePricesByService = async (
  serviceId: string,
  filters?: Omit<ServicePriceFilters, 'service_id'>
): Promise<ServicePricesListResponse> => {
  return getServicePrices({
    ...filters,
    service_id: serviceId,
  });
};

/**
 * Get current service prices only
 */
export const getCurrentServicePrices = async (
  filters?: Omit<ServicePriceFilters, 'is_current'>
): Promise<ServicePricesListResponse> => {
  return getServicePrices({
    ...filters,
    is_current: true,
  });
};

/**
 * Search service prices
 */
export const searchServicePrices = async (
  searchTerm: string,
  filters?: Omit<ServicePriceFilters, 'search'>
): Promise<ServicePricesListResponse> => {
  return getServicePrices({
    ...filters,
    search: searchTerm,
  });
};

/**
 * Get service prices with pagination
 */
export const getServicePricesPaginated = async (
  page: number = 1,
  limit: number = 10,
  filters?: Omit<ServicePriceFilters, 'page' | 'limit'>
): Promise<ServicePricesListResponse> => {
  return getServicePrices({
    ...filters,
    page,
    limit,
  });
};

/**
 * Get service prices for a specific date
 */
export const getServicePricesForDate = async (
  date: string,
  filters?: Omit<ServicePriceFilters, 'effective_date'>
): Promise<ServicePricesListResponse> => {
  return getServicePrices({
    ...filters,
    effective_date: date,
  });
};

/**
 * Duplicate service price (create a copy with new dates)
 */
export const duplicateServicePrice = async (
  priceId: string,
  newEffectiveFrom?: string,
  newEffectiveTo?: string
): Promise<ServicePriceResponse> => {
  // First get the original service price
  const originalResponse = await getServicePriceById(priceId);
  const originalPrice = originalResponse.extend.service_price;

  // Create a new service price based on the original
  const newPriceData: CreateServicePriceRequest = {
    service_id: originalPrice.service_id,
    price_amount: originalPrice.price_amount,
    effective_from: newEffectiveFrom || new Date().toISOString().split('T')[0],
    effective_to: newEffectiveTo || undefined,
  };

  return createServicePrice(newPriceData);
};

/**
 * Get service price statistics
 */
export const getServicePriceStatistics = async (): Promise<{
  total: number;
  current: number;
  expired: number;
  future: number;
  services_with_prices: number;
}> => {
  const [allPrices, currentPrices] = await Promise.all([
    getServicePrices(),
    getCurrentServicePrices(),
  ]);

  const now = new Date();
  const expired = allPrices.extend.service_prices.filter(price => 
    price.effective_to && new Date(price.effective_to) < now
  ).length;
  
  const future = allPrices.extend.service_prices.filter(price => 
    new Date(price.effective_from) > now
  ).length;

  const uniqueServices = new Set(
    allPrices.extend.service_prices.map(price => price.service_id)
  ).size;

  return {
    total: allPrices.extend.count,
    current: currentPrices.extend.count,
    expired,
    future,
    services_with_prices: uniqueServices,
  };
};

/**
 * Get price history for a service
 */
export const getServicePriceHistory = async (
  serviceId: string
): Promise<ServicePricesListResponse> => {
  return getServicePricesByService(serviceId);
};

/**
 * Check if service has current price
 */
export const hasCurrentPrice = async (serviceId: string): Promise<boolean> => {
  try {
    await getCurrentServicePrice(serviceId);
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Get services without current prices
 */
export const getServicesWithoutCurrentPrices = async (): Promise<string[]> => {
  // This would require a custom endpoint or client-side filtering
  // For now, we'll return an empty array as this functionality
  // would need to be implemented on the backend
  return [];
};
