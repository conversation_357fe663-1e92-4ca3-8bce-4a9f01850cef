import { apiGet, apiPost } from './api';
import { getAccessToken } from './auth';
import {
  AddCompanyDeviceRequest,
  AddCompanyDeviceResponse,
  InitializeDeviceResponse
} from '@/types/device-sync';

export interface Company {
  company_id: string;
  company_name: string;
  company_tin: string | null;
  created_at: string;
  database_name: string;
  devices: Device[];
  phone_number: string | null;
}

export interface Device {
  id: number;
  device_sn: string;
  device_name: string;
  device_type: string;
  status: string;
  created_at: string;
}

export interface CompaniesResponse {
  companies: Company[];
}

// Legacy interface for backward compatibility
export interface AddDeviceRequest {
  company_id: string;
  device_name: string;
  device_type: string;
}

// Legacy interface for backward compatibility
export interface AddDeviceResponse {
  success: boolean;
  message: string;
  device?: Device;
}

/**
 * Get all companies
 */
export async function getCompanies(): Promise<CompaniesResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<CompaniesResponse>('get_companies', {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}

/**
 * Add a device to a company (Legacy function for backward compatibility)
 */
export async function addCompanyDevice(data: AddDeviceRequest): Promise<AddDeviceResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<AddDeviceResponse>('add_company_device', data, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}

/**
 * Add a device to a company using the correct API endpoint
 * This is the fixed version that should work with the actual API
 */
export async function addCompanyDeviceFixed(data: AddCompanyDeviceRequest): Promise<AddCompanyDeviceResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiPost<AddCompanyDeviceResponse>('add_company_device', data, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}

/**
 * Initialize a device system
 * @param deviceSn The device serial number to initialize
 * @returns Initialize device response
 */
export async function initializeDevice(deviceSn: string): Promise<InitializeDeviceResponse> {
  const token = getAccessToken();
  if (!token) {
    throw new Error('Authentication required');
  }

  const response = await apiGet<InitializeDeviceResponse>(`initSystem?deviceSn=${deviceSn}`, {
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response;
}
