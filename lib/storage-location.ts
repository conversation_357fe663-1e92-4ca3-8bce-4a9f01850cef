/**
 * Storage Location API Service (Cleaned)
 * Handles all API calls for storage location management functionality
 */

import { apiGet, apiPost, apiPatch, apiDelete } from "./api";
import { getAccessToken } from "./auth";
import {
  StorageLocation,
  CreateStorageLocationRequest,
  UpdateStorageLocationRequest,
  StorageLocationResponse,
  StorageLocationsListResponse,
  SingleStorageLocationResponse,
  DeleteStorageLocationResponse,
  StorageLocationFilters,
  StorageLocationStatistics,
  BulkUpdateStorageLocationRequest,
} from "@/types/storage-location";

/**
 * Create a new storage location
 */
export const createStorageLocation = async (
  locationData: CreateStorageLocationRequest
): Promise<StorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  return apiPost<StorageLocationResponse>(
    "api/storage-locations",
    locationData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Get storage location by ID
 */
export const getStorageLocationById = async (
  locationId: string
): Promise<SingleStorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  return apiGet<SingleStorageLocationResponse>(
    `api/storage-locations/${locationId}`,
    {
      headers: { Authorization: `Bearer ${token}` },
    }
  );
};

/**
 * Get all storage locations
 */
export const getStorageLocations = async (
  filters?: StorageLocationFilters
): Promise<StorageLocationsListResponse> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  const params = new URLSearchParams();

  if (filters) {
    if (filters.is_available !== undefined)
      params.append("available_only", filters.is_available.toString());
    if (filters.search) params.append("search", filters.search);
    if (filters.page) params.append("page", filters.page.toString());
    if (filters.limit) params.append("limit", filters.limit.toString());
  }

  const queryString = params.toString();
  const url = queryString
    ? `api/storage-locations?${queryString}`
    : "api/storage-locations";

  return apiGet<StorageLocationsListResponse>(url, {
    headers: { Authorization: `Bearer ${token}` },
  });
};

/**
 * Update storage location
 */
export const updateStorageLocation = async (
  locationId: string,
  locationData: UpdateStorageLocationRequest
): Promise<StorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  return apiPatch<StorageLocationResponse>(
    `api/storage-locations/${locationId}`,
    locationData,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
};

/**
 * Delete storage location
 */
export const deleteStorageLocation = async (
  locationId: string
): Promise<DeleteStorageLocationResponse> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  return apiDelete<DeleteStorageLocationResponse>(
    `api/storage-locations/${locationId}`,
    { headers: { Authorization: `Bearer ${token}` } }
  );
};

/**
 * Get available storage locations only
 */
export const getAvailableStorageLocations =
  async (): Promise<StorageLocationsListResponse> => {
    return getStorageLocations({ is_available: true });
  };

/**
 * Search storage locations
 */
export const searchStorageLocations = async (
  searchTerm: string
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({ search: searchTerm });
};

/**
 * Get paginated storage locations
 */
export const getStorageLocationsPaginated = async (
  page: number = 1,
  limit: number = 10
): Promise<StorageLocationsListResponse> => {
  return getStorageLocations({ page, limit });
};

/**
 * Bulk update storage locations
 */
export const bulkUpdateStorageLocations = async (
  bulkData: BulkUpdateStorageLocationRequest
): Promise<{ success: number; failed: number; errors: string[] }> => {
  const token = getAccessToken();
  if (!token) throw new Error("Authentication required");

  const results = { success: 0, failed: 0, errors: [] as string[] };

  for (const locationId of bulkData.location_ids) {
    try {
      await updateStorageLocation(locationId, bulkData.updates);
      results.success++;
    } catch (error) {
      results.failed++;
      results.errors.push(`Failed to update ${locationId}: ${error}`);
    }
  }

  return results;
};

/**
 * Get storage location statistics
 */
export const getStorageLocationStatistics =
  async (): Promise<StorageLocationStatistics> => {
    const response = await getStorageLocations();
    const locations = response.extend.storage_locations;

    return {
      total: locations.length,
      available: locations.filter((l) => l.is_available).length,
      unavailable: locations.filter((l) => !l.is_available).length,
    };
  };

/**
 * Toggle storage location availability
 * Note: The API doesn't have a direct toggle endpoint, so we need to implement this differently
 * For now, this function is a placeholder that doesn't actually change availability
 */
export const toggleStorageLocationAvailability = async (
  locationId: string
): Promise<StorageLocationResponse> => {
  // TODO: Implement proper availability toggle when API supports it
  // The current API only allows updating notes, not availability status
  // Availability is managed automatically when items are stored/retrieved
  throw new Error(
    "Toggle availability is not supported by the current API. Availability is managed automatically when items are stored or retrieved."
  );
};
