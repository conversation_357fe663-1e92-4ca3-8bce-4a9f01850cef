/**
 * Date utility functions for consistent date formatting across the application
 */

/**
 * Convert a date string (YYYY-MM-DD) to datetime format expected by backend (YYYY-MM-DD HH:MM:SS)
 * @param dateString Date string in YYYY-MM-DD format
 * @param time Optional time string in HH:MM:SS format. If not provided, current time is used.
 * @returns Datetime string in YYYY-MM-DD HH:MM:SS format
 */
export const formatDateForBackend = (
  dateString: string,
  time?: string
): string => {
  if (!dateString) {
    throw new Error("Date string is required");
  }
  // If time is not provided, use current time
  const now = new Date();
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");
  const timeString = time || `${hours}:${minutes}:${seconds}`;

  return `${dateString} ${timeString}`;
};

/**
 * Convert a datetime string to date string (YYYY-MM-DD)
 * @param datetimeString Datetime string in various formats
 * @returns Date string in YYYY-MM-DD format
 */
export const extractDateFromDatetime = (datetimeString: string): string => {
  if (!datetimeString) {
    return "";
  }
  // Handle ISO format (2025-10-11T14:30:00.000Z) or simple datetime (2025-10-11 14:30:00)
  return datetimeString.split("T")[0].split(" ")[0];
};

/**
 * Get current date in YYYY-MM-DD format
 * @returns Current date string in YYYY-MM-DD format
 */
export const getCurrentDateString = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");

  return `${year}-${month}-${day}`;
};

/**
 * Get current datetime in YYYY-MM-DD HH:MM:SS format
 * @returns Current datetime string in YYYY-MM-DD HH:MM:SS format
 */
export const getCurrentDateTimeString = (): string => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  const day = String(now.getDate()).padStart(2, "0");
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

/**
 * Validate date string format (YYYY-MM-DD)
 * @param dateString Date string to validate
 * @returns True if valid, false otherwise
 */
export const isValidDateString = (dateString: string): boolean => {
  if (!dateString) return false;
  const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (!dateRegex.test(dateString)) return false;
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

/**
 * Validate datetime string format (YYYY-MM-DD HH:MM:SS)
 * @param datetimeString Datetime string to validate
 * @returns True if valid, false otherwise
 */
export const isValidDateTimeString = (datetimeString: string): boolean => {
  if (!datetimeString) return false;
  const datetimeRegex = /^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/;
  if (!datetimeRegex.test(datetimeString)) return false;
  const date = new Date(datetimeString);
  return !isNaN(date.getTime());
};
