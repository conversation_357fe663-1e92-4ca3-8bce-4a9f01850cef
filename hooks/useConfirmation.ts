"use client";

import { useState, useCallback } from 'react';

export type ConfirmationType = 'danger' | 'warning' | 'info' | 'success';

interface ConfirmationOptions {
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  type?: ConfirmationType;
}

interface ConfirmationState extends ConfirmationOptions {
  isOpen: boolean;
  isLoading: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

export const useConfirmation = () => {
  const [confirmationState, setConfirmationState] = useState<ConfirmationState>({
    isOpen: false,
    isLoading: false,
    title: '',
    message: '',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'warning',
    onConfirm: () => {},
    onCancel: () => {},
  });

  const showConfirmation = useCallback(
    (options: ConfirmationOptions): Promise<boolean> => {
      return new Promise((resolve) => {
        setConfirmationState({
          isOpen: true,
          isLoading: false,
          title: options.title,
          message: options.message,
          confirmText: options.confirmText || 'Confirm',
          cancelText: options.cancelText || 'Cancel',
          type: options.type || 'warning',
          onConfirm: () => {
            setConfirmationState(prev => ({ ...prev, isLoading: true }));
            resolve(true);
          },
          onCancel: () => {
            setConfirmationState(prev => ({ ...prev, isOpen: false }));
            resolve(false);
          },
        });
      });
    },
    []
  );

  const hideConfirmation = useCallback(() => {
    setConfirmationState(prev => ({ ...prev, isOpen: false, isLoading: false }));
  }, []);

  const setLoading = useCallback((loading: boolean) => {
    setConfirmationState(prev => ({ ...prev, isLoading: loading }));
  }, []);

  return {
    confirmationState,
    showConfirmation,
    hideConfirmation,
    setLoading,
  };
};

// Convenience functions for common confirmation types
export const useDeleteConfirmation = () => {
  const { showConfirmation, confirmationState, hideConfirmation, setLoading } = useConfirmation();

  const confirmDelete = useCallback(
    (itemName: string, itemType: string = 'item'): Promise<boolean> => {
      return showConfirmation({
        title: `Delete ${itemType}`,
        message: `Are you sure you want to delete "${itemName}"? This action cannot be undone.`,
        confirmText: 'Delete',
        cancelText: 'Cancel',
        type: 'danger',
      });
    },
    [showConfirmation]
  );

  return {
    confirmDelete,
    confirmationState,
    hideConfirmation,
    setLoading,
  };
};

export const useActionConfirmation = () => {
  const { showConfirmation, confirmationState, hideConfirmation, setLoading } = useConfirmation();

  const confirmAction = useCallback(
    (
      action: string,
      itemName: string,
      type: ConfirmationType = 'warning'
    ): Promise<boolean> => {
      return showConfirmation({
        title: `Confirm ${action}`,
        message: `Are you sure you want to ${action.toLowerCase()} "${itemName}"?`,
        confirmText: action,
        cancelText: 'Cancel',
        type,
      });
    },
    [showConfirmation]
  );

  return {
    confirmAction,
    confirmationState,
    hideConfirmation,
    setLoading,
  };
};
