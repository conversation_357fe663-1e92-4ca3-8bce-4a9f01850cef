# Customer Management Testing Guide

This guide provides step-by-step instructions for testing the customer CRUD operations functionality.

## Prerequisites

1. Ensure the application is running locally
2. Have HR, admin, or super-admin role access
3. API endpoints are available at the configured base URL

## Testing Steps

### 1. Access Customer Management

1. **Navigate to HR Dashboard**
   - Go to `/dashboard/hr`
   - Verify the "Customers" quick action card is visible with "NEW" badge
   - Click on the "Customers" card

2. **Verify Customer Management Page**
   - URL should be `/dashboard/hr/customers`
   - Page title should be "Customer Management"
   - "Add Customer" button should be visible
   - Search and filter controls should be present

### 2. Test Customer Creation

1. **Open Registration Form**
   - Click "Add Customer" button
   - Modal should open with title "Add New Customer"

2. **Test Form Validation**
   - Try submitting empty form - should show validation errors
   - Enter invalid email format - should show email validation error
   - Enter invalid phone number - should show phone validation error
   - Enter future date of birth - should show date validation error

3. **Create Valid Customer**
   - Fill in required fields:
     - First Name: "John"
     - Last Name: "Doe"
   - Fill in optional fields:
     - Email: "<EMAIL>"
     - Phone: "+1234567890"
     - Membership Number: "12345"
     - Customer Segment: "VIP"
     - Preferred Contact Method: "Email"
     - Date of Birth: "1990-01-01"
     - Notes: "Test customer"
     - Marketing Consent: Check the checkbox
   - Click "Create Customer"
   - Should show success and close modal
   - New customer should appear in the list

### 3. Test Customer List Features

1. **Verify Customer Display**
   - Customer should appear in the table/cards
   - All information should be displayed correctly
   - Status badges should have appropriate colors
   - Segment badges should have appropriate colors

2. **Test Search Functionality**
   - Enter customer name in search box
   - Results should filter in real-time
   - Try searching by email, phone, or membership number

3. **Test Filtering**
   - Click "Filters" button to expand filter panel
   - Filter by customer segment - results should update
   - Filter by status - results should update
   - Use "Clear Filters" to reset

4. **Test Pagination**
   - If more than 10 customers exist, pagination should appear
   - Test "Previous" and "Next" buttons
   - Verify page numbers are correct

### 4. Test Customer Details View

1. **Open Customer Details**
   - Click the "View" (eye) icon for a customer
   - Modal should open with customer details

2. **Verify Information Display**
   - All customer information should be displayed
   - Contact information should be clickable (email/phone links)
   - Customer segment and status should have colored badges
   - Registration and update timestamps should be shown

3. **Test Statistics Section**
   - Statistics should load (or show loading spinner)
   - Visit breakdown should be displayed
   - If no statistics, should show "No statistics available"

4. **Test Quick Actions**
   - If customer has email, "Send Email" button should be present
   - If customer has phone, "Call Customer" button should be present
   - Buttons should open appropriate applications (email client/phone)

### 5. Test Customer Editing

1. **Open Edit Modal**
   - Click the "Edit" (pencil) icon for a customer
   - Modal should open with title "Edit Customer"
   - Form should be pre-populated with customer data

2. **Test Form Pre-population**
   - All fields should contain current customer data
   - Dropdowns should show correct selected values
   - Checkboxes should reflect current state

3. **Test Updates**
   - Modify some fields (e.g., change segment to "Premium")
   - Add or modify notes
   - Change status to "Inactive"
   - Click "Update Customer"
   - Should show success and close modal
   - Changes should be reflected in the customer list

4. **Test Validation on Edit**
   - Clear required fields - should show validation errors
   - Enter invalid data - should show appropriate errors

### 6. Test Customer Deletion

1. **Delete Customer**
   - Click the "Delete" (trash) icon for a customer
   - Should show confirmation dialog
   - Click "Cancel" - nothing should happen
   - Click "Delete" again and confirm
   - Customer should be removed from the list

### 7. Test Error Handling

1. **Network Errors**
   - Disconnect internet or block API calls
   - Try to load customers - should show error message
   - Try to create/update/delete - should show appropriate error messages

2. **Invalid Data**
   - Try to access non-existent customer details
   - Should handle gracefully with error messages

### 8. Test Responsive Design

1. **Mobile View**
   - Resize browser to mobile width
   - Table should switch to card layout
   - All functionality should remain accessible
   - Forms should be mobile-friendly

2. **Tablet View**
   - Test at tablet breakpoints
   - Layout should adapt appropriately

### 9. Test Integration with HR Dashboard

1. **Navigation**
   - From customer management, navigate back to HR dashboard
   - Customer quick action should still be visible
   - Other dashboard functionality should work normally

2. **Permissions**
   - Test with different user roles
   - Only HR, admin, and super-admin should have access
   - Other roles should be redirected or see access denied

## Expected API Calls

During testing, the following API calls should be made:

1. **GET** `/api/customers` - List customers with pagination/filters
2. **POST** `/api/customers` - Create new customer
3. **GET** `/api/customers/{id}` - Get customer details
4. **PATCH** `/api/customers/{id}` - Update customer
5. **DELETE** `/api/customers/{id}` - Delete customer
6. **GET** `/api/customers/{id}/statistics` - Get customer statistics

## Common Issues to Check

1. **Form Validation**
   - Required field validation
   - Email format validation
   - Phone number format validation
   - Date validation

2. **Data Display**
   - Proper formatting of dates
   - Correct badge colors for segments/status
   - Proper handling of null/empty values

3. **User Experience**
   - Loading states during API calls
   - Error messages are user-friendly
   - Success feedback is provided
   - Modals close properly after actions

4. **Performance**
   - List loads quickly
   - Search/filtering is responsive
   - No memory leaks when opening/closing modals

## Success Criteria

✅ All CRUD operations work correctly
✅ Form validation prevents invalid data
✅ Search and filtering work as expected
✅ Customer details display all information
✅ Statistics load and display properly
✅ Error handling is graceful
✅ Responsive design works on all devices
✅ Integration with HR dashboard is seamless
✅ User permissions are enforced
✅ API calls are made correctly

## Reporting Issues

If any issues are found during testing:

1. Document the steps to reproduce
2. Include browser/device information
3. Note any console errors
4. Capture screenshots if applicable
5. Check network tab for failed API calls
