{"info": {"_postman_id": "06fd3cf9-537c-4cdd-8642-ec2ecd484c80", "name": "export-folder", "schema": "https://schema.getpostman.com/json/collection/v2.0.0/collection.json", "_exporter_id": "44839095", "_collection_link": "https://lively-moon-11159.postman.co/workspace/New-Team-Workspace~58219126-f4e7-4259-a7ba-e0a347e183eb/collection/44839095-06fd3cf9-537c-4cdd-8642-ec2ecd484c80?action=share&source=collection_link&creator=44839095"}, "item": [{"name": "org units", "item": [{"name": "create a unit", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"department_id\":\"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"name\": \"pediatrics\",\n    \"level\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sms.remmittance.com/org_units"}, "response": [{"name": "success", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"department_id\":\"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"name\": \"pediatrics\",\n    \"level\": 1\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sms.remmittance.com/org_units"}, "status": "Created", "code": 201, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:18:51 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "331"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"created_at\": \"2025-11-12 20:18:51\",\n    \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"department_name\": \"Admin\",\n    \"description\": null,\n    \"level\": 1,\n    \"manager_id\": null,\n    \"name\": \"pediatrics\",\n    \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n    \"parent_id\": null,\n    \"updated_at\": \"2025-11-12 20:18:51\"\n}"}, {"name": "sub unit", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"department_id\":\"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"name\": \"pediatrics\",\n    \"parent_id\":\"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n    \"level\": 2\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sms.remmittance.com/org_units"}, "status": "Created", "code": 201, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:37:05 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "365"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"created_at\": \"2025-11-12 20:37:05\",\n    \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"department_name\": \"Admin\",\n    \"description\": null,\n    \"level\": 2,\n    \"manager_id\": null,\n    \"name\": \"pediatrics\",\n    \"org_unit_id\": \"14c23e47-f1bc-4802-bb9d-9749797a1ca4\",\n    \"parent_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n    \"updated_at\": \"2025-11-12 20:37:05\"\n}"}]}, {"name": "get all units", "request": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/org_units"}, "response": [{"name": "success", "originalRequest": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/org_units"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:44:55 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "749"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "[\n    {\n        \"created_at\": \"2025-11-12 20:18:51\",\n        \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n        \"department_name\": \"Admin\",\n        \"description\": null,\n        \"level\": 1,\n        \"manager_id\": null,\n        \"name\": \"pediatrics\",\n        \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n        \"parent_id\": null,\n        \"updated_at\": \"2025-11-12 20:18:51\"\n    },\n    {\n        \"created_at\": \"2025-11-12 20:37:05\",\n        \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n        \"department_name\": \"Admin\",\n        \"description\": null,\n        \"level\": 2,\n        \"manager_id\": null,\n        \"name\": \"pediatrics\",\n        \"org_unit_id\": \"14c23e47-f1bc-4802-bb9d-9749797a1ca4\",\n        \"parent_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n        \"updated_at\": \"2025-11-12 20:37:05\"\n    }\n]"}]}, {"name": "get unit by ID", "request": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/org_units/6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955"}, "response": [{"name": "success", "originalRequest": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/org_units/6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:50:42 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "787"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"children\": [\n        {\n            \"children\": [],\n            \"created_at\": \"2025-11-12 20:37:05\",\n            \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n            \"department_name\": \"Admin\",\n            \"description\": null,\n            \"level\": 2,\n            \"manager_id\": null,\n            \"name\": \"pediatrics\",\n            \"org_unit_id\": \"14c23e47-f1bc-4802-bb9d-9749797a1ca4\",\n            \"parent_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n            \"updated_at\": \"2025-11-12 20:37:05\"\n        }\n    ],\n    \"created_at\": \"2025-11-12 20:18:51\",\n    \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"department_name\": \"Admin\",\n    \"description\": null,\n    \"level\": 1,\n    \"manager_id\": null,\n    \"name\": \"pediatrics\",\n    \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n    \"parent_id\": null,\n    \"updated_at\": \"2025-11-12 20:18:51\"\n}"}]}, {"name": "get department units", "request": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/departments/0e05ae75-82eb-47c9-9444-1455d6f75274/org_units"}, "response": [{"name": "success", "originalRequest": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/departments/0e05ae75-82eb-47c9-9444-1455d6f75274/org_units"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:56:12 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "749"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "[\n    {\n        \"created_at\": \"2025-11-12 20:18:51\",\n        \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n        \"department_name\": \"Admin\",\n        \"description\": null,\n        \"level\": 1,\n        \"manager_id\": null,\n        \"name\": \"pediatrics\",\n        \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n        \"parent_id\": null,\n        \"updated_at\": \"2025-11-12 20:18:51\"\n    },\n    {\n        \"created_at\": \"2025-11-12 20:37:05\",\n        \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n        \"department_name\": \"Admin\",\n        \"description\": null,\n        \"level\": 2,\n        \"manager_id\": null,\n        \"name\": \"pediatrics\",\n        \"org_unit_id\": \"14c23e47-f1bc-4802-bb9d-9749797a1ca4\",\n        \"parent_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n        \"updated_at\": \"2025-11-12 20:37:05\"\n    }\n]"}]}, {"name": "update unit", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"description\": \"This is the unit for nurses.\",\n    \"name\": \"nursing\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sms.remmittance.com/org_units/6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955"}, "response": [{"name": "success", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\n    \"description\": \"This is the unit for nurses.\",\n    \"name\": \"nursing\"\n}", "options": {"raw": {"language": "json"}}}, "url": "https://sms.remmittance.com/org_units/6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 19:59:20 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "354"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"created_at\": \"2025-11-12 20:18:51\",\n    \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"department_name\": \"Admin\",\n    \"description\": \"This is the unit for nurses.\",\n    \"level\": 1,\n    \"manager_id\": null,\n    \"name\": \"nursing\",\n    \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\",\n    \"parent_id\": null,\n    \"updated_at\": \"2025-11-12 20:59:19\"\n}"}]}, {"name": "delete organisation unit", "request": {"method": "DELETE", "header": [], "url": "https://sms.remmittance.com/org_units/14c23e47-f1bc-4802-bb9d-9749797a1ca4"}, "response": [{"name": "success", "originalRequest": {"method": "DELETE", "header": [], "url": "https://sms.remmittance.com/org_units/14c23e47-f1bc-4802-bb9d-9749797a1ca4"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 20:01:16 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "49"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"message\": \"Org unit deleted successfully\"\n}"}]}, {"name": "get full hierachical structure", "request": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/departments/0e05ae75-82eb-47c9-9444-1455d6f75274/structure"}, "response": [{"name": "success", "originalRequest": {"method": "GET", "header": [], "url": "https://sms.remmittance.com/departments/0e05ae75-82eb-47c9-9444-1455d6f75274/structure"}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Server", "value": "nginx/1.22.1"}, {"key": "Date", "value": "Wed, 12 Nov 2025 20:04:23 GMT"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Content-Length", "value": "308"}, {"key": "Connection", "value": "keep-alive"}, {"key": "Access-Control-Allow-Origin", "value": "http://localhost:3000"}, {"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Vary", "value": "Origin"}], "cookie": [], "body": "{\n    \"department_id\": \"0e05ae75-82eb-47c9-9444-1455d6f75274\",\n    \"department_name\": \"Admin\",\n    \"units\": [\n        {\n            \"children\": [],\n            \"description\": \"This is the unit for nurses.\",\n            \"manager_id\": null,\n            \"name\": \"nursing\",\n            \"org_unit_id\": \"6ab1c4bf-fb6e-44f2-8ddd-5d9f4b212955\"\n        }\n    ]\n}"}]}]}]}